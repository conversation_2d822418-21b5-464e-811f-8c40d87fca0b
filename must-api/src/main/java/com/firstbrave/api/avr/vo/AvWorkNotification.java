package com.firstbrave.api.avr.vo;

import com.firstbrave.api.base.ApiContentType;
import com.firstbrave.api.base.AvrTransactionRecord;

/**
 * The AVR record contains information specific to an audiovisual work.
 * Detail records can be listed subsequent to the AVR record providing further information
 * on the audiovisual work and the musical works that are included in the audiovisual work.
 * The CUE record contains the title and work numbers for existing works and cues.
 * If the society or international work number (ISWC) is not available and it is necessary
 * to send additional information regarding the work, then the works information should be sent
 * using the work records, MWI, MIP, and MSR.
 *
 * <AUTHOR>
 * @date 2019/9/18
 */
public class AvWorkNotification extends AvrTransactionRecord {

    /**
     * Name/Title of the film or of the series - left justified.
     */
    @ApiContentType(index = 4, length = 60)
    private String avWorkTitle;

    /**
     * The unique number used internally by the society within its own systems to
     * identify the audiovisual work (episode or film) and as submitted to the AVIndex.
     */
    @ApiContentType(index = 5, length = 15)
    private String societyAvWorkNumber;

    /**
     * For series, this field contains the name of the episode - left justified.
     */
    @ApiContentType(index = 6, length = 60)
    private String episodeTitle;

    /**
     * Reserved.
     */
    @ApiContentType(index = 7, length = 43)
    private String filler1;

    /**
     * The language of this AV work. These values reside in the CIS Language Code Table.
     */
    @ApiContentType(index = 8, length = 2)
    private String cisLanguageCode;

    /**
     * The territory of this AV work. These values reside in the TIS Code Table.
     */
    @ApiContentType(index = 9, length = 4)
    private String tisNumericCode;

    /**
     * Indicate whether the AV work is a feature film or episode/series (F, S).
     */
    @ApiContentType(index = 10, length = 1)
    private String category;

    /**
     * The name or number assigned by the production company to identify different series of audiovisual works.
     */
    @ApiContentType(index = 11, length = 4)
    private String seriesIdentifier;

    /**
     * The number generated by the production company to identify the work.
     */
    @ApiContentType(index = 12, length = 12)
    private String productionNumber;

    /**
     * Number assigned to an episode by the producer.
     */
    @ApiContentType(index = 13, length = 20)
    private String episodeNumber;

    /**
     * Reserved.
     */
    @ApiContentType(index = 14, length = 1)
    private String filler2;

    /**
     * The year in which the production of the film or episode was completed.
     */
    @ApiContentType(index = 15, length = 4)
    private Integer yearOfProduction;

    /**
     * Duration of the AV Work or programme.
     */
    @ApiContentType(index = 16, length = 6)
    private String avWorkDuration;

    /**
     * The total duration of music played in the audiovisual work or programme, expressed in hours, minutes and seconds.
     */
    @ApiContentType(index = 17, length = 6)
    private String totalMusicDuration;

    /**
     * Date of first performance in originating territory. This is used for identification purposes.
     */
    @ApiContentType(index = 18, length = 8)
    private String airDate;

    /**
     * Type of AV work These values reside in the AV Type Table.
     */
    @ApiContentType(index = 19, length = 3)
    private String type;

    /**
     * Indication if AV work is an average, composite, summarized or surrogate AV work using the Cue Sheet Type Table.
     */
    @ApiContentType(index = 20, length = 1)
    private String cueSheetType;

    /**
     * The specific purpose for which the audiovisual work was created if any, or the process by which it comes about.
     * This information is necessary to identify the version of the AV work since a version of an audio visual work
     * can be defined based on the destination format (e.g. internet, cinema, television etc.).
     * The updated destination tables can be found in the document
     * “AVI09-0222_AVI_enchancing_version_AV_works_2009-03-12_EN.DOC”
     */
    @ApiContentType(index = 21, length = 3)
    private String destination;

    /**
     * Country of origin for the AV work. These values reside in the TIS Code Table.
     */
    @ApiContentType(index = 22, length = 4)
    private String countryOfOrigin;

    /**
     * The date when the most recent revision was completed.
     */
    @ApiContentType(index = 23, length = 8)
    private String dateOfRevision;

    /**
     * Indicates if this is a revision to an existing AV work.
     */
    @ApiContentType(index = 24, length = 1)
    private String codeOfRevision;

    /**
     * Reserved.
     */
    @ApiContentType(index = 25, length = 26)
    private String filler3;

    /**
     * The unique number used internally by the society within its own systems to identify
     * the series to which the episode belongs.
     */
    @ApiContentType(index = 26, length = 15)
    private String societySeriesNumber;

    /**
     * The specific nature of the AV content for which the audiovisual work was created, if any.
     * Along with the destination attributes (e.g. internet, cinema, television etc.) when applicable,
     * this information is necessary to identify the version of the AV work based on the content of
     * the AV work (e.g. international, Director’s cuts etc.). The relevant content version tables can be
     * found in the document “AVI09-0222_AVI_enchancing_version_AV_works_2009-03-12_EN.DOC”
     */
    @ApiContentType(index = 27, length = 3)
    private String contentVersion;


    public String getAvWorkTitle() {
        return avWorkTitle;
    }

    public void setAvWorkTitle(String avWorkTitle) {
        this.avWorkTitle = avWorkTitle;
    }

    public String getSocietyAvWorkNumber() {
        return societyAvWorkNumber;
    }

    public void setSocietyAvWorkNumber(String societyAvWorkNumber) {
        this.societyAvWorkNumber = societyAvWorkNumber;
    }

    public String getEpisodeTitle() {
        return episodeTitle;
    }

    public void setEpisodeTitle(String episodeTitle) {
        this.episodeTitle = episodeTitle;
    }

    public String getFiller1() {
        return filler1;
    }

    public void setFiller1(String filler1) {
        this.filler1 = filler1;
    }

    public String getCisLanguageCode() {
        return cisLanguageCode;
    }

    public void setCisLanguageCode(String cisLanguageCode) {
        this.cisLanguageCode = cisLanguageCode;
    }

    public String getTisNumericCode() {
        return tisNumericCode;
    }

    public void setTisNumericCode(String tisNumericCode) {
        this.tisNumericCode = tisNumericCode;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSeriesIdentifier() {
        return seriesIdentifier;
    }

    public void setSeriesIdentifier(String seriesIdentifier) {
        this.seriesIdentifier = seriesIdentifier;
    }

    public String getProductionNumber() {
        return productionNumber;
    }

    public void setProductionNumber(String productionNumber) {
        this.productionNumber = productionNumber;
    }

    public String getEpisodeNumber() {
        return episodeNumber;
    }

    public void setEpisodeNumber(String episodeNumber) {
        this.episodeNumber = episodeNumber;
    }

    public String getFiller2() {
        return filler2;
    }

    public void setFiller2(String filler2) {
        this.filler2 = filler2;
    }

    public Integer getYearOfProduction() {
        return yearOfProduction;
    }

    public void setYearOfProduction(Integer yearOfProduction) {
        this.yearOfProduction = yearOfProduction;
    }

    public String getAvWorkDuration() {
        return avWorkDuration;
    }

    public void setAvWorkDuration(String avWorkDuration) {
        this.avWorkDuration = avWorkDuration;
    }

    public String getTotalMusicDuration() {
        return totalMusicDuration;
    }

    public void setTotalMusicDuration(String totalMusicDuration) {
        this.totalMusicDuration = totalMusicDuration;
    }

    public String getAirDate() {
        return airDate;
    }

    public void setAirDate(String airDate) {
        this.airDate = airDate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCueSheetType() {
        return cueSheetType;
    }

    public void setCueSheetType(String cueSheetType) {
        this.cueSheetType = cueSheetType;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public String getCountryOfOrigin() {
        return countryOfOrigin;
    }

    public void setCountryOfOrigin(String countryOfOrigin) {
        this.countryOfOrigin = countryOfOrigin;
    }

    public String getDateOfRevision() {
        return dateOfRevision;
    }

    public void setDateOfRevision(String dateOfRevision) {
        this.dateOfRevision = dateOfRevision;
    }

    public String getCodeOfRevision() {
        return codeOfRevision;
    }

    public void setCodeOfRevision(String codeOfRevision) {
        this.codeOfRevision = codeOfRevision;
    }

    public String getFiller3() {
        return filler3;
    }

    public void setFiller3(String filler3) {
        this.filler3 = filler3;
    }

    public String getSocietySeriesNumber() {
        return societySeriesNumber;
    }

    public void setSocietySeriesNumber(String societySeriesNumber) {
        this.societySeriesNumber = societySeriesNumber;
    }

    public String getContentVersion() {
        return contentVersion;
    }

    public void setContentVersion(String contentVersion) {
        this.contentVersion = contentVersion;
    }
}
