package com.firstbrave.api.avr.vo;

import com.firstbrave.api.base.ApiContentType;
import com.firstbrave.api.base.AvrTransactionRecord;

/**
 * This record identifies the various global identifiers assigned to this AV work
 * by international databases or organizations e.g. IDA, ISAN and EIDR ...
 * for the purposes of identification. It is important to note that an AV Work may have more than
 * one distinct V-ISAN number and more than one distinct EIDR number.
 * The IDS record permits the data contributor to submit several EIDR or V-ISAN numbers for one AV Work.
 *
 * <AUTHOR>
 * @date 2019/9/18
 */
public class GlobalIdentifiers extends AvrTransactionRecord {

    /**
     * Actual value of the identifier being submitted.
     * The value provided must match an entry in the associated databases
     * as per field level validation description below.
     * Only the identifiers described in the AV Work Identifier Type Table
     * can be provided e.g. V-ISAN, EIDR or IDA...
     */
    @ApiContentType(index = 4, length = 60)
    private String identifierValue;

    /**
     * Describes the type of the provided identifier e.g.
     * V-ISAN, EIDR or IDA... Values for this field reside in the AV Work Identifier Type Table
     */
    @ApiContentType(index = 5, length = 8)
    private String identifierType;

    public String getIdentifierValue() {
        return identifierValue;
    }

    public void setIdentifierValue(String identifierValue) {
        this.identifierValue = identifierValue;
    }

    public String getIdentifierType() {
        return identifierType;
    }

    public void setIdentifierType(String identifierType) {
        this.identifierType = identifierType;
    }
}
