package com.firstbrave.api.base;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/10/21
 */
@Data
public class CrdTransactionRecord extends CrdRecord {

    /**
     * If this is the first transaction within a group,
     * the Transaction Sequence # must be equal to 00000000.
     * Otherwise, for transaction headers, the Transaction Sequence #
     * must be equal to the previous transaction header’s Transaction Sequence # incremented by 1.
     * For detail records, the Transaction Sequence #
     * must be equal to the Transaction Sequence # of the previous transaction header.
     */
    @ApiContentType(index = 2, length = 8)
    protected Integer transactionSequenceNo;

    /**
     * For transaction headers, always set to 00000000.
     * For detail records, set this field to the AvrRecord Sequence # of the previous record
     * written to the file incremented by 1.
     */
    @ApiContentType(index = 3, length = 8)
    protected Integer recordSequenceNo;
}
