package com.firstbrave.api.base;

/**
 * <AUTHOR>
 * @date 2019/9/19
 */
public class E4Record {

    /**
     * The Remitting Society is identified by the encode number that CISAC  has assigned to societies.
     */
    @ApiContentType(index = 1, length = 3)
    protected Integer codeOfRemittingSociety;

    /**
     * The receiving society is identified by the encode number that CISAC has assigned to societies.
     */
    @ApiContentType(index = 2, length = 3)
    protected Integer codeOfReceivingSociety;

    /**
     * This will be set to zero to indicate that the record is the file header.
     */
    @ApiContentType(index = 3, length = 1)
    protected Integer recordType;

    public Integer getCodeOfRemittingSociety() {
        return codeOfRemittingSociety;
    }

    public void setCodeOfRemittingSociety(Integer codeOfRemittingSociety) {
        this.codeOfRemittingSociety = codeOfRemittingSociety;
    }

    public Integer getCodeOfReceivingSociety() {
        return codeOfReceivingSociety;
    }

    public void setCodeOfReceivingSociety(Integer codeOfReceivingSociety) {
        this.codeOfReceivingSociety = codeOfReceivingSociety;
    }

    public Integer getRecordType() {
        return recordType;
    }

    public void setRecordType(Integer recordType) {
        this.recordType = recordType;
    }
}
