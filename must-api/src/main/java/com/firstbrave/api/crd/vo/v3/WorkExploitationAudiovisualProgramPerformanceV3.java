package com.firstbrave.api.crd.vo.v3;

import com.firstbrave.api.base.ApiContentType;
import com.firstbrave.api.base.CrdTransactionRecord;
import lombok.Data;

/**
 * WEA: Work Exploitation, Audiovisual Program Performance Detail
 * AvrRecord
 * This record is used to convey information relevant to an exploitation of a work by performance, where the
 * exploitation resulted from its use in an audiovisual production.
 */
@Data
public class WorkExploitationAudiovisualProgramPerformanceV3 extends CrdTransactionRecord {
    /**
     * Unique identifier of this record. C
     */
    @ApiContentType(index=4,length = 15)
    private String referenceIdentifier;

    /**
     * Source of use generating royalties (e.g. radio, TV, cinema,  sound carriers)
     */
    @ApiContentType(index=5,length = 2)
    private Integer distributionCategory;

    /**
     * Unique identifier assigned to the source by the remitting  society (internal identifier)
     */
    @ApiContentType(index=6,length = 20)
    private String exploitationSourceIdentifier;

    /**
     * Number of performances/usages
     */
    @ApiContentType(index=7,length = 8)
    private Integer quantity;

    /**
     * Code describing the period covered by this record
     */
    @ApiContentType(index=8,length = 1)
    private String aggregationPeriodType;

    /**
     * Date of performance for itemised list reporting, or the  start date of an aggregation period
     */
    @ApiContentType(index=9,length = 8)
    private String exploitationDate;

    /**
     * Time of performance
     */
    @ApiContentType(index=10,length = 4)
    private String exploitationTime;

    /**
     * Unique identifier assigned to the audiovisual work by the  remitting society
     */
    @ApiContentType(index=11,length = 15)
    private String societyAvWorkIdentifier;

    /**
     * (Broadcast) Day of week grouping
     */
    @ApiContentType(index=12,length = 1)
    private Integer dayOfWeekCode;

    /**
     * Code showing the origin of the work (cue) within this AV  production
     */
    @ApiContentType(index=13,length = 1)
    private String origin;

    /**
     * Sequence of this cue (musical work) within the program's  cue sheet
     */
    @ApiContentType(index=14,length = 3)
    private Integer cueSequence;

    /**
     * Duration of this musical work usage in the AV production  in hours, minutes and seconds.
     */
    @ApiContentType(index=15,length = 6)
    private String duration;

    /**
     * Catalogue number assigned to the product by the  manufacturer if the work was cued from a recording
     */
    @ApiContentType(index=16,length = 18)
    private String productCatalogueNumber;

    /**
     * Type of performance reporting
     */
    @ApiContentType(index=17,length = 1)
    private String surveyType;

    /**
     * Code representing the basis upon which the performance  credit is calculated
     */
    @ApiContentType(index=18,length = 1)
    private String basisOfRateCalculation;

    /**
     * Optional identifier to allow societies to group earnings  (e.g. by publishing sub-catalogue agreement)
     */
    @ApiContentType(index=19,length = 11)
    private String nationalGroupingIdentifier;

    /**
     * Code of the currency of royalty, tax and commission  amounts
     */
    @ApiContentType(index=20,length = 3)
    private String currency;

    /**
     * Amount of available royalties before deduction of any  commissions and/or taxes
     */
    @ApiContentType(index=21,length = 18)
    private Long grossRoyaltyAmount;

    /**
     * Percentage of royalty deducted for legislated taxation  payments; may be zero
     */
    @ApiContentType(index=22,length = 8)
    private Integer taxRate;

    /**
     * Amount of tax withheld by the remitting society; zero if  not applicable
     */
    @ApiContentType(index=23,length = 18)
    private Long taxAmount;

    /**
     * Amount of commission deducted by the remitting  society; zero if not applicable
     */
    @ApiContentType(index=24,length = 18)
    private Long commissionAmount;

    /**
     * CISAC encode of the society that provided these royalties to  the remitting society (for foreign income or central  licensing); zero if not applicable
     */
    @ApiContentType(index=25,length = 3)
    private Integer sourceSociety;

    /**
     * Amount of tax withheld by the source society; zero if not  applicable
     */
    @ApiContentType(index=26,length = 18)
    private Long sourceSocietyTaxAmount;

    /**
     * Amount of commission deducted by the source collection  society prior to sending these royalties to the remitting  society; zero if not applicable
     */
    @ApiContentType(index=27,length = 18)
    private Long sourceSocietyCommissionAmount;

    /**
     * The net amount of distributed royalties for the sharer  from this exploitation in the currency described in SDN-14
     */
    @ApiContentType(index=28,length = 18)
    private Long remittedRoyaltyAmount;

    /**
     * Unique identifier assigned to the recorded product by the
     * remitting society. May be identical to either the EAN/UPC
     * code or manufacturer’s catalogue number
     */
    @ApiContentType(index=29,length = 20)
    private String recordedProductNo;

    /**
     * Percentage of royalty retained as commission by the
     * remitting society; zero if not applicable
     */
    @ApiContentType(index=30,length = 8)
    private String commissionRate;

    /**
     * The preceding MDR sequence number within the same
     * transaction number (MWN) to which this payment relates
     */
    @ApiContentType(index=31,length = 8)
    private String mdrSeqNo;

    /**
     * The preceding MDT sequence number within the same
     * transaction number (MWN) to which this payment relates
     */
    @ApiContentType(index=32,length = 8)
    private String mdtSeqNo;
}
