package com.firstbrave.api.ipi.base;

import com.firstbrave.api.base.ApiContentType;

public class Record {
    /**
     * The three character transaction type or detail record type. These values reside in the AvrRecord Type Table.
     */
    @ApiContentType(index=1,length = 3)
    protected String recordType;

  /*  public AvrRecord(String content) {
        this.recordType = content.substring(0,3);
    }*/

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }
}
