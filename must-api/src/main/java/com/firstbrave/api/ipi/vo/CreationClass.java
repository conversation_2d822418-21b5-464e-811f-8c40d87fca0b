package com.firstbrave.api.ipi.vo;

import com.firstbrave.api.ipi.enumerate.CreationClassEnum;
import com.firstbrave.api.ipi.base.RecordPrefix;
import com.firstbrave.api.base.ApiContentType;

/**
 * CCL: Creation class<br/>
 * A creation class is a class of products of human imagination and/or endeavour.<br/>
 * This entity contains the creation class codes used in the IPI system.<br/>
 * A creation class may contain or point to none, one or more creation subclasses.<br/>
 * A creation subclass points to exactly one creation class.<br/>
 * It is maintained by the IPI centre (SUISA).<br/>
 * It will be EDI distributed (downloaded files only) when needed. See CCL detail record of the IDA
 * transaction in the EDI document.
 */
public class CreationClass extends RecordPrefix {
    @ApiContentType(index=4,length = 2)
	private String creationClassCode;
    @ApiContentType(index=5,length = 60)
	private String creationClassDesc;
    @ApiContentType(index=6,length = 2)
	private String languageCode;

  /*  public CreationClass(String content) {
        super(content);

        setCreationClassCode(content.substring(19,21));
        setCreationClassDesc(content.substring(21,81));
        setLanguageCode(content.substring(81,83));
    }*/

    public String getCreationClassCode() {
        return creationClassCode;
    }

    public void setCreationClassCode(String creationClassCode) {
        this.creationClassCode = CreationClassEnum.valueOf(creationClassCode).toString();
    }

    public String getCreationClassDesc() {
        return creationClassDesc;
    }

    public void setCreationClassDesc(String creationClassDesc) {
        this.creationClassDesc = creationClassDesc;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    @Override
    public String toString() {
        return "CreationClass{" +
                "creationClassCode='" + creationClassCode + '\'' +
                ", creationClassDesc='" + creationClassDesc + '\'' +
                ", languageCode='" + languageCode + '\'' +
                ", transactionSequenceNo='" + transactionSequenceNo + '\'' +
                ", recordSequenceNo='" + recordSequenceNo + '\'' +
                ", recordType='" + recordType +
                "} ";
    }
}
