package com.firstbrave.api.p.vo;

import com.firstbrave.api.base.ApiContentType;
import com.firstbrave.api.base.PRecord;

/**
 * @description:
 * @author: handa
 * @time: 2019/10/30 18:03
 */
public class PHeader extends PRecord {

    /**
     *(Four alphanumeric positions for each of the ten sub-fields).
     * The identification code for each distribution contained in the diskette
     */
    @ApiContentType(index = 7, length = 40)
    private String distributionNumbers;

    /**
     *
     */
    @ApiContentType(index = 8, length = 20)
    private String adjustmentNumbers;

    /**
     *
     */
    @ApiContentType(index = 9, length = 128)
    private String 	sparePositions;

    /**
     *
     */
    @ApiContentType(index = 10, length = 1)
    private Integer recordType;

    /**
     *
     */
    @ApiContentType(index = 11, length = 2)
    private String formatOfDisketteUsed;

    @ApiContentType(index = 12, length = 1)
    private Integer numbersOfDecimalPlacesInAmountFields;

    @ApiContentType(index = 13, length = 9)
    private String CodeOfReceivingCAE;

    @ApiContentType(index = 14, length = 80)
    private String nameOfReceivingCAE;

    @ApiContentType(index = 15, length = 16)
    private String sparePositions2;

    public String getDistributionNumbers() {
        return distributionNumbers;
    }

    public void setDistributionNumbers(String distributionNumbers) {
        this.distributionNumbers = distributionNumbers;
    }

    public String getAdjustmentNumbers() {
        return adjustmentNumbers;
    }

    public void setAdjustmentNumbers(String adjustmentNumbers) {
        this.adjustmentNumbers = adjustmentNumbers;
    }

    public String getSparePositions() {
        return sparePositions;
    }

    public void setSparePositions(String sparePositions) {
        this.sparePositions = sparePositions;
    }

    public Integer getRecordType() {
        return recordType;
    }

    public void setRecordType(Integer recordType) {
        this.recordType = recordType;
    }

    public String getFormatOfDisketteUsed() {
        return formatOfDisketteUsed;
    }

    public void setFormatOfDisketteUsed(String formatOfDisketteUsed) {
        this.formatOfDisketteUsed = formatOfDisketteUsed;
    }

    public Integer getNumbersOfDecimalPlacesInAmountFields() {
        return numbersOfDecimalPlacesInAmountFields;
    }

    public void setNumbersOfDecimalPlacesInAmountFields(Integer numbersOfDecimalPlacesInAmountFields) {
        this.numbersOfDecimalPlacesInAmountFields = numbersOfDecimalPlacesInAmountFields;
    }

    public String getCodeOfReceivingCAE() {
        return CodeOfReceivingCAE;
    }

    public void setCodeOfReceivingCAE(String codeOfReceivingCAE) {
        CodeOfReceivingCAE = codeOfReceivingCAE;
    }

    public String getNameOfReceivingCAE() {
        return nameOfReceivingCAE;
    }

    public void setNameOfReceivingCAE(String nameOfReceivingCAE) {
        this.nameOfReceivingCAE = nameOfReceivingCAE;
    }

    public String getSparePositions2() {
        return sparePositions2;
    }

    public void setSparePositions2(String sparePositions2) {
        this.sparePositions2 = sparePositions2;
    }

    @Override
    public String toString() {
        return "PHeader{" +
                "distributionNumbers='" + distributionNumbers + '\'' +
                ", adjustmentNumbers='" + adjustmentNumbers + '\'' +
                ", sparePositions='" + sparePositions + '\'' +
                ", recordType=" + recordType +
                ", formatOfDisketteUsed='" + formatOfDisketteUsed + '\'' +
                ", numbersOfDecimalPlacesInAmountFields=" + numbersOfDecimalPlacesInAmountFields +
                ", CodeOfReceivingCAE='" + CodeOfReceivingCAE + '\'' +
                ", nameOfReceivingCAE='" + nameOfReceivingCAE + '\'' +
                ", sparePositions2='" + sparePositions2 + '\'' +
                ", codeOfRemittingSociety=" + codeOfRemittingSociety +
                ", identificationOfTheDistribution='" + identificationOfTheDistribution + '\'' +
                ", codeOfTerritoryOfPerformance=" + codeOfTerritoryOfPerformance +
                ", codeOfReceivingSociety=" + codeOfReceivingSociety +
                ", codeOfTypeOfTitle=" + codeOfTypeOfTitle +
                ", nameOfComposer='" + nameOfComposer + '\'' +
                '}';
    }
}
