package com.firstbrave.api.parser;

import com.firstbrave.api.base.ApiContentType;
import com.firstbrave.api.ccid.base.CCIDRecordPrefix;
import com.firstbrave.api.ccid.base.spotify.CCIDRecordPrefixSpotify;
import com.firstbrave.api.ccidv14.spotify.vo.InvoiceDetailRecordV14;
import com.firstbrave.api.ipi.base.Record;

import java.lang.reflect.Field;
import java.util.TreeMap;
import java.util.function.Predicate;


public class CCIDFileEncoder {

	  private Predicate<Class> filter = clazz ->{
	        if(null == clazz) return false;
	        String superclassName = clazz.getSimpleName();

	        if(superclassName.equals(Record.class.getSimpleName())
	                || superclassName.equals(CCIDRecordPrefix.class.getSimpleName())) return true;
	        return false;
	    };

	    /**
	     * 获取class类包含的字段
	     * @param baseClass
	     * @param clazz
	     * @return
	     * @throws IllegalAccessException
	     */
	    private TreeMap<Integer, Tuple<Integer, Object>> getFields(CCIDRecordPrefix baseClass, Class<?> clazz) throws IllegalAccessException {
	        Field[] declaredFields = clazz.getDeclaredFields();
	        TreeMap<Integer, Tuple<Integer,Object>> map = new TreeMap<>();

	        if(filter.test(clazz.getSuperclass())){
	            map.putAll(getFields(baseClass,clazz.getSuperclass()));
	        }
	        for (Field declaredField : declaredFields) {
	            ApiContentType annotation = declaredField.getAnnotation(ApiContentType.class);
	            if(null != annotation){
	                declaredField.setAccessible(true);
	                map.put(annotation.index(), new Tuple(annotation.length(),declaredField.get(baseClass)));
	            }
	        }
	        return map;
	    }

	    public String code(CCIDRecordPrefix record) throws IllegalAccessException {
	        StringBuilder sb = new StringBuilder();
	        Class<?> clazz = record.getClass();

	        TreeMap<Integer, Tuple<Integer,Object>> map = getFields(record, clazz);
	        map.forEach((k,v) ->{
	            if(v.y instanceof Integer || v.y instanceof Long ){
	            	if(v.y==null) {
	            		sb.append("0");
	            	}else {
	            		sb.append(v.y.toString());
	            	}
	            	sb.append("|");
	                
	            }else{
	            	if(v.y==null) {
	            		sb.append("");
	            	}else {
	            		sb.append(v.y.toString().replace("|", ","));
	            	}
	            	
	         
	            }
	            sb.append("|");

	        });
	        
	        if (sb.length() > 1) {
	        	//删除最后一个竖线
				sb.deleteCharAt(sb.length() -1);
			}

	        return sb.toString();
	    }

	public String codeByTab(CCIDRecordPrefix record) throws IllegalAccessException {
		StringBuilder sb = new StringBuilder();
		Class<?> clazz = record.getClass();

		TreeMap<Integer, Tuple<Integer,Object>> map = getFields(record, clazz);
		map.forEach((k,v) ->{
			if(v.y instanceof Integer || v.y instanceof Long ){
				if(v.y==null) {
					sb.append("0");
				}else {
					sb.append(v.y.toString());
				}
				sb.append("\t");

			}else{
				if(v.y==null) {
					sb.append("");
				}else {
					sb.append(v.y.toString());
				}


			}
			sb.append("\t");
		});

		if (sb.length() > 1) {
			//删除最后一个竖线
			sb.deleteCharAt(sb.length() -1);
		}

		return sb.toString();
	}

	public String codeByTab(CCIDRecordPrefixSpotify record) throws IllegalAccessException {
		StringBuilder sb = new StringBuilder();
		Class<?> clazz = record.getClass();

		TreeMap<Integer, Tuple<Integer,Object>> map = getFields(record, clazz);
		map.forEach((k,v) ->{
			if(v.y instanceof Integer || v.y instanceof Long ){
				if(v.y==null) {
					sb.append("0");
				}else {
					sb.append(v.y.toString());
				}
				sb.append("\t");

			}else{
				if(v.y==null) {
					sb.append("");
				}else {
					sb.append(v.y.toString());
				}


			}
			sb.append("\t");
		});

		if (sb.length() > 1) {
			//删除最后一个竖线
			sb.deleteCharAt(sb.length() -1);
		}

		return sb.toString();
	}

	/**
	 * 获取class类包含的字段
	 * @param baseClass
	 * @param clazz
	 * @return
	 * @throws IllegalAccessException
	 */
	private TreeMap<Integer, Tuple<Integer, Object>> getFields(CCIDRecordPrefixSpotify baseClass, Class<?> clazz) throws IllegalAccessException {
		Field[] declaredFields = clazz.getDeclaredFields();
		TreeMap<Integer, Tuple<Integer,Object>> map = new TreeMap<>();

		if(filter.test(clazz.getSuperclass())){
			map.putAll(getFields(baseClass,clazz.getSuperclass()));
		}
		for (Field declaredField : declaredFields) {
			ApiContentType annotation = declaredField.getAnnotation(ApiContentType.class);
			if(null != annotation){
				declaredField.setAccessible(true);
				map.put(annotation.index(), new Tuple(annotation.length(),declaredField.get(baseClass)));
			}
		}
		return map;
	}

}
