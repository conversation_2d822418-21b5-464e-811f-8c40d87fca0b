package com.firstbrave.api.parser;


import com.firstbrave.api.base.CrdRecord;
import com.firstbrave.api.crd.vo.*;
import com.firstbrave.api.crd.vo.menu.VerisonTypeEnum;
import com.firstbrave.api.crd.vo.v3.*;
import com.firstbrave.api.util.ParserUtil;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2019/10/21
 */
public class CrdFileDecoder {

    /**
     * MAP中存放 RECORD 信息对应的实体类
     */
    public static final Map<String, Class> MAP = new HashMap<>();

    public static final Map<String, Class> MAPV3 = new HashMap<>();

    static {
        MAP.put("HDR", TransmissionHeader.class);
        MAP.put("GRH", GroupHeader.class);
        MAP.put("GRT", GroupTrailer.class);
        MAP.put("TRL", TransmissionTrailer.class);

        MAP.put("SDN", SocietyDistributionNotificationTransactionHeader.class);
        MAP.put("CCR", CurrencyConversionRate.class);
        MAP.put("TDI", TerritorySpecificDistributionInformation.class);
        MAP.put("ESI", ExploitationSourceInformation.class);
        MAP.put("IPI", InterestedPartyInformation.class);
        MAP.put("API", AudioVisualProgramInformation.class);
        MAP.put("RPI", RecordedProductInformation.class);

        MAP.put("MWN", MusicalWorkNotificationTransactionHeader.class);
        MAP.put("OIP", OtherInterestedParty.class);
        MAP.put("MDS", MusicalWorkDistributionStatus.class);
        MAP.put("MDR", MusicalWorkDistributionStatusRight.class);
        MAP.put("MDT", MusicalWorkDistributionStatusTerritory.class);
        MAP.put("MIP", MusicalWorkInterestedParty.class);
        MAP.put("ICC", InterestedPartyCategorizedContingencyPayments.class);
        MAP.put("ADJ", Adjustment.class);
        MAP.put("RRP", ReturnedRoyaltyPayment.class);
        MAP.put("WEP", WorkExploitationPerformance.class);
        MAP.put("WEA", WorkExploitationAudiovisualProgramPerformance.class);
        MAP.put("WER", WorkExploitationRecording.class);
        MAP.put("WBI", WorkExploitationPerformanceBonusIndications.class);
        MAP.put("WUI", WorkExploitationUnidentifiedPerformanceInformation.class);
        MAP.put("FEO", FeesInErrorNotificationToOriginalSociet.class);

        MAP.put("SIN", SocietySpecificInformationNotificationTransactionHeader.class);
        MAP.put("SID", SocietySpecificInformation.class);

        MAP.put("RGT", RoyaltyGrandTotalsTransactionHeader.class);
        MAP.put("AHP", AdHocPayment.class);

        MAP.put("ACK", AcknowledgementTransactionHeader.class);
        MAP.put("SPW", SuccessfulProcessWork.class);
        MAP.put("UPA", UnsuccessfulProcessAdvice.class);
    }
    static {
        MAPV3.put("HDR", TransmissionHeader.class);
        MAPV3.put("GRH", GroupHeader.class);
        MAPV3.put("GRT", GroupTrailer.class);
        MAPV3.put("TRL", TransmissionTrailer.class);

        MAPV3.put("SDN", SocietyDistributionNotificationTransactionHeader.class);
        MAPV3.put("CCR", CurrencyConversionRate.class);
        MAPV3.put("TDI", TerritorySpecificDistributionInformation.class);
        MAPV3.put("ESI", ExploitationSourceInformation.class);
        MAPV3.put("IPI", InterestedPartyInformation.class);
        MAPV3.put("API", AudioVisualProgramInformationV3.class);
        MAPV3.put("RPI", RecordedProductInformationV3.class);

        MAPV3.put("MWN", MusicalWorkNotificationTransactionHeader.class);
        MAPV3.put("OIP", OtherInterestedParty.class);
        MAPV3.put("MDS", MusicalWorkDistributionStatus.class);
        MAPV3.put("MDR", MusicalWorkDistributionStatusRight.class);
        MAPV3.put("MDT", MusicalWorkDistributionStatusTerritory.class);
        MAPV3.put("MIP", MusicalWorkInterestedParty.class);
        MAPV3.put("ICC", InterestedPartyCategorizedContingencyPaymentsV3.class);
        MAPV3.put("ADJ", AdjustmentV3.class);
        MAPV3.put("RRP", ReturnedRoyaltyPayment.class);
        MAPV3.put("WEP", WorkExploitationPerformanceV3.class);
        MAPV3.put("WEA", WorkExploitationAudiovisualProgramPerformanceV3.class);
        MAPV3.put("WER", WorkExploitationRecordingV3.class);
        MAPV3.put("WBI", WorkExploitationPerformanceBonusIndicationsV3.class);
        MAPV3.put("WUI", WorkExploitationUnidentifiedPerformanceInformation.class);
        MAPV3.put("FEO", FeesInErrorNotificationToOriginalSocietV3.class);

        MAPV3.put("AHP", AdHocPaymentV3.class);
        MAPV3.put("SIN", SocietySpecificInformationNotificationTransactionHeader.class);
        MAPV3.put("SID", SocietySpecificInformation.class);

        MAPV3.put("RGT", RoyaltyGrandTotalsTransactionHeaderV3.class);

        MAPV3.put("ACK", AcknowledgementTransactionHeader.class);
        MAPV3.put("SPW", SuccessfulProcessWork.class);
        MAPV3.put("UPA", UnsuccessfulProcessAdvice.class);
    }

    /**
     * CRD文件解码
     *
     * @param lines 读取记录行链表
     * @return records 行记录封装对象集合
     */
    public static List<CrdRecord> decode(StringBuffer errorMsg,LinkedList<String> lines) throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        AtomicInteger currentLine = new AtomicInteger(1);
        List<CrdRecord> records = new ArrayList<>();
        while (!lines.isEmpty()) {
            String content = lines.pollFirst();
            if (content != null) {
                CrdRecord r = (CrdRecord) decode(currentLine,errorMsg,content);
                if (Objects.nonNull(r)){
                    r.setLine(currentLine.get());
                    records.add(r);
                }
            }
            currentLine.incrementAndGet();
        }
        return records;
    }

    /**
     * CRD文件解码
     *
     * @param lines 读取记录行链表
     * @return records 行记录封装对象集合
     */
    public static List<CrdRecord> decode(StringBuffer errorMsg,LinkedList<String> lines,String fileType) throws InvocationTargetException, NoSuchMethodException, InstantiationException, IllegalAccessException {
        AtomicInteger currentLine = new AtomicInteger(1);
        List<CrdRecord> records = new ArrayList<>();
        while (!lines.isEmpty()) {
            String content = lines.pollFirst();
            if (content != null) {
                CrdRecord r = (CrdRecord) decode(currentLine,errorMsg,content,fileType);
                if (Objects.nonNull(r)){
                    r.setLine(currentLine.get());
                    records.add(r);
                }
            }
            currentLine.incrementAndGet();
        }
        return records;
    }

    private static Object decode(AtomicInteger currentLine,StringBuffer errorMsg,String content) {
        String recordType = content.substring(0, 3);
        Class clazz = MAP.get(recordType);
        if(clazz == null){
            return null;
//            errorMsg.append("解析失败--第".concat(String.valueOf(currentLine.get())).concat("行数据无法识别recordType类型！")).append("\r\n");
        }
        try{
            return ParserUtil.decodeByChar(clazz, content);
        }catch (Exception e){
            errorMsg.append(recordType+"类型数据解析失败--第".concat(String.valueOf(currentLine.get())).concat("行数据解析错误"));
            errorMsg.append("=========").append("错误详情：").append(e.toString()).append("！").append("\r\n");
            e.printStackTrace();
        }
        return null;
    }

    private static Object decode(AtomicInteger currentLine,StringBuffer errorMsg,String content,String fileType) {
        String recordType = content.substring(0, 3);
        Class clazz ;
        if(fileType.equals(VerisonTypeEnum.VERISON_TYPE_ENUM_30.getCode())){
            clazz = MAPV3.get(recordType);
        } else {
            clazz = MAP.get(recordType);
        }
        if(clazz == null){
            return null;
//            errorMsg.append("解析失败--第".concat(String.valueOf(currentLine.get())).concat("行数据无法识别recordType类型！")).append("\r\n");
        }
        try{
            return ParserUtil.decodeByChar(clazz, content);
        }catch (Exception e){
            errorMsg.append(recordType+"类型数据解析失败--第".concat(String.valueOf(currentLine.get())).concat("行数据解析错误"));
            errorMsg.append("=========").append("错误详情：").append(e.toString()).append("！").append("\r\n");
            e.printStackTrace();
        }
        return null;
    }
}
