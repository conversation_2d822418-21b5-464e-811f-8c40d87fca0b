import com.firstbrave.api.base.PRecord;
import com.firstbrave.api.p.vo.PHeader;
import com.firstbrave.api.p.vo.PRoyaltyDistribution;
import com.firstbrave.api.p.vo.PTrailer;
import com.firstbrave.api.parser.PFileDecoder;
import com.firstbrave.api.util.ParserUtil;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.LinkedList;
import java.util.List;

/**
 * @description:
 * @author: handa
 * @time: 2019/10/31 10:08
 */
public class TestPFileDecoder {

    public static void main(String[] args) throws IOException, NoSuchMethodException, InstantiationException, IllegalAccessException, InvocationTargetException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\交接文档\\P档\\P档\\七好實業社_I0039421253_I172.P";
        List<String> filelines = Files.readAllLines(Paths.get(filePath));
        LinkedList<String> lines = new LinkedList<>(filelines);
        List<PRecord> list = PFileDecoder.parser(lines);
        list.forEach(pRecord -> {
            try {
                if(pRecord instanceof PHeader){
                    System.out.println(ParserUtil.encodeByChar(pRecord));
                }
                if (pRecord instanceof PRoyaltyDistribution){
                    System.out.println(ParserUtil.encodeByChar(pRecord));
                }
                if (pRecord instanceof PTrailer){
                    System.out.println(ParserUtil.encodeByChar(pRecord));
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        });

        //test object of PRrcoder is encodered to lines
     /*   List<String> encoderLines = PFileEncoder.encoder(list);
        for (String line : encoderLines){
            System.out.println(line);
        }*/

     //other test
        lines.add(1,"22");
    }
}
