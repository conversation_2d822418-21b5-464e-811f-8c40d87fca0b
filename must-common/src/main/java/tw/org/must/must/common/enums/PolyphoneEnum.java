package tw.org.must.must.common.enums;

import java.util.Properties;

/**
 * @ClassName: PolyphoneEnum
 * @Description: TODO
 * @Author: handa
 * @Date: 2020/5/7 9:53
 */

public enum PolyphoneEnum {
    OU("區", "区", "OU"),
    MIAO("繆", "缪", "MIAO"),
    CHEN("晟", "晟", "CHENG"),
    YUE("樂", "乐", "YUE"),
    YUN("員", "员", "YUN"),
    YUN1("贠", "贠", "YUN"),
    HE("黑", "黑", "HE"),
    CHON("重", "重", "CHONG"),
    QIU("仇", "仇", "QIU"),
    BI("秘", "秘", "BI"),
    XIAN("冼", "冼", "XIAN"),
    XIE("解", "解", "XIE"),
    SHE("折", "折", "SHE"),
    SHAN("單", "单", "SHAN"),
    PIAO("樸", "朴", "PIAO"),
    ZHA("翟", "翟", "ZHA"),
    ZHA1("查", "查", "ZHA"),
    GE("蓋", "盖", "GE"),
    MO_QI("萬俟", "万俟", "MO QI"),
    YU_CHI("尉遲", "尉迟", "YU CHI"),
    QIN("覃", "覃", "QIN"),
    FENG("馮", "冯", "FENG"),
    XIA("夏", "夏", "XIA"),
    QU("瞿", "瞿", "QU"),
    ZENG("曾", "曾", "ZENG"),
    SHI("石", "石", "SHI"),
    NA("那", "那", "NA"),
    ZANG("藏", "藏", "ZANG"),
    CHU("褚", "褚", "CHU"),
    ZHA2("紮", "扎", "ZHA"),
    JING("景", "景", "JING"),
    ZHAI("翟", "翟", "ZHAI"),
    DU("都", "都", "DU"),
    LU("六", "六", "LU"),
    BO("薄", "薄", "BO"),
    XUN("郇", "郇", "XUN"),
    CHAO("晁", "晁", "CHAO"),
    BEN("賁", "贲", "BEN"),
    JIA("賈", "贾", "JIA"),
    DE("的", "的", "DE"),
    HA("哈", "哈", "HA"),
    JU("居", "居", "JU"),
    WEI("尾", "尾", "WEI"),
    GAI("蓋", "盖", "GAI"),
    SHENG("盛", "盛", "SHENG"),
    TA("塔", "塔", "TA"),
    HE1("和", "和", "HE"),
    BAI("柏", "柏", "BAI"),
    LAN("藍", "蓝", "LAN"),
    MU("牟", "牟", "MU"),
    YIN("殷", "殷", "YIN"),
    GU("谷", "谷", "GU"),
    QIAN("乾", "乾", "QIAN"),
    LU1("陸", "陆", "LU"),
    NIE("乜", "乜", "NIE"),
    LE("樂", "乐", "LE"),
    TAO("陶", "陶", "TAO"),
    KAN("闞", "阚", "KAN"),
    YE("葉", "叶", "YE"),
    QIANG("強", "强", "QIANG"),
    BU("不", "不", "BU"),
    DING("丁", "丁", "DING"),
    A("阿", "阿", "A"),
    TANG("湯", "汤", "TANG"),
    WAN("萬", "万", "WAN"),
    CHE("車", "车", "CHE"),
    CHEN1("稱", "称", "CHEN"),
    SHEN("沈", "沉", "SHEN"),
    OU1("區", "区", "OU"),
    QIU1("仇", "仇", "QIU"),
    SU("宿", "宿", "SU"),
    NAN("南", "南", "NAN"),
    SHAN1("單", "单", "SHAN"),
    BU1("蔔", "卜", "BU"),
    NIAO("鳥", "鸟", "NIAO"),
    SI("思", "思", "SI"),
    XUN1("尋", "寻", "XUN"),
    YU("於", "于", "YU"),
    YAN("煙", "烟", "YAN"),
    YU1("余", "余", "YU"),
    QIAN1("淺", "浅", "QIAN"),
    AI("艾", "艾", "AI"),
    WAN1("浣", "浣", "WAN"),
    WU("無", "无", "WU"),
    XIN("信", "信", "XIN"),
    XU("許", "许", "XU"),
    QI("齊", "齐", "QI"),
    YU2("俞", "俞", "YU"),
    RUO("若", "若", "RUO");

    public String numName;
    public String sumName;
    public String value;

    public String getNumName() {
        return numName;
    }

    public String getSumName() {
        return sumName;
    }

    public String getValue() {
        return value;
    }

    PolyphoneEnum(String numName, String sumName, String value) {
        this.numName = numName;
        this.sumName = sumName;
        this.value = value;
    }

    public static final Properties PROPERTIES = new Properties();

    static {
        PolyphoneEnum[] types = PolyphoneEnum.values();
        for (PolyphoneEnum type : types) {
            PROPERTIES.put(type.getNumName(), type.getValue());
            PROPERTIES.put(type.getSumName(), type.getValue());
        }
    }
}
