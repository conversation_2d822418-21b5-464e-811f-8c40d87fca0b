package tw.org.must.must.common.util.excel;


import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GenerateMuiltSheetExcelUtil<T> {
    protected SXSSFWorkbook workbook = null;
    CellStyle contentStyle;
    CellStyle titleStyle;





    public GenerateMuiltSheetExcelUtil(String filePath,Class<T> classz){

        init(classz);
        workbook = new SXSSFWorkbook(getXSSFWorkbook(filePath),1000);
        contentStyle = ExcelStyle.createCellStyle(workbook);
        titleStyle = ExcelStyle.createCellStyleTitle(workbook);
    }
    /**
     * 先创建一个XSSFWorkbook对象
     * @param filePath
     * @return
     */
    public static XSSFWorkbook getXSSFWorkbook(String filePath) {
        XSSFWorkbook workbook =  null;
        BufferedOutputStream outputStream = null;
        try {
            File fileXlsxPath = new File(filePath);
            outputStream = new BufferedOutputStream(new FileOutputStream(fileXlsxPath));
            workbook = new XSSFWorkbook();
            workbook.createSheet("测试Sheet");
            workbook.write(outputStream);
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if(outputStream!=null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return workbook;
    }


    protected String[] title;
    protected String[] titleKey;

    protected Map<String,Sheet> sheetMap  =new HashMap<>();

    public void init(Class<T> classz){

        Field[] fields = classz.getDeclaredFields();
        //初始化表头
        title = new String[fields.length];
        titleKey = new String[fields.length];

        for (int i = 0; i < fields.length; i++) {
            String name = fields[i].getName();
            if(name.contains("this")) {
                continue;
            }
            title[i]=name;
            titleKey[i]=name;
        }


    }




    public void writeList(String sheetName, List<T> dataList){
        Sheet sheet = null;
        if (!sheetMap.containsKey(sheetName)){
            sheet = workbook.createSheet(sheetName);
            sheetMap.put(sheetName,sheet);
            setDetailTitle(sheet);
        }else{
            sheet = sheetMap.get(sheetName);
        }
        for (T data:  dataList) {
            int rowNum = sheet.getLastRowNum();
            JSONObject json = JSONObject.parseObject(JSON.toJSONString(data,SerializerFeature.WriteDateUseDateFormat));
            Row row = sheet.createRow(++rowNum);
            // 明细
            for (int i = 0; i < titleKey.length; i++) {
                String key = titleKey[i];
                String valueString = json.getString(key);
                if (valueString != null && valueString.length() > SpreadsheetVersion.EXCEL2007.getMaxTextLength()) {
                    valueString = valueString.substring(0, SpreadsheetVersion.EXCEL2007.getMaxTextLength() - 4) + "...";
                }
                Cell cell = row.createCell(i);
                cell.setCellValue(valueString);
                cell.setCellStyle(contentStyle);
            }
        }



    }

    public void close() throws IOException {

        workbook.close();
        workbook.dispose();

    }

    /**
     * 初始化标题
     *
     * @param sheet
     */
    private void setDetailTitle(Sheet sheet) {
        Row row = sheet.createRow(0);
        for (int i = 0; i < title.length; i++) {
            sheet.setColumnWidth(i, 4048);
            Cell cell = row.createCell(i);
            String val = title[i];
            cell.setCellValue(val);
            cell.setCellStyle(titleStyle);
        }

    }


}
