package tw.org.must.must.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import tw.org.must.must.core.service.agr.*;
import tw.org.must.must.core.service.mbr.*;
import tw.org.must.must.core.service.ref.RefRightService;
import tw.org.must.must.core.service.ref.RefTerritoryRelationService;
import tw.org.must.must.core.service.wrk.WrkWorkIpShareService;
import tw.org.must.must.core.service.wrk.WrkWorkService;
import tw.org.must.must.model.agr.*;
import tw.org.must.must.model.mbr.*;
import tw.org.must.must.model.wrk.WrkWorkIpShare;
import tw.org.must.must.model.wrk.WrkWorkRight;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
@Service
public class AgrCacheTesting {

    private Logger logger = LoggerFactory.getLogger(AgrCacheTesting.class);

    @Autowired
    private AgrAssignorService agrAssignorService;

    @Autowired
    private AgrAssigneeService agrAssigneeService;

    @Autowired
    private AgrAgreementExtendService agrAgreementExtendService;

    @Autowired
    private AgrAgreementTerritoryService agrAgreementTerritoryService;

    @Autowired
    private AgrContentService agrContentService;

    @Autowired
    private AgrContentIpShareService agrContentIpShareService;

    @Autowired
    private WrkWorkIpShareService wrkWorkIpShareService;

    @Autowired
    private MbrIpNameService mbrIpNameService;

    @Autowired
    private MbrIpNameMergeService mbrIpNameMergeService;

    @Autowired
    private RefRightService refRightService;

    @Autowired
    private MbrIpAgreementService mbrIpAgreementService;

    @Autowired
    private MbrIpAgreementTerritoryService mbrIpAgreementTerritoryService;

    @Autowired
    private RefTerritoryRelationService refTerritoryRelationService;

    @Autowired
    private MbrIpService mbrIpService;

    @Autowired
    private WrkWorkService wrkWorkService;

    public void test(){
        Map<String, AgrObject> stringAgrObjectMap = loadingData();
        List<WrkWorkIpShare> workIpShareList = wrkWorkIpShareService.getWrkWorkIpShareByWorkUniqueKey("026-1007836852");
        long l = System.currentTimeMillis();
//        WrkWork wrkWorkByWorkUniqueKey = wrkWorkService.getWrkWorkByWorkUniqueKey("026-1007836852");
        HashMap<String, WrkWorkRight> stringStringHashMap = wrkWorkService.workRightSetSd("026-1007836852");
//        agrContentIpShareService.calcIpshareListByWorkSearch(stringStringHashMap,workIpShareList,"TW","2020",stringAgrObjectMap);
        long l1 = System.currentTimeMillis();
        logger.info("calcIpshareList 计算耗时："+(l1-l)+"ms");
    }

    public Map<String,AgrObject> loadingData(){
        logger.info("start Loading data ......");

        Map<String,AgrObject> agrTableMap = new ConcurrentHashMap<>();
        Map<String,Boolean> hasAgrIpMap = new ConcurrentHashMap<>();
        hasAgrIpMap.put("hasAgrIp",false);
        ExecutorService executorService = Executors.newFixedThreadPool(5);

        // 1、执行 将Assignor写入内存
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                Long id = 0l;
                Long startTime = System.currentTimeMillis();
                logger.info("開始寫入Assignor......");
                boolean hasNext = true;
                while(hasNext){
                    logger.info("query 10000-assignor start......");
                    long start = System.currentTimeMillis();
                    List<AgrAssignor> agrAssignorList = agrAssignorService.getAgrAssignorByIdLimit1000(id);
                    long end = System.currentTimeMillis();
                    logger.info("query 10000-assignee end......耗时："+(end-start));
                    if(null == agrAssignorList || agrAssignorList.size() <1){
                        hasNext = false;
                        break;
                    }

                    ExecutorService resultExecutorService = Executors.newFixedThreadPool(5);

                    for (int i=0;i<5;i++){
                        List<AgrAssignor> resultList = agrAssignorList.subList(i * 2000, (i + 1) * 2000);

                        resultExecutorService.execute(new Runnable() {
                            @Override
                            public void run() {
                                logger.info(Thread.currentThread().getName()+" for assignee start......");
                                for (AgrAssignor agrAssignor : resultList) {
                                    String agrNo = agrAssignor.getAgrNo();
                                    String oipBaseNo = agrAssignor.getOipBaseNo();
                                    String oipNameNo = agrAssignor.getOipNameNo();
                                    AgrObject assignorObject = agrTableMap.get(agrNo);
                                    AgrObject agrObject1 = agrTableMap.get(oipBaseNo);
                                    AgrObject agrObject2 = agrTableMap.get(oipNameNo);
                                    CAgrAssignor cAgrAssignor = createCagrAssignor(agrAssignor);

                                    if(null == assignorObject){
                                        assignorObject = new AgrObject();
                                        assignorObject.init();
                                        assignorObject.setAgrAssignor(cAgrAssignor); // 1对1 不需要更新
                                    }
                                    agrTableMap.put(agrNo,assignorObject);

                                    if(null == agrObject1){
                                        agrObject1 = new AgrObject();
                                        agrObject1.init();
                                    }
                                    List<CAgrAssignor> agrAssignorList1 = agrObject1.getAgrAssignorList();
                                    agrAssignorList1.add(cAgrAssignor);
                                    agrObject1.setAgrAssignorList(agrAssignorList1);
                                    agrTableMap.put(oipBaseNo,agrObject1);

                                    if(null == agrObject2){
                                        agrObject2 = new AgrObject();
                                        agrObject2.init();
                                    }
                                    List<CAgrAssignor> agrAssignorList2 = agrObject2.getAgrAssignorList();
                                    agrAssignorList2.add(cAgrAssignor);
                                    agrObject2.setAgrAssignorList(agrAssignorList2);
                                    agrTableMap.put(oipNameNo,agrObject2);
                                }
                                logger.info(Thread.currentThread().getName()+" for assignee end......");
                            }
                        });
                    }

                    resultExecutorService.shutdown();
                    while (true){
                        if(resultExecutorService.isTerminated())
                            break;
                    }

                    Comparator<? super AgrAssignor> comparatorByGraterThran = (o1, o2) -> o1.getId().intValue()
                            - o2.getId().intValue();
                    AgrAssignor maxByGraterThanId = agrAssignorList.stream().max(comparatorByGraterThran).get();
                    id = maxByGraterThanId.getId();
                }
                Long endTime = System.currentTimeMillis();
                hasAgrIpMap.put("hasAgrIp",true);
                logger.info("Assignor寫入完成~ 耗时为："+(endTime-startTime)+"ms!");
            }
        });

        // 2、执行 将Assignee写入库
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                Long id = 0l;
                Long startTime = System.currentTimeMillis();
                logger.info("開始寫入Assignee......");
                synchronized (id){
                    boolean hasNext = true;
                    while(hasNext){
                        logger.info("query 10000-assignee start......");
                        long start = System.currentTimeMillis();
                        List<AgrAssignee> agrAssigneeList = agrAssigneeService.getAssigneeMapByIdLimit1000(id);
                        long end = System.currentTimeMillis();
                        logger.info("query 10000-assignee end......耗时："+(end-start));
                        if(null == agrAssigneeList || agrAssigneeList.size() <1){
                            hasNext = false;
                            break;
                        }

                        ExecutorService resultExecutorService = Executors.newFixedThreadPool(5);

                        for (int i=0;i<5;i++){
                            List<AgrAssignee> resultList = agrAssigneeList.subList(i * 2000, (i + 1) * 2000);

                            resultExecutorService.execute(new Runnable() {
                                @Override
                                public void run() {
                                    logger.info(Thread.currentThread().getName()+" for assignee start......");
                                    for (AgrAssignee agrAssignee : resultList) {
                                        AgrObject assignorObject = agrTableMap.get(agrAssignee.getAgrNo());
                                        CAgrAssignee cAgrAssignee = createCagrAssignee(agrAssignee);
                                        if(null == assignorObject){
                                            assignorObject = new AgrObject();
                                            assignorObject.init();
                                        }
                                        List<CAgrAssignee> agrAssigneeList1 = assignorObject.getAgrAssigneeList();
                                        agrAssigneeList1.add(cAgrAssignee);
                                        assignorObject.setAgrAssigneeList(agrAssigneeList1);
                                        agrTableMap.put(agrAssignee.getAgrNo(),assignorObject);
                                    }
                                    logger.info(Thread.currentThread().getName()+" for assignee end......");
                                }
                            });
                        }

                        resultExecutorService.shutdown();
                        while (true){
                            if(resultExecutorService.isTerminated())
                                break;
                        }

                        Comparator<? super AgrAssignee> comparatorByGraterThran = (o1, o2) -> o1.getId().intValue()
                                - o2.getId().intValue();
                        AgrAssignee maxByGraterThanId = agrAssigneeList.stream().max(comparatorByGraterThran).get();
                        id = maxByGraterThanId.getId();
                    }
                    Long endTime = System.currentTimeMillis();
                    logger.info("AgrAssignee寫入完成~ 耗时为："+(endTime-startTime)+"ms!");
                }
            }
        });

        // 3、agrExtend 缓存
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                Long id = 0l;
                Long startTime = System.currentTimeMillis();
                logger.info("開始寫入agrExtend......");
                synchronized (id){
                    boolean hasNext = true;
                    while(hasNext){
                        logger.info("query 10000-agrExtend start......");
                        long start = System.currentTimeMillis();
                        List<AgrAgreementExtend> agrAgreementExtendList = agrAgreementExtendService.getAgreementExtendByIdLimit1000(id);
                        long end = System.currentTimeMillis();
                        logger.info("query 10000-agrExtend end......耗时："+(end-start));
                        if(null == agrAgreementExtendList || agrAgreementExtendList.size() <1){
                            hasNext = false;
                            break;
                        }

                        ExecutorService resultExecutorService = Executors.newFixedThreadPool(5);

                        for (int i=0;i<5;i++){
                            List<AgrAgreementExtend> resultList = agrAgreementExtendList.subList(i * 2000, (i + 1) * 2000);

                            resultExecutorService.execute(new Runnable() {
                                @Override
                                public void run() {
                                    logger.info(Thread.currentThread().getName()+" for agrExtend start......");
                                    for (AgrAgreementExtend agrAgreementExtend : resultList) {
                                        AgrObject assignorObject = agrTableMap.get(agrAgreementExtend.getAgrNo());
                                        CAgrAgreementExtend cAgrAgreementExtend = createCagrExtend(agrAgreementExtend);
                                        if(null == assignorObject){
                                            assignorObject = new AgrObject();
                                            assignorObject.init();
                                        }
                                        List<CAgrAgreementExtend> agrAgreementExtendList1 = assignorObject.getAgrAgreementExtendList();
                                        agrAgreementExtendList1.add(cAgrAgreementExtend);
                                        assignorObject.setAgrAgreementExtendList(agrAgreementExtendList1);
                                        agrTableMap.put(agrAgreementExtend.getAgrNo(),assignorObject);
                                    }
                                    logger.info(Thread.currentThread().getName()+" for agrExtend end......");
                                }
                            });
                        }

                        resultExecutorService.shutdown();
                        while (true){
                            if(resultExecutorService.isTerminated())
                                break;
                        }

                        Comparator<? super AgrAgreementExtend> comparatorByGraterThran = (o1, o2) -> o1.getId().intValue()
                                - o2.getId().intValue();
                        AgrAgreementExtend maxByGraterThanId = agrAgreementExtendList.stream().max(comparatorByGraterThran).get();
                        id = maxByGraterThanId.getId();
                    }
                    Long endTime = System.currentTimeMillis();
                    logger.info("AgrAgreementExtend寫入完成~ 耗时为："+(endTime-startTime)+"ms!");
                }
            }
        });

        // 4、AgrTerr   缓存
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                Long id = 0l;
                Long startTime = System.currentTimeMillis();
                logger.info("開始寫入agrTerry......");
                synchronized (id){
                    boolean hasNext = true;
                    while(hasNext){
                        logger.info("query 10000-agrTerry start......");
                        long start = System.currentTimeMillis();
                        List<AgrAgreementTerritory> agrAgreementTerritoryList = agrAgreementTerritoryService.getAgrAgreementTerrByIdLimit1000(id);
                        long end = System.currentTimeMillis();
                        logger.info("query 10000-agrExtend end......耗时："+(end-start));
                        if(null == agrAgreementTerritoryList || agrAgreementTerritoryList.size() <1){
                            hasNext = false;
                            break;
                        }

                        ExecutorService resultExecutorService = Executors.newFixedThreadPool(5);

                        for (int i=0;i<5;i++){
                            List<AgrAgreementTerritory> resultList = agrAgreementTerritoryList.subList(i * 2000, (i + 1) * 2000);

                            resultExecutorService.execute(new Runnable() {
                                @Override
                                public void run() {
                                    logger.info(Thread.currentThread().getName()+" for agrExtend start......");
                                    for (AgrAgreementTerritory agrAgreementTerritory : resultList) {
                                        AgrObject assignorObject = agrTableMap.get(agrAgreementTerritory.getAgrNo());
                                        CAgrAgreementTerritory cAgrAgreementTerritory = createCagrTerr(agrAgreementTerritory);
                                        if(null == assignorObject){
                                            assignorObject = new AgrObject();
                                            assignorObject.init();
                                        }
                                        List<CAgrAgreementTerritory> agrAgreementTerritoryList1 = assignorObject.getAgrAgreementTerritoryList();
                                        agrAgreementTerritoryList1.add(cAgrAgreementTerritory);
                                        assignorObject.setAgrAgreementTerritoryList(agrAgreementTerritoryList1);
                                        agrTableMap.put(agrAgreementTerritory.getAgrNo(),assignorObject);
                                    }
                                    logger.info(Thread.currentThread().getName()+" for agrExtend end......");
                                }
                            });
                        }

                        resultExecutorService.shutdown();
                        while (true){
                            if(resultExecutorService.isTerminated())
                                break;
                        }

                        Comparator<? super AgrAgreementTerritory> comparatorByGraterThran = (o1, o2) -> o1.getId().intValue()
                                - o2.getId().intValue();
                        AgrAgreementTerritory maxByGraterThanId = agrAgreementTerritoryList.stream().max(comparatorByGraterThran).get();
                        id = maxByGraterThanId.getId();
                    }
                    Long endTime = System.currentTimeMillis();
                    logger.info("AgrAgreementTerritory寫入完成~ 耗时为："+(endTime-startTime)+"ms!");
                }
            }
        });

        // 5、agrContent 缓存
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                Long id = 0l;
                Long startTime = System.currentTimeMillis();
                synchronized (id){
                    boolean hasNext = true;
                    while(hasNext){
                        logger.info("query 10000-agrContent start......");
                        long start = System.currentTimeMillis();
                        List<AgrContent> agrContentList = agrContentService.getAgrContentByIdLimit1000(id);
                        long end = System.currentTimeMillis();
                        logger.info("query 10000-agrContent end......耗时："+(end-start));
                        if(null == agrContentList || agrContentList.size() <1){
                            hasNext = false;
                            break;
                        }

                        ExecutorService resultExecutorService = Executors.newFixedThreadPool(5);

                        for (int i=0;i<5;i++){
                            List<AgrContent> resultList = agrContentList.subList(i * 2000, (i + 1) * 2000);

                            resultExecutorService.execute(new Runnable() {
                                @Override
                                public void run() {
                                    logger.info(Thread.currentThread().getName()+" for agrContent start......");
                                    for (AgrContent agrContent : resultList) {
                                        AgrObject assignorObject = agrTableMap.get(agrContent.getAgrNo());
                                        if(null == assignorObject){
                                            assignorObject = new AgrObject();
                                            assignorObject.init();
                                            CAgrContent cAgrContent = createCagrContent(agrContent);
                                            assignorObject.setAgrContent(cAgrContent);
                                            agrTableMap.put(agrContent.getAgrNo(),assignorObject);
                                        }
                                    }
                                    logger.info(Thread.currentThread().getName()+" for agrContent end......");
                                }
                            });
                        }

                        resultExecutorService.shutdown();
                        while (true){
                            if(resultExecutorService.isTerminated())
                                break;
                        }

                        Comparator<? super AgrContent> comparatorByGraterThran = (o1, o2) -> o1.getId().intValue()
                                - o2.getId().intValue();
                        AgrContent maxByGraterThanId = agrContentList.stream().max(comparatorByGraterThran).get();
                        id = maxByGraterThanId.getId();

                    }
                    Long endTime = System.currentTimeMillis();
                    logger.info("AgrContent写入内存完成~ 耗时为："+(endTime-startTime)+"ms!");
                }
            }
        });

        executorService.shutdown();
        while (true){
            if(executorService.isTerminated())
                break;
        }


        // 6、mbrIpName   FIXME 这里只获取有合约的人
        if(hasAgrIpMap.get("hasAgrIp")){
            ExecutorService executorService1 = Executors.newFixedThreadPool(5);
            executorService1.execute(new Runnable() {
                @Override
                public void run() {
                    Long id = 0l;
                    Long startTime = System.currentTimeMillis();
                    logger.info("開始寫入mbrIpName......");
                        boolean hasNext = true;
                        while(hasNext){
                            logger.info("query 10000-mbrIpName start......");
                            long start = System.currentTimeMillis();
                            List<MbrIpName> mbrIpNameList = mbrIpNameService.getMbrIpNameByIdLimit1000(id);
                            long end = System.currentTimeMillis();
                            logger.info("query 10000-mbrIpName end......耗时："+(end-start));
                            if(null == mbrIpNameList || mbrIpNameList.size() <1){
                                hasNext = false;
                                break;
                            }

                            ExecutorService resultExecutorService = Executors.newFixedThreadPool(5);

                            for (int i=0;i<5;i++){
                                List<MbrIpName> resultList = mbrIpNameList.subList(i * 2000, (i + 1) * 2000);

                                resultExecutorService.execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        logger.info(Thread.currentThread().getName()+" for mbrIpName start......");
                                        for (MbrIpName mbrIpName : mbrIpNameList) {
                                            AgrObject assignorObject = agrTableMap.get(mbrIpName.getIpNameNo());
                                            if(null != assignorObject){
                                                CMbrIpName cMbrIpName = createCmbrIpName(mbrIpName);
                                                assignorObject.setMbrIpName(cMbrIpName);
                                                agrTableMap.put(mbrIpName.getIpNameNo(),assignorObject);
                                            }
                                        }
                                        logger.info(Thread.currentThread().getName()+" for mbrIpName end......");
                                    }
                                });
                            }

                            resultExecutorService.shutdown();
                            while (true){
                                if(resultExecutorService.isTerminated())
                                    break;
                            }

                            Comparator<? super MbrIpName> comparatorByGraterThran = (o1, o2) -> o1.getId().intValue()
                                    - o2.getId().intValue();
                            MbrIpName maxByGraterThanId = mbrIpNameList.stream().max(comparatorByGraterThran).get();
                            id = maxByGraterThanId.getId();
                        }
                        Long endTime = System.currentTimeMillis();
                        logger.info("MbrIpName写入内存完成~ 耗时为："+(endTime-startTime)+"ms!");
                    }
            });

            // 7、mbrIpNameMerge
            executorService1.execute(new Runnable() {
                @Override
                public void run() {
                    Long id = 0l;
                    Long startTime = System.currentTimeMillis();
                    logger.info("開始寫入mbrIpNameMerge......");
                        boolean hasNext = true;
                        while(hasNext){
                            logger.info("query 10000-mbrIpNameMerge start......");
                            long start = System.currentTimeMillis();
                            List<MbrIpNameMerge> mbrIpNameMergeList = mbrIpNameMergeService.getMbrIpNameMergeByIdLimit1000(id);
                            long end = System.currentTimeMillis();
                            logger.info("query 10000-mbrIpNameMerge end......耗时："+(end-start));
                            if(null == mbrIpNameMergeList || mbrIpNameMergeList.size() <1){
                                hasNext = false;
                                break;
                            }

                            ExecutorService resultExecutorService = Executors.newFixedThreadPool(5);

                            for (int i=0;i<5;i++){
                                List<MbrIpNameMerge> resultList = mbrIpNameMergeList.subList(i * 2000, (i + 1) * 2000);

                                resultExecutorService.execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        logger.info(Thread.currentThread().getName()+" for mbrIpNameMerge start......");
                                        for (MbrIpNameMerge mbrIpNameMerge : resultList) {
                                            AgrObject assignorObject = agrTableMap.get(mbrIpNameMerge.getIpNameNo());
                                            if(null != assignorObject){
                                                CMbrIpName mbrIpName = assignorObject.getMbrIpName();
                                                mbrIpName.setIpBaseNo(mbrIpNameMerge.getIpBaseNo());
                                                agrTableMap.put(mbrIpNameMerge.getIpNameNo(),assignorObject);
                                            }
                                        }
                                        logger.info(Thread.currentThread().getName()+" for mbrIpNameMerge end......");
                                    }
                                });
                            }

                            resultExecutorService.shutdown();
                            while (true){
                                if(resultExecutorService.isTerminated())
                                    break;
                            }

                            Comparator<? super MbrIpNameMerge> comparatorByGraterThran = (o1, o2) -> o1.getId().intValue()
                                    - o2.getId().intValue();
                            MbrIpNameMerge maxByGraterThanId = mbrIpNameMergeList.stream().max(comparatorByGraterThran).get();
                            id = maxByGraterThanId.getId();
                        }
                        Long endTime = System.currentTimeMillis();
                        logger.info("MbrIpNameMerge写入内存完成~ 耗时为："+(endTime-startTime)+"ms!");
                    }
            });

            // 9、MbrIpAgreement  FIXME 这里只缓存有权利的ip 且有合约的
            executorService1.execute(new Runnable() {
                @Override
                public void run() {
                    Long id = 0l;
                    Long startTime = System.currentTimeMillis();

                    List<String> rightForMustAry = initRightForMust();
                    logger.info("開始寫入MbrIpAgreement......");
                    synchronized (id){
                        boolean hasNext = true;
                        while(hasNext){
                            logger.info("query 10000-mbrIpAgreement start......");
                            long start = System.currentTimeMillis();
                            List<MbrIpAgreement> mbrIpAgreementList = mbrIpAgreementService.getMbrIpAgreementByIdLimit1000(id);
                            long end = System.currentTimeMillis();
                            logger.info("query 10000-mbrIpAgreement end......耗时："+(end-start));
                            if(null == mbrIpAgreementList || mbrIpAgreementList.size() <1){
                                hasNext = false;
                                break;
                            }

                            ExecutorService resultExecutorService = Executors.newFixedThreadPool(5);

                            for (int i=0;i<5;i++){
                                List<MbrIpAgreement> resultList = mbrIpAgreementList.subList(i * 2000, (i + 1) * 2000);

                                resultExecutorService.execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        logger.info(Thread.currentThread().getName()+" for mbrIpAgreement start......");
                                        for (MbrIpAgreement mbrIpAgreement : resultList) {
                                            AgrObject assignorObject = agrTableMap.get(mbrIpAgreement.getIpBaseNo());
                                            if(null != assignorObject){
                                                List<CMbrIpAgreement> mbrIpAgreementList1 = assignorObject.getMbrIpAgreementList();
                                                if(mbrIpAgreementList1.size()>0){
                                                    // 如果存在 只需要更新
                                                    for (CMbrIpAgreement cMbrIpAgreement : mbrIpAgreementList1) {
                                                        boolean hasRoleCode = checkIpHasRight(mbrIpAgreement, rightForMustAry);
                                                        if(hasRoleCode){
                                                            cMbrIpAgreement.setHasRight(true);
                                                        }
                                                        if("MR".equalsIgnoreCase(mbrIpAgreement.getRightCode())){
                                                            cMbrIpAgreement.setHasRight(true);
                                                        }
                                                    }
                                                }else{
                                                    boolean hasRoleCode = checkIpHasRight(mbrIpAgreement, rightForMustAry);
                                                    if(hasRoleCode) {
                                                        CMbrIpAgreement cMbrIpAgreement = createCmbrIpAgreement(mbrIpAgreement);
                                                        List<CMbrIpAgreement> mbrIpAgreementList2 = assignorObject.getMbrIpAgreementList();
                                                        mbrIpAgreementList2.add(cMbrIpAgreement);
                                                        assignorObject.setMbrIpAgreementList(mbrIpAgreementList2);
                                                        agrTableMap.put(mbrIpAgreement.getIpBaseNo(),assignorObject);
                                                    }
                                                    if("MR".equalsIgnoreCase(mbrIpAgreement.getRightCode())){
                                                        CMbrIpAgreement cMbrIpAgreement = createCmbrIpAgreement(mbrIpAgreement);
                                                        List<CMbrIpAgreement> mbrIpAgreementList2 = assignorObject.getMbrIpAgreementList();
                                                        mbrIpAgreementList2.add(cMbrIpAgreement);
                                                        assignorObject.setMbrIpAgreementList(mbrIpAgreementList2);
                                                        agrTableMap.put(mbrIpAgreement.getIpBaseNo(),assignorObject);
                                                    }
                                                }
                                            }
                                        }
                                        logger.info(Thread.currentThread().getName()+" for mbrIpAgreement end......");
                                    }
                                });
                            }

                            resultExecutorService.shutdown();
                            while (true){
                                if(resultExecutorService.isTerminated())
                                    break;
                            }

                            Comparator<? super MbrIpAgreement> comparatorByGraterThran = (o1, o2) -> o1.getId().intValue()
                                    - o2.getId().intValue();
                            MbrIpAgreement maxByGraterThanId = mbrIpAgreementList.stream().max(comparatorByGraterThran).get();
                            id = maxByGraterThanId.getId();
                        }
                        Long endTime = System.currentTimeMillis();
                        logger.info("MbrIpAgreement写入内存完成~ 耗时为："+(endTime-startTime)+"ms!");
                    }
                }
            });

            // 10、MbrIpAgreementTerritory
            executorService1.execute(new Runnable() {
                @Override
                public void run() {
                    Long id = 0l;
                    Long startTime = System.currentTimeMillis();
                    logger.info("開始寫入MbrIpAgreementTerritory......");
                    synchronized (id){
                        boolean hasNext = true;
                        while(hasNext){
                            logger.info("query 10000-mbrIpAgreementTerritory start......");
                            long start = System.currentTimeMillis();
                            List<MbrIpAgreementTerritory> mbrIpAgreementTerritoryList = mbrIpAgreementTerritoryService.getMbrIpAgreementTerritoryByIdLimit1000(id);
                            long end = System.currentTimeMillis();
                            logger.info("query 10000-mbrIpAgreementTerritory end......耗时："+(end-start));
                            if(null == mbrIpAgreementTerritoryList || mbrIpAgreementTerritoryList.size() <1){
                                hasNext = false;
                                break;
                            }

                            ExecutorService resultExecutorService = Executors.newFixedThreadPool(5);

                            for (int i=0;i<5;i++){
                                int startNum = i*2000;
                                int endNum = (i + 1) * 2000;

                                if(endNum>mbrIpAgreementTerritoryList.size()){
                                    endNum = mbrIpAgreementTerritoryList.size();
                                }

                                List<MbrIpAgreementTerritory> resultList = mbrIpAgreementTerritoryList.subList(startNum, endNum);

                                resultExecutorService.execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        logger.info(Thread.currentThread().getName()+" for mbrIpAgreementTerritory start......");
                                        for (MbrIpAgreementTerritory mbrIpAgreementTerritory : resultList) {
                                            AgrObject assignorObject = agrTableMap.get(mbrIpAgreementTerritory.getIpBaseNo());
                                            if(null != assignorObject){
                                                List<CMbrIpAgreement> mbrIpAgreementList = assignorObject.getMbrIpAgreementList();
                                                if(mbrIpAgreementList.size() <1){
                                                    CMbrIpAgreement cMbrIpAgreement = createCmbrIpAgrTerr(mbrIpAgreementTerritory);
                                                    mbrIpAgreementList.add(cMbrIpAgreement);
                                                    assignorObject.setMbrIpAgreementList(mbrIpAgreementList);
                                                    agrTableMap.put(mbrIpAgreementTerritory.getIpBaseNo(),assignorObject);
                                                }else{
                                                    for (CMbrIpAgreement cMbrIpAgreement : mbrIpAgreementList) {
                                                        cMbrIpAgreement.setIndicator(mbrIpAgreementTerritory.getIndicator());
                                                    }
                                                }
                                            }
                                        }
                                        logger.info(Thread.currentThread().getName()+" for mbrIpAgreementTerritory end......");
                                    }
                                });
                            }

                            resultExecutorService.shutdown();
                            while (true){
                                if(resultExecutorService.isTerminated())
                                    break;
                            }

                            Comparator<? super MbrIpAgreementTerritory> comparatorByGraterThran = (o1, o2) -> o1.getId().intValue()
                                    - o2.getId().intValue();
                            MbrIpAgreementTerritory maxByGraterThanId = mbrIpAgreementTerritoryList.stream().max(comparatorByGraterThran).get();
                            id = maxByGraterThanId.getId();
                        }
                        Long endTime = System.currentTimeMillis();
                        logger.info("MbrIpAgreementTerritory写入内存完成~ 耗时为："+(endTime-startTime)+"ms!");
                    }
                }
            });

            // 11、MbrIp
            executorService1.execute(new Runnable() {
                @Override
                public void run() {
                    Long id = 0l;
                    Long startTime = System.currentTimeMillis();
                    logger.info("開始寫入MbrIp......");
                    synchronized (id){
                        boolean hasNext = true;
                        while(hasNext){
                            logger.info("query 10000-mbrIp start......");
                            long start = System.currentTimeMillis();
                            List<MbrIp> mbrIpList = mbrIpService.getMbrIpByIdLimit1000(id);
                            long end = System.currentTimeMillis();
                            logger.info("query 10000-mbrIp end......耗时："+(end-start));
                            if(null == mbrIpList || mbrIpList.size() <1){
                                hasNext = false;
                                break;
                            }

                            ExecutorService resultExecutorService = Executors.newFixedThreadPool(5);

                            for (int i=0;i<5;i++){
                                List<MbrIp> resultList = mbrIpList.subList(i * 2000, (i + 1) * 2000);

                                resultExecutorService.execute(new Runnable() {
                                    @Override
                                    public void run() {
                                        logger.info(Thread.currentThread().getName()+" for mbrIp end......");
                                        for (MbrIp mbrIp : resultList) {
                                            AgrObject assignorObject = agrTableMap.get(mbrIp.getIpBaseNo());
                                            if(null != assignorObject){
                                                CMbrIp cMbrIp = createCmbrIp(mbrIp);
                                                assignorObject.setMbrIp(cMbrIp);
                                                agrTableMap.put(mbrIp.getIpBaseNo(),assignorObject);
                                            }
                                        }
                                        logger.info(Thread.currentThread().getName()+" for mbrIp end......");
                                    }
                                });
                            }

                            Comparator<? super MbrIp> comparatorByGraterThran = (o1, o2) -> o1.getId().intValue()
                                    - o2.getId().intValue();
                            MbrIp maxByGraterThanId = mbrIpList.stream().max(comparatorByGraterThran).get();
                            id = maxByGraterThanId.getId();
                        }
                        Long endTime = System.currentTimeMillis();
                        logger.info("MbrIp写入内存完成~ 耗时为："+(endTime-startTime)+"ms!");
                    }
                }
            });

            executorService1.shutdown();
            while (true){
                if(executorService1.isTerminated())
                    break;
            }
        }

        // 12、ref_territory_relation
//        executorService.execute(new Runnable() {
//            @Override
//            public void run() {
//                Long startTime = System.currentTimeMillis();
//                boolean hasNext = true;
//                while(hasNext){
//                    List<RefTerritoryRelation> refTerritoryRelationList = refTerritoryRelationService.listAll();
//                    if(null == refTerritoryRelationList || refTerritoryRelationList.size() <1){
//                        hasNext = false;
//                        break;
//                    }
//                    for (RefTerritoryRelation refTerritoryRelation : refTerritoryRelationList) {
//                        AgrObject assignorObject = agrTableMap.get(refTerritoryRelation.getTisN()+"");
//                        if(null == assignorObject){
//                            assignorObject = new AgrObject();
//                            assignorObject.init();
//                            assignorObject.getRefTerritoryRelationList().add(refTerritoryRelation);
//                        }else{
//                            assignorObject.getRefTerritoryRelationList().add(refTerritoryRelation);
//                        }
//                        agrTableMap.put(refTerritoryRelation.getTisN()+"",assignorObject);
//                    }
//                }
//                Long endTime = System.currentTimeMillis();
//                logger.info("RefTerritoryRelation写入内存完成~ 耗时为："+(endTime-startTime)+"ms!");
//            }
//        });

        executorService.shutdown();
        while (true){
            if(executorService.isTerminated())
                break;
        }
        return agrTableMap;
    }

    private CMbrIpAgreement createCmbrIpAgrTerr(MbrIpAgreementTerritory mbrIpAgreementTerritory) {
        CMbrIpAgreement cMbrIpAgreement = new CMbrIpAgreement();
        cMbrIpAgreement.setHasRight(true);
        cMbrIpAgreement.setIpBaseNo(mbrIpAgreementTerritory.getIpBaseNo());
        cMbrIpAgreement.setItTisN(Integer.parseInt(mbrIpAgreementTerritory.getTisN()+""));
        cMbrIpAgreement.setRightCode(mbrIpAgreementTerritory.getRightCode());
        cMbrIpAgreement.setSocietyCode(mbrIpAgreementTerritory.getSocietyCode());
        cMbrIpAgreement.setValidFrom(mbrIpAgreementTerritory.getValidFrom());
        cMbrIpAgreement.setValidTo(mbrIpAgreementTerritory.getValidTo());
        return cMbrIpAgreement;
    }

    private CMbrIp createCmbrIp(MbrIp mbrIp) {
        CMbrIp cMbrIp = new CMbrIp();
        cMbrIp.setIpBaseNo(mbrIp.getIpBaseNo());
        cMbrIp.setIpType(mbrIp.getIpType());
        return cMbrIp;
    }

    private CMbrIpAgreement createCmbrIpAgreement(MbrIpAgreement mbrIpAgreement) {
        CMbrIpAgreement cMbrIpAgreement = new CMbrIpAgreement();
        cMbrIpAgreement.setHasRight(true);
        cMbrIpAgreement.setIpBaseNo(mbrIpAgreement.getIpBaseNo());
        cMbrIpAgreement.setItTisN(mbrIpAgreement.getTisN());
        cMbrIpAgreement.setRightCode(mbrIpAgreement.getRightCode());
        cMbrIpAgreement.setSocietyCode(mbrIpAgreement.getSocietyCode());
        cMbrIpAgreement.setValidFrom(mbrIpAgreement.getValidFrom());
        cMbrIpAgreement.setValidTo(mbrIpAgreement.getValidTo());
        return cMbrIpAgreement;
    }

    private boolean checkIpHasRight(MbrIpAgreement mbrIpAgreement,List<String> rightForMustAry) {
        String rightCode = mbrIpAgreement.getRightCode();
        if(rightForMustAry.contains(rightCode)){
            return true;
        }
        return false;
    }

    private CMbrIpName createCmbrIpName(MbrIpName mbrIpName) {
        CMbrIpName cMbrIpName = new CMbrIpName();
        cMbrIpName.setChineseName(mbrIpName.getChineseName());
        cMbrIpName.setIpNameNo(mbrIpName.getIpNameNo());
        cMbrIpName.setName(mbrIpName.getName());
        cMbrIpName.setNameType(mbrIpName.getNameType());
        return cMbrIpName;
    }

    private CAgrContent createCagrContent(AgrContent agrContent) {
        CAgrContent cAgrContent = new CAgrContent();
        cAgrContent.setAgrNo(agrContent.getAgrNo());
        cAgrContent.setAgrEdate(agrContent.getAgrEdate());
        cAgrContent.setAgrType(agrContent.getAgrType());
        cAgrContent.setAutoExtensionInd(agrContent.getAutoExtensionInd());
        cAgrContent.setPreTerm(agrContent.getPreTerm());
        cAgrContent.setAgrSdate(agrContent.getAgrSdate());

        return cAgrContent;
    }

    private CAgrAgreementTerritory createCagrTerr(AgrAgreementTerritory agrAgreementTerritory) {
        CAgrAgreementTerritory cAgrAgreementTerritory = new CAgrAgreementTerritory();
        cAgrAgreementTerritory.setAgrNo(agrAgreementTerritory.getAgrNo());
        cAgrAgreementTerritory.setIndicator(agrAgreementTerritory.getIndicator());
        cAgrAgreementTerritory.setTisN(agrAgreementTerritory.getTisN());
        return cAgrAgreementTerritory;
    }

    private CAgrAgreementExtend createCagrExtend(AgrAgreementExtend agrAgreementExtend) {
        CAgrAgreementExtend cAgrAgreementExtend = new CAgrAgreementExtend();
        cAgrAgreementExtend.setAgrNo(agrAgreementExtend.getAgrNo());
        cAgrAgreementExtend.setIpBaseNo(agrAgreementExtend.getIpBaseNo());
        cAgrAgreementExtend.setIpNameNo(agrAgreementExtend.getIpNameNo());
        cAgrAgreementExtend.setWorknumSociety(agrAgreementExtend.getWorknumSociety());
        cAgrAgreementExtend.setWorksnum(agrAgreementExtend.getWorksnum());
        cAgrAgreementExtend.setIndicator(agrAgreementExtend.getIndicator());
        cAgrAgreementExtend.setOriValidTo(agrAgreementExtend.getOriValidTo());
        cAgrAgreementExtend.setWorkWriterRole(agrAgreementExtend.getWorkWriterRole());
        return cAgrAgreementExtend;
    }

    private CAgrAssignee createCagrAssignee(AgrAssignee agrAssignee) {
        CAgrAssignee cAgrAssignee = new CAgrAssignee();
        cAgrAssignee.setAgrNo(agrAssignee.getAgrNo());
        cAgrAssignee.setSipNameNo(agrAssignee.getSipNameNo());
        cAgrAssignee.setSipBaseNo(agrAssignee.getSipBaseNo());
        cAgrAssignee.setSipDbshare(agrAssignee.getSipDbshare());
        cAgrAssignee.setSipMshare(agrAssignee.getSipMshare());
        cAgrAssignee.setSipRole(agrAssignee.getSipRole());
        cAgrAssignee.setSipOdshare(agrAssignee.getSipOdshare());
        cAgrAssignee.setSipPshare(agrAssignee.getSipPshare());
        cAgrAssignee.setSipSshare(agrAssignee.getSipSshare());
        return cAgrAssignee;

    }

    private CAgrAssignor createCagrAssignor(AgrAssignor agrAssignor) {
        CAgrAssignor cAgrAssignor = new CAgrAssignor();
        cAgrAssignor.setAgrNo(agrAssignor.getAgrNo());
        cAgrAssignor.setOipBaseNo(agrAssignor.getOipBaseNo());
        cAgrAssignor.setOipNameNo(agrAssignor.getOipNameNo());
        cAgrAssignor.setAgrContentNo(agrAssignor.getContentAgrNo());
        return cAgrAssignor;
    }

    private List<String> initRightForMust() {
        List<String> rightForMustAry = new ArrayList<>();
        rightForMustAry.add("PC");
        rightForMustAry.add("PR");
        rightForMustAry.add("RB");
        rightForMustAry.add("RT");
        rightForMustAry.add("TB");
        rightForMustAry.add("TO");
        rightForMustAry.add("TP");
        rightForMustAry.add("TV");
        return rightForMustAry;
    }

    public class CMbrIpAgreement{
        private String ipBaseNo;
        private Date validFrom;
        private Date validTo;
        private Integer itTisN;
        private String rightCode;
        private boolean hasRight;
        private Integer societyCode;
        private String indicator;

        public String getIpBaseNo() {
            return ipBaseNo;
        }

        public void setIpBaseNo(String ipBaseNo) {
            this.ipBaseNo = ipBaseNo;
        }

        public Date getValidFrom() {
            return validFrom;
        }

        public void setValidFrom(Date validFrom) {
            this.validFrom = validFrom;
        }

        public Date getValidTo() {
            return validTo;
        }

        public void setValidTo(Date validTo) {
            this.validTo = validTo;
        }

        public Integer getItTisN() {
            return itTisN;
        }

        public void setItTisN(Integer itTisN) {
            this.itTisN = itTisN;
        }

        public String getRightCode() {
            return rightCode;
        }

        public void setRightCode(String rightCode) {
            this.rightCode = rightCode;
        }

        public boolean isHasRight() {
            return hasRight;
        }

        public void setHasRight(boolean hasRight) {
            this.hasRight = hasRight;
        }

        public Integer getSocietyCode() {
            return societyCode;
        }

        public void setSocietyCode(Integer societyCode) {
            this.societyCode = societyCode;
        }

        public String getIndicator() {
            return indicator;
        }

        public void setIndicator(String indicator) {
            this.indicator = indicator;
        }
    }

    public class CMbrIpName{
        private String ipNameNo;
        private String name;
        private String chineseName;
        private String nameType;
        private String ipBaseNo;

        public String getIpNameNo() {
            return ipNameNo;
        }

        public void setIpNameNo(String ipNameNo) {
            this.ipNameNo = ipNameNo;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getChineseName() {
            return chineseName;
        }

        public void setChineseName(String chineseName) {
            this.chineseName = chineseName;
        }

        public String getNameType() {
            return nameType;
        }

        public void setNameType(String nameType) {
            this.nameType = nameType;
        }

        public String getIpBaseNo() {
            return ipBaseNo;
        }

        public void setIpBaseNo(String ipBaseNo) {
            this.ipBaseNo = ipBaseNo;
        }
    }

    public class CMbrIp{
        private String ipBaseNo;
        private String ipType;

        public String getIpBaseNo() {
            return ipBaseNo;
        }

        public void setIpBaseNo(String ipBaseNo) {
            this.ipBaseNo = ipBaseNo;
        }

        public String getIpType() {
            return ipType;
        }

        public void setIpType(String ipType) {
            this.ipType = ipType;
        }
    }

    public class CAgrAssignor{
        private String agrNo;
        private String oipNameNo;
        private String oipBaseNo;
        private String agrContentNo;

        public String getAgrNo() {
            return agrNo;
        }

        public void setAgrNo(String agrNo) {
            this.agrNo = agrNo;
        }

        public String getOipNameNo() {
            return oipNameNo;
        }

        public void setOipNameNo(String oipNameNo) {
            this.oipNameNo = oipNameNo;
        }

        public String getOipBaseNo() {
            return oipBaseNo;
        }

        public void setOipBaseNo(String oipBaseNo) {
            this.oipBaseNo = oipBaseNo;
        }

        public String getAgrContentNo() {
            return agrContentNo;
        }

        public void setAgrContentNo(String agrContentNo) {
            this.agrContentNo = agrContentNo;
        }
    }

    public class CAgrAssignee{
        private String agrNo;
        private String sipBaseNo;
        private String sipNameNo;
        private String sipRole;
        private String ipType;
        private BigDecimal sipPshare;
        private BigDecimal sipMshare;
        private BigDecimal sipSshare;
        private BigDecimal sipOdshare;
        private BigDecimal sipDbshare;

        public String getAgrNo() {
            return agrNo;
        }

        public void setAgrNo(String agrNo) {
            this.agrNo = agrNo;
        }

        public String getSipNameNo() {
            return sipNameNo;
        }

        public void setSipNameNo(String sipNameNo) {
            this.sipNameNo = sipNameNo;
        }

        public String getSipBaseNo() {
            return sipBaseNo;
        }

        public void setSipBaseNo(String sipBaseNo) {
            this.sipBaseNo = sipBaseNo;
        }

        public String getSipRole() {
            return sipRole;
        }

        public void setSipRole(String sipRole) {
            this.sipRole = sipRole;
        }

        public String getIpType() {
            return ipType;
        }

        public void setIpType(String ipType) {
            this.ipType = ipType;
        }

        public BigDecimal getSipPshare() {
            return sipPshare;
        }

        public void setSipPshare(BigDecimal sipPshare) {
            this.sipPshare = sipPshare;
        }

        public BigDecimal getSipMshare() {
            return sipMshare;
        }

        public void setSipMshare(BigDecimal sipMshare) {
            this.sipMshare = sipMshare;
        }

        public BigDecimal getSipSshare() {
            return sipSshare;
        }

        public void setSipSshare(BigDecimal sipSshare) {
            this.sipSshare = sipSshare;
        }

        public BigDecimal getSipOdshare() {
            return sipOdshare;
        }

        public void setSipOdshare(BigDecimal sipOdshare) {
            this.sipOdshare = sipOdshare;
        }

        public BigDecimal getSipDbshare() {
            return sipDbshare;
        }

        public void setSipDbshare(BigDecimal sipDbshare) {
            this.sipDbshare = sipDbshare;
        }
    }

    public class CAgrAgreementExtend{
        /**
         * 合约编号
         */
        @Column(name = "agr_no")
        private String agrNo;

        /**
         *
         */
        @Column(name = "worksnum")
        private Long worksnum;

        /**
         *
         */
        @Column(name = "worknum_society")
        private Integer worknumSociety;

        /**
         *
         */
        @Column(name = "ip_base_no")
        private String ipBaseNo;

        /**
         *
         */
        @Column(name = "ip_name_no")
        private String ipNameNo;

        private String indicator;

        private String workWriterRole;

        private Date oriValidTo;

        public String getAgrNo() {
            return agrNo;
        }

        public void setAgrNo(String agrNo) {
            this.agrNo = agrNo;
        }

        public Long getWorksnum() {
            return worksnum;
        }

        public void setWorksnum(Long worksnum) {
            this.worksnum = worksnum;
        }

        public Integer getWorknumSociety() {
            return worknumSociety;
        }

        public void setWorknumSociety(Integer worknumSociety) {
            this.worknumSociety = worknumSociety;
        }

        public String getIpBaseNo() {
            return ipBaseNo;
        }

        public void setIpBaseNo(String ipBaseNo) {
            this.ipBaseNo = ipBaseNo;
        }

        public String getIpNameNo() {
            return ipNameNo;
        }

        public void setIpNameNo(String ipNameNo) {
            this.ipNameNo = ipNameNo;
        }

        public String getIndicator() {
            return indicator;
        }

        public void setIndicator(String indicator) {
            this.indicator = indicator;
        }

        public String getWorkWriterRole() {
            return workWriterRole;
        }

        public void setWorkWriterRole(String workWriterRole) {
            this.workWriterRole = workWriterRole;
        }

        public Date getOriValidTo() {
            return oriValidTo;
        }

        public void setOriValidTo(Date oriValidTo) {
            this.oriValidTo = oriValidTo;
        }
    }

    public class CAgrAgreementTerritory{
        private String agrNo;
        private Integer tisN;
        private String indicator;

        public String getAgrNo() {
            return agrNo;
        }

        public void setAgrNo(String agrNo) {
            this.agrNo = agrNo;
        }

        public Integer getTisN() {
            return tisN;
        }

        public void setTisN(Integer tisN) {
            this.tisN = tisN;
        }

        public String getIndicator() {
            return indicator;
        }

        public void setIndicator(String indicator) {
            this.indicator = indicator;
        }
    }

    public class CAgrContent{
        private String agrNo;
        private Date agrEdate;
        private String autoExtensionInd;
        private String agrType;
        private String preTerm;
        private Date agrSdate;
        // 计算比例使用月份
        private Double month;
        private BigDecimal oipPshare;
        private BigDecimal oipMshare;
        private BigDecimal oipSshare;
        private BigDecimal oipOdshare;
        private BigDecimal oipDbshare;
        private String sd;


        public String getAgrNo() {
            return agrNo;
        }

        public void setAgrNo(String agrNo) {
            this.agrNo = agrNo;
        }

        public Date getAgrEdate() {
            return agrEdate;
        }

        public void setAgrEdate(Date agrEdate) {
            this.agrEdate = agrEdate;
        }

        public String getAutoExtensionInd() {
            return autoExtensionInd;
        }

        public void setAutoExtensionInd(String autoExtensionInd) {
            this.autoExtensionInd = autoExtensionInd;
        }

        public String getAgrType() {
            return agrType;
        }

        public void setAgrType(String agrType) {
            this.agrType = agrType;
        }

        public String getPreTerm() {
            return preTerm;
        }

        public void setPreTerm(String preTerm) {
            this.preTerm = preTerm;
        }

        public Date getAgrSdate() {
            return agrSdate;
        }

        public void setAgrSdate(Date agrSdate) {
            this.agrSdate = agrSdate;
        }

        public Double getMonth() {
            return month;
        }

        public void setMonth(Double month) {
            this.month = month;
        }

        public BigDecimal getOipPshare() {
            return oipPshare;
        }

        public void setOipPshare(BigDecimal oipPshare) {
            this.oipPshare = oipPshare;
        }

        public BigDecimal getOipMshare() {
            return oipMshare;
        }

        public void setOipMshare(BigDecimal oipMshare) {
            this.oipMshare = oipMshare;
        }

        public BigDecimal getOipSshare() {
            return oipSshare;
        }

        public void setOipSshare(BigDecimal oipSshare) {
            this.oipSshare = oipSshare;
        }

        public BigDecimal getOipOdshare() {
            return oipOdshare;
        }

        public void setOipOdshare(BigDecimal oipOdshare) {
            this.oipOdshare = oipOdshare;
        }

        public BigDecimal getOipDbshare() {
            return oipDbshare;
        }

        public void setOipDbshare(BigDecimal oipDbshare) {
            this.oipDbshare = oipDbshare;
        }

        public String getSd() {
            return sd;
        }

        public void setSd(String sd) {
            this.sd = sd;
        }
    }

    public class AgrObject{
        private CAgrAssignor agrAssignor;

        private List<CAgrAssignor> agrAssignorList;

        private List<CAgrAssignee> agrAssigneeList;

        private List<CAgrAgreementExtend> agrAgreementExtendList;

        private List<CAgrAgreementTerritory> agrAgreementTerritoryList;

        private CAgrContent agrContent;

        private CMbrIpName mbrIpName;

        private List<CMbrIpAgreement> mbrIpAgreementList;

        private CMbrIp mbrIp;

        public void init(){
            agrAssignorList = new ArrayList<>();
            agrAssignor = new CAgrAssignor();
            agrAssigneeList = new ArrayList<>();
            agrAgreementExtendList = new ArrayList<>();
            agrAgreementTerritoryList = new ArrayList<>();
            agrContent = new CAgrContent();
            mbrIpName = new CMbrIpName();
            mbrIpAgreementList = new ArrayList<>();
            mbrIp = new CMbrIp();
        }

        public CAgrAssignor getAgrAssignor() {
            return agrAssignor;
        }

        public void setAgrAssignor(CAgrAssignor agrAssignor) {
            this.agrAssignor = agrAssignor;
        }

        public List<CAgrAssignor> getAgrAssignorList() {
            return agrAssignorList;
        }

        public void setAgrAssignorList(List<CAgrAssignor> agrAssignorList) {
            this.agrAssignorList = agrAssignorList;
        }

        public List<CAgrAssignee> getAgrAssigneeList() {
            return agrAssigneeList;
        }

        public void setAgrAssigneeList(List<CAgrAssignee> agrAssigneeList) {
            this.agrAssigneeList = agrAssigneeList;
        }

        public List<CAgrAgreementExtend> getAgrAgreementExtendList() {
            return agrAgreementExtendList;
        }

        public void setAgrAgreementExtendList(List<CAgrAgreementExtend> agrAgreementExtendList) {
            this.agrAgreementExtendList = agrAgreementExtendList;
        }

        public List<CAgrAgreementTerritory> getAgrAgreementTerritoryList() {
            return agrAgreementTerritoryList;
        }

        public void setAgrAgreementTerritoryList(List<CAgrAgreementTerritory> agrAgreementTerritoryList) {
            this.agrAgreementTerritoryList = agrAgreementTerritoryList;
        }

        public CAgrContent getAgrContent() {
            return agrContent;
        }

        public void setAgrContent(CAgrContent agrContent) {
            this.agrContent = agrContent;
        }

        public CMbrIpName getMbrIpName() {
            return mbrIpName;
        }

        public void setMbrIpName(CMbrIpName mbrIpName) {
            this.mbrIpName = mbrIpName;
        }

        public CMbrIp getMbrIp() {
            return mbrIp;
        }

        public void setMbrIp(CMbrIp mbrIp) {
            this.mbrIp = mbrIp;
        }

        public List<CMbrIpAgreement> getMbrIpAgreementList() {
            return mbrIpAgreementList;
        }

        public void setMbrIpAgreementList(List<CMbrIpAgreement> mbrIpAgreementList) {
            this.mbrIpAgreementList = mbrIpAgreementList;
        }
    }

}
