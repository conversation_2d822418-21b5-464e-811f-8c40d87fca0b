package tw.org.must.must.core.analysis.xml.ddex;



import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

@Component
public class AppleMusicReportParser {
    private static CallBack callBack;

    public void setCallBack(CallBack callBack) {
        this.callBack = callBack;
    }

    public static void parse(String filePath) {
        try {
            FileInputStream fileInputStream = new FileInputStream(new File(filePath));
            InputStreamReader isr = new InputStreamReader(fileInputStream);// InputStreamReader 是字节流通向字符流的桥梁,
            BufferedReader br = new BufferedReader(isr);// 从字符输入流中读取文件中的内容,封装了一个new InputStreamReader的对象
            String line;
            int a = 0;
            List<String> listMapKey = null;
            List<Map<String, String>> mapList = new ArrayList<>();
            while ((line = br.readLine()) != null) {
                if (line.startsWith("#")) {
                    listMapKey = dealWithString(line);
                    continue;
                }
                String[] split = line.split("\t");
                if (split.length == listMapKey.size()) {
                    Map<String, String> map = getMapOfSplit(split, listMapKey);
                    mapList.add(map);
                    if (mapList.size() > 100000) {
                        callBack.execute(mapList);
                        mapList = new ArrayList<>();
                        Thread.sleep(1000);
                    }
                } else
                    System.out.println("" + a++ + line);
            }
            callBack.execute(mapList);

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }

    /**
     * @param split
     * @param listMapKey 里面存的是 map的key
     * @return
     */
    private static Map<String, String> getMapOfSplit(String[] split, List<String> listMapKey) {
        Map<String, String> map = new LinkedHashMap<>();
        for (int i = 0; i < listMapKey.size(); i++) {
            map.put(listMapKey.get(i), split[i]);
        }
        return map;
    }

    /**
     * 将map的key 封装到list中
     *
     * @param line
     * @return
     */

    private static List<String> dealWithString(String line) {
        String[] split = line.split("\t");
        List<String> listMapKey = new ArrayList<>();
        for (String s : split)
            listMapKey.add(s);
        return listMapKey;

    }

    public static void main(String[] args) {
        AppleMusicReportParser appleMusicReportParser = new AppleMusicReportParser();
        appleMusicReportParser.setCallBack(new AppleMusicReportParserAfter());
        parse("C:\\Users\\<USER>\\Desktop\\must-建新\\AppleMusic_Usage_2019Q1_TWN_v1.tsv");

    }
}
