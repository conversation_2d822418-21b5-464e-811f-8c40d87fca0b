package tw.org.must.must.core.analysis.xml.ddex;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.core.analysis.xml.AbstractXmlParser;
import tw.org.must.must.core.analysis.xml.ddex.dto.*;
import tw.org.must.must.core.exception.CustomException;
import tw.org.must.must.core.parse.DspDataTemp;
import tw.org.must.must.core.service.claim.ClaimMaxRevenueService;
import tw.org.must.must.core.service.claim.ClaimMinimaInfoService;
import tw.org.must.must.core.service.list.*;
import tw.org.must.must.model.claim.ClaimMaxRevenue;
import tw.org.must.must.model.claim.ClaimMinimaInfo;
import tw.org.must.must.model.list.ListDspFileBase;
import tw.org.must.must.model.list.ListDspFileDataMapping;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 同步解析DDEX文档并入库
 */
@Component
public class DdexParser extends AbstractXmlParser {

    private static final Logger LOGGER = LoggerFactory.getLogger(DdexParser.class);

    private static final int INSERT_SIZE = 5000;


    @Autowired
    private MessageService messageService;


    @Autowired
    private ClaimMinimaInfoService claimMinimaInfoService;


    @Autowired
    private ListDspFileDataMappingService listDspFileDataMappingService;

    @Autowired
    private ListDspFileBaseService listDspFileBaseService;


    @Autowired
    private ClaimMaxRevenueService claimMaxRevenueService;

    private List<Resource> resources = new ArrayList<>();

    private List<Release> releases = new ArrayList<>();

    private List<ReleaseTransaction> releaseTransactions = new ArrayList<>();

    private FileParser fileParser = null;

    private ListDspFileBase listDspFileBase = null;


    /**
     * 转换入库
     */
    public void compute() {
        if (fileParser.getWriteStatus() == 1 && fileParser.getComputeStatus() == -1) {
            try {
                LOGGER.info("开始转换数据......");
                computeStart(listDspFileBase.getId());
                LOGGER.info("结束转换数据......");

                LOGGER.info("开始更新listFileBase id = " + listDspFileBase.getId() + "......");
                updateListDspFileBase();
                LOGGER.info("结束更新listFileBase id = " + listDspFileBase.getId() + "......");

                LOGGER.info("开始创建ClaimMaxRevenue......");
                createClaimMaxRevenue();
                LOGGER.info("结束创建ClaimMaxRevenue......");
//                insertListFile();
            } catch (Exception e) {
                LOGGER.error(listDspFileBase.getFileName() + ": 转换发生错误:", e);
                messageService.deleteCompute(fileParser);
                // LOGGER.error(listDspFileBase.getFileName() + ": 转换清理完毕");
            }
        }
    }


    private void updateListDspFileBase() {
        String filePath = listDspFileBase.getFilePath();
        String filePathMd5 = DigestUtils.md5DigestAsHex(filePath.getBytes());
        MessageHeader messageHeader = messageService.getMessageHeader(filePathMd5);
        SalesReport salesReport = messageService.getSalesReport(filePathMd5);
//        listDspFileBase.setListTypeInfoId(fileParser.getTypeId());
//        listDspFileBase.setListTypeName(fileParser.getTypeName());
//        listDspFileBase.setListFileType(fileParser.getFileType());
//        listDspFileBase.setListFileName(fileParser.getFileName());
        Long fileTotal = listDspFileDataMappingService.selectCountByBaseId(listDspFileBase.getId());

        Long claimSetInfoId = null;

        // 获取claimSetInfoId配置
        String extJson = fileParser.getListFileQueue().getExtJson();
        if (StringUtils.isNotBlank(extJson)) {
            JSONObject object = JSONObject.fromObject(extJson);
            claimSetInfoId = Long.valueOf(object.get("claimSetInfoId") + "");

        }

        if (claimSetInfoId ==null){
            LOGGER.error(listDspFileBase.getFileName() + ": 转换发生错误:未配置claimSetInfoId");
            return;
        }


        listDspFileBase.setListFileTotal(fileTotal);
//        listDspFileBase.setFilePath(fileParser.getFilePath());
//        listDspFileBase.setListParentMd5(fileParser.getFilePathMd5());
//        listDspFileBase.setSource(messageHeader.getSenderPartyName());
        if (null != messageHeader) {
//        	String notificationPeriodStartDate = messageHeader.getNotificationPeriodStartDate();
//        	if(StringUtils.isNotBlank(notificationPeriodStartDate))
//        		listDspFileBase.setListFileStartTime(DateParse.parseDate(messageHeader.getNotificationPeriodStartDate()));
//        	String notificationPeriodEndDate = messageHeader.getNotificationPeriodEndDate();
//        	if(StringUtils.isNotBlank(notificationPeriodEndDate))
//        		listDspFileBase.setListFileEndTime(DateParse.parseDate(messageHeader.getNotificationPeriodEndDate()));
            listDspFileBase.setDspCompany(messageHeader.getSenderPartyName());
            listDspFileBase.setCommercialModel(messageHeader.getComment());
            ClaimMinimaInfo info = new ClaimMinimaInfo();
            info.setCompnyName(messageHeader.getSenderPartyName());
            info.setProductFullName(messageHeader.getTradingName());
            info.setClaimSetId(claimSetInfoId);
            List<ClaimMinimaInfo> claimMinimaInfos = claimMinimaInfoService.list(info);
            if (claimMinimaInfos.size() > 0) {
                listDspFileBase.setClaimMinimaInfoId(claimMinimaInfos.get(0).getId());
            } else {
                // FIXME 如果product不存在，直接在數據庫新建
                ClaimMinimaInfo cmi = new ClaimMinimaInfo();
                cmi.init();
                cmi.setCompnyName(messageHeader.getSenderPartyName());
                cmi.setProductFullName(messageHeader.getTradingName());
                cmi.setProductShortName(messageHeader.getTradingName());
                listDspFileBase.setFilePath(fileParser.getFilePath());

                cmi.setClaimSetId(claimSetInfoId);

                claimMinimaInfoService.add(cmi);
                listDspFileBase.setClaimMinimaInfoId(cmi.getId());
            }
            listDspFileBase.setMinimaCalcStatus(0);

        }



        listDspFileBase.setProductName(messageHeader.getTradingName());
        listDspFileBase.setDistStatus(0);
        listDspFileBase.setDist(0);
        if (null != salesReport) {
            String dspNetRevenueAmount = salesReport.getDspNetRevenueAmount();
            if (StringUtils.isNotBlank(dspNetRevenueAmount)) {
                listDspFileBase.setListTotalRoy(new BigDecimal(salesReport.getDspNetRevenueAmount()));
            } else {
                listDspFileBase.setListTotalRoy(BigDecimal.ZERO);
            }
            String numberOfSubscribersQuantity = salesReport.getNumberOfSubscribersQuantity();
            if (StringUtils.isNotBlank(numberOfSubscribersQuantity)) {
                listDspFileBase.setListSubscribeCount(new BigDecimal(salesReport.getNumberOfSubscribersQuantity()));
            } else {
                listDspFileBase.setListSubscribeCount(BigDecimal.ZERO);
            }
            listDspFileBase.setCurrency(salesReport.getCurrencyOfAccounting());
        }
        listDspFileBase.setListTotalClickCount(fileParser.getTotalClickCount());
        listDspFileBase.setClickNumber(fileParser.getTotalClickCount());
        listDspFileBase.setFileStatus(1);
        listDspFileBase.init();
        listDspFileBaseService.updateSelective(listDspFileBase);
    }

    private void computeStart(Long baseId) {
        fileParser.setComputeStatus(0);
        messageService.updateFileParser(fileParser);
        LOGGER.info(listDspFileBase.getFileName() + ": 开始转换,当前listDspFileBase的id为：" + baseId);
        String filePathMd5 = fileParser.getFilePathMd5();
        int count = messageService.countRelease(filePathMd5);
        int pageNumTotal = (count % INSERT_SIZE == 0) ? (count / INSERT_SIZE) : (count / INSERT_SIZE) + 1;
        LOGGER.info("转换总记录数: " + count + ", 每次: " + INSERT_SIZE + ", 转换次数：" + pageNumTotal);
        MessageHeader messageHeader = messageService.getMessageHeader(filePathMd5);
        // mapping插入动态生成的临时表中
        String tableName = listDspFileDataMappingService.createTemporaryTable(baseId);
        List<DspDataTemp> dspDataTempList = new ArrayList<>(pageNumTotal * INSERT_SIZE); // <fileBaseId, <mapping.id, mapping.clickNumber>>
        for (int i = 1; i <= pageNumTotal; i++) {
            List<Release> releases = messageService.getReleases(filePathMd5, i, INSERT_SIZE);
            List<String> proprietaryIds = releases.stream().map(Release::getProprietaryId).collect(Collectors.toList());
            Set<String> resourceReferences = new HashSet<>();
            releases.forEach(release -> {
                List<String> strs = Arrays.asList(release.getResourceReferences().replaceAll("\\[", "")
                        .replaceAll("]", "").split(","));
                resourceReferences.addAll(strs);
            });
            List<ReleaseTransaction> releaseTransactions = messageService.getReleaseTransactions(filePathMd5, proprietaryIds);
            List<Resource> resources = messageService.getResources(filePathMd5, new ArrayList<>(resourceReferences));
            List<ListDspFileDataMapping> listDspFileDataMappings = getFileDataInfos(baseId, messageHeader, releases, releaseTransactions, resources);
            listDspFileDataMappingService.addListTemporary(tableName, listDspFileDataMappings);
            List<DspDataTemp> transfer = transfer(listDspFileDataMappings);
            dspDataTempList.addAll(transfer);
            LOGGER.info("本次: " + listDspFileDataMappings.size() + ", 转换次数： " + i + "/" + pageNumTotal);
        }
        // 临时表生成完，排序查询后插入正式表，再删除临时表
        if (!dspDataTempList.isEmpty()) {
            long startTime = System.currentTimeMillis();
            List<Long> collect = dspDataTempList.stream().sorted(Comparator.comparing(DspDataTemp::getWorkPrice, Comparator.reverseOrder()).thenComparing(DspDataTemp::getClickNumber, Comparator.reverseOrder())).map(DspDataTemp::getId).collect(Collectors.toList());
            List<List<Long>> partition = Lists.partition(collect, 100000); //控制每次查询和插入量
            long listSize = 0;
            for (List<Long> ids : partition) {
                List<ListDspFileDataMapping> listDspFileDataMappings = listDspFileDataMappingService.selectTemporaryByIds(ids, tableName);
                listDspFileDataMappingService.addList(listDspFileDataMappings);
                listSize += listDspFileDataMappings.size();
                LOGGER.info("临时表【{}】，开始插入正式表, 已插入 size:【{} / {}】", tableName, listSize, collect.size());
            }
            listDspFileDataMappingService.dropTemporaryTable(tableName);
            long consuming = System.currentTimeMillis() - startTime;
            LOGGER.info("临时表【{}】->正式表：【{}】, list size：【{}】", tableName, consuming > 1000 ? (consuming / 1000) + " 秒" : consuming + " 毫秒", collect.size());
        }
        fileParser.setComputeStatus(1);
        LOGGER.info(listDspFileBase.getFileName() + ": 转换完成");
        messageService.updateFileParser(fileParser);
    }

    private void createClaimMaxRevenue() {
        Long minimaInfoId = listDspFileBase.getClaimMinimaInfoId();
        if (minimaInfoId == null) {
            return;
        }
        ClaimMinimaInfo claimMinimaInfo = claimMinimaInfoService.getById(minimaInfoId);
        String filePathMd5 = DigestUtils.md5DigestAsHex(listDspFileBase.getFilePath().getBytes());
        MessageHeader messageHeader = messageService.getMessageHeader(filePathMd5);
        SalesReport salesReport = messageService.getSalesReport(filePathMd5);
        if (messageHeader == null || salesReport == null) {
            return;
        }
        ClaimMaxRevenue claimMaxRevenue = new ClaimMaxRevenue();
        claimMaxRevenue.setLicensor(salesReport.getLicensorPartyName());
        claimMaxRevenue.setStartDate(DateParse.parseDate(messageHeader.getNotificationPeriodStartDate()));
        claimMaxRevenue.setEndDate(DateParse.parseDate(messageHeader.getNotificationPeriodEndDate()));
        claimMaxRevenue.setProduct(claimMinimaInfo.getProductFullName());
        claimMaxRevenue.setTerrCode(salesReport.getTerritoryCode());
        claimMaxRevenue.setCurrency(salesReport.getCurrencyOfAccounting());
        claimMaxRevenue.setTotalTracks(listDspFileBase.getListTotalClickCount());
        claimMaxRevenue.setRevenueShare(claimMinimaInfo.getTotalShare());
        BigDecimal listSubScribeCount = listDspFileBase.getListSubscribeCount();
        if (null == listSubScribeCount) {
            listSubScribeCount = BigDecimal.ZERO;
        }
        claimMaxRevenue.setSubscriberNumber(listSubScribeCount);
        BigDecimal formulaPublicMechanical = claimMinimaInfo.getFormulaPublicMechanical();
        if (formulaPublicMechanical == null) {
            formulaPublicMechanical = BigDecimal.ZERO;
        }
        BigDecimal minima = formulaPublicMechanical.multiply(listSubScribeCount);
        claimMaxRevenue.setMiniClaimRoy(minima);
        BigDecimal listTotalRoy = listDspFileBase.getListTotalRoy();
        if (null == listTotalRoy) {
            listTotalRoy = BigDecimal.ZERO;
        }
        BigDecimal licensor = listTotalRoy.multiply(claimMinimaInfo.getTotalShare() == null ? BigDecimal.ZERO : claimMinimaInfo.getTotalShare()).divide(BigDecimal.valueOf(100), 5, BigDecimal.ROUND_HALF_UP);
        claimMaxRevenue.setLicnClaimRoy(licensor);
        BigDecimal claimRoy = BigDecimal.ZERO;
        if (minima.compareTo(licensor) > -1) {
            claimRoy = minima;
            claimMaxRevenue.setClaimType("M");
        } else {
            claimRoy = licensor;
            claimMaxRevenue.setClaimType("P");
        }
        claimMaxRevenue.setClaimRoy(claimRoy);
        claimMaxRevenue.setNetRevenue(listTotalRoy);
        BigDecimal listTotalClickCount = listDspFileBase.getListTotalClickCount();
        if (null == listTotalClickCount) {
            listTotalClickCount = BigDecimal.ZERO;
        }
        claimMaxRevenue.setUnitPrice(claimRoy.divide(listTotalClickCount, 5, BigDecimal.ROUND_HALF_UP));
        claimMaxRevenue.setListFileId(listDspFileBase.getId());
        claimMaxRevenue.init();
        claimMaxRevenueService.add(claimMaxRevenue);
    }


    private List<ListDspFileDataMapping> getFileDataInfos(Long baseId, MessageHeader messageHeader,
                                                          List<Release> releases,
                                                          List<ReleaseTransaction> releaseTransactions,
                                                          List<Resource> resources) {
        List<ListDspFileDataMapping> listDspFileDataMappings = new ArrayList<ListDspFileDataMapping>();
        Map<String, ReleaseTransaction> releaseTransactionMap = new HashMap<>(releaseTransactions.size());
        releaseTransactions.forEach(releaseTransaction -> releaseTransactionMap.put(releaseTransaction.getProprietaryId(), releaseTransaction));
        Map<String, Resource> resourceMap = new HashMap<>(resources.size());
        resources.forEach(resource -> resourceMap.put(resource.getResourceReference(), resource));
        if (null != releases && releases.size() > 0) {
            releases.forEach(release -> {
                String proprietaryId = release.getProprietaryId();
                String[] resourceReferences = release.getResourceReferences().replaceAll("\\[", "")
                        .replaceAll("]", "").split(",");
                ReleaseTransaction releaseTransaction = releaseTransactionMap.get(proprietaryId);
                List<Resource> resources1 = new ArrayList<>();
                if (null != resourceReferences) {
                    for (String resourceReference : resourceReferences) {
                        Resource resource = resourceMap.get(resourceReference);
                        if (null != resource) {
                            resources1.add(resourceMap.get(resourceReference));
                        }
                    }
                }
                AtomicInteger durationTotle = new AtomicInteger();
                if (resources1.size() > 0) {
                    resources1.forEach(resource -> {
                        String durationStr = resource.getDuration();
                        durationTotle.addAndGet(getDuration(durationStr));


                        List<Artist> artists = JSON.parseArray(resource.getDisplayArtists(), Artist.class);
                        List<Artist> indirectContributors = JSON.parseArray(resource.getIndirectContributors(), Artist.class);
                        if (artists == null) {
                            artists = new ArrayList<>();
                        }
                        if (indirectContributors != null && indirectContributors.size() > 0) {
                            artists.addAll(indirectContributors);
                        }
                        StringBuilder workArtist = new StringBuilder();
                        StringBuilder author = new StringBuilder();
                        StringBuilder composer = new StringBuilder();
                        Set<String> names = new HashSet<>();
                        artists.forEach(artist -> {
                            String name = artist.getName();
                            if (!names.contains(name)) {
                                if (workArtist.length() > 1) {
                                    workArtist.append(";");
                                }
                                workArtist.append(name);
                                names.add(name);
                            }
                            if (artist.getRole() != null && ("lyricist".equals(artist.getRole().toLowerCase()) || "author".equals(artist.getRole().toLowerCase()))) {
                                if (author.length() > 1) {
                                    author.append(";");
                                }
                                author.append(name);
                            }
                            if (artist.getRole() != null && "composer".equals(artist.getRole().toLowerCase())) {
                                if (composer.length() > 1) {
                                    composer.append(";");
                                }
                                composer.append(name);
                            }
                        });

                        // 统计总的点击数
                        BigDecimal totalClickCount = fileParser.getTotalClickCount();
                        if (null == totalClickCount) {
                            totalClickCount = BigDecimal.ZERO;
                        }

                        // list_dsp_file_data_mapping 表
                        ListDspFileDataMapping listDspFileDataMapping = new ListDspFileDataMapping();
                        listDspFileDataMapping.setFileBaseId(baseId);
                        listDspFileDataMapping.setTitle(resource.getReferenceTitle());
                        listDspFileDataMapping.setWorkArtist(workArtist.toString());
                        listDspFileDataMapping.setAuthor(author.toString());
                        listDspFileDataMapping.setComposer(composer.toString());
                        listDspFileDataMapping.setWorkType(release.getReleaseType());
                        BigDecimal clickNumber = BigDecimal.ZERO;
                        BigDecimal freeClickNumber = BigDecimal.ZERO;
                        if (null != releaseTransaction) {
                            String numberOfConsumerSalesGross = releaseTransaction.getNumberOfConsumerSalesGross();
                            if (StringUtils.isNotBlank(numberOfConsumerSalesGross)) {
                                clickNumber = new BigDecimal(releaseTransaction.getNumberOfConsumerSalesGross());
                                totalClickCount = totalClickCount.add(clickNumber);
                            }

                            String numberOfFreeUnitsToConsumers = releaseTransaction.getNumberOfFreeUnitsToConsumers();
                            if (StringUtils.isNotBlank(numberOfFreeUnitsToConsumers)) {
                                freeClickNumber = new BigDecimal(releaseTransaction.getNumberOfFreeUnitsToConsumers());
                                totalClickCount = totalClickCount.add(freeClickNumber);
                            }
                            listDspFileDataMapping.setUsage(releaseTransaction.getUseType());
                            listDspFileDataMapping.setWorkPrice(StringUtils.isBlank(releaseTransaction.getPriceConsumerPaidExcSalesTax()) ? null : new BigDecimal(releaseTransaction.getPriceConsumerPaidExcSalesTax()));
                            listDspFileBase.setUseType(releaseTransaction.getUseType());
                            listDspFileDataMapping.setSalesTransactionId(releaseTransaction.getTransactionId());
                        }

                        listDspFileDataMapping.setClickNumber(clickNumber);
                        listDspFileDataMapping.setFreeClickNumber(freeClickNumber);

                        // 重新设置回去
                        fileParser.setTotalClickCount(totalClickCount);
                        listDspFileDataMapping.setIsrc(resource.getIsrc());
                        listDspFileDataMapping.setIswc(resource.getIswc());

                        listDspFileDataMapping.setDurationStr(resource.getDuration());
                        listDspFileDataMapping.setDurationM(getDurationM(resource.getDuration()));
                        listDspFileDataMapping.setDurationS(getDurationS(resource.getDuration()));
                        listDspFileDataMapping.setUseArea(resource.getTerritoryCode());
                        listDspFileBase.setListFileStartTime(messageHeader.getNotificationPeriodStartDate() == null ? null : DateParse.parseDate(messageHeader.getNotificationPeriodStartDate(), "yyyy-MM-dd"));
                        listDspFileBase.setListFileEndTime(messageHeader.getNotificationPeriodEndDate() == null ? null : DateParse.parseDate(messageHeader.getNotificationPeriodEndDate(), "yyyy-MM-dd"));
                        listDspFileDataMapping.setListFileStartTime(messageHeader.getNotificationPeriodStartDate() == null ? null : DateParse.parseDate(messageHeader.getNotificationPeriodStartDate(), "yyyy-MM-dd"));
                        listDspFileDataMapping.setListFileEndTime(messageHeader.getNotificationPeriodEndDate() == null ? null : DateParse.parseDate(messageHeader.getNotificationPeriodEndDate(), "yyyy-MM-dd"));
                        listDspFileDataMapping.setWorkSource(messageHeader.getSenderPartyName() + "ddex");

                        listDspFileDataMapping.setListParentMd5(messageHeader.getFilePathMd5());
//            			listDspFileDataMapping.setGroupCode(release.getProprietaryId());
                        listDspFileDataMapping.setCompany(messageHeader.getSenderPartyName());
                        listDspFileDataMapping.setBusinessId(resource.getProprietaryId());
                        listDspFileDataMapping.setResourceId(resource.getProprietaryId());
                        listDspFileDataMapping.setReleaseId(release.getProprietaryId());
                        listDspFileDataMapping.setProduct(messageHeader.getTradingName());
                        listDspFileDataMapping.setAlbumTitle(release.getReferenceTitle());

                        if(listDspFileBase.getMatchMark().equals("N")){
                            listDspFileDataMapping.setStatus(5);
                        }else{
                            listDspFileDataMapping.setStatus(0);
                        }

                        //生成dataUniqueKey 和 dataUniqueKeyStr
                        setUniqueKey(listDspFileDataMapping);
                        BigDecimal musicShare = BigDecimal.ZERO;
                        if (resources1.size() > 1) {
                            int duration = getDuration(resource.getDuration());
                            int durationTotleGet = durationTotle.get();
                            if (durationTotleGet != 0) {
                                musicShare = BigDecimal.valueOf((duration * 1.00 / durationTotleGet) * 100);
                            }
                        } else {
                            musicShare = new BigDecimal(100.00);
                        }
                        listDspFileDataMapping.setMusicShare(musicShare);

                        // 20211101客户Iris要求点击次数大于0的数据才需要
                        if (clickNumber.compareTo(BigDecimal.ZERO)==0){
                            return;
                        }

                        listDspFileDataMapping.setDistRatio(clickNumber.add(freeClickNumber)
                                .multiply(musicShare).divide(BigDecimal.valueOf(100), 5, BigDecimal.ROUND_HALF_UP));
                        listDspFileDataMapping.init();
                        listDspFileDataMappings.add(listDspFileDataMapping);
                    });
                }
            });
        }
        return listDspFileDataMappings;
    }


    private static int getDuration(String duration) {
        if (!duration.contains("PT")) {
            return 0;
        }
        int m = 0;
        int s = 0;
        if (duration.contains("M")) {
            if (duration.contains("H")) {
                m = Integer.parseInt(duration.substring(duration.indexOf("H") + 1, duration.indexOf("M")));
            } else if (duration.contains("T")) {
                m = Integer.parseInt(duration.substring(duration.indexOf("T") + 1, duration.indexOf("M")));
            }
            if (duration.contains("S")) {
                s = Integer.parseInt(duration.substring(duration.indexOf("M") + 1, duration.indexOf("S")));
            }
        } else if (duration.contains("S")) {
            if (duration.contains("H")) {
                s = Integer.parseInt(duration.substring(duration.indexOf("H") + 1, duration.indexOf("S")));
            } else {
                s = Integer.parseInt(duration.substring(duration.indexOf("T") + 1, duration.indexOf("S")));
            }
        }
        return m * 60 + s;
    }

    private static int getDurationM(String duration) {
        if (!duration.contains("PT")) {
            return 0;
        }
        int m = 0;
        if (duration.contains("M")) {
            if (duration.contains("H")) {
                m = Integer.parseInt(duration.substring(duration.indexOf("H") + 1, duration.indexOf("M")));
            } else {
                m = Integer.parseInt(duration.substring(duration.indexOf("T") + 1, duration.indexOf("M")));
            }
        }
        return m;
    }

    private static int getDurationS(String duration) {
        if (!duration.contains("PT")) {
            return 0;
        }
        int s = 0;
        if (duration.contains("S")) {
            if (duration.contains("M")) {
                s = Integer.parseInt(duration.substring(duration.indexOf("M") + 1, duration.indexOf("S")));
            } else if (duration.contains("H")) {
                s = Integer.parseInt(duration.substring(duration.indexOf("H") + 1, duration.indexOf("S")));
            } else {
                s = Integer.parseInt(duration.substring(duration.indexOf("T") + 1, duration.indexOf("S")));
            }
        }
        return s;
    }

    @Override
    protected void clean(String msg) {
        fileParser.setReadStatus(-1);
        fileParser.setWriteStatus(-1);
        messageService.deleteMessage(fileParser);
        LOGGER.info(fileParser.getFileName() + ": 读取清理完毕");
    }

    private void resourcesInsert() {
        if (resources.size() >= INSERT_SIZE) {
            messageService.insertResources(resources);
            resources.clear();
        }
    }

    private void releasesInsert() {
        if (releases.size() >= INSERT_SIZE) {
            messageService.insertReleases(releases);
            releases.clear();
        }
    }

    private void releaseTransactionsInsert() {
        if (releaseTransactions.size() >= INSERT_SIZE) {
            messageService.insertReleaseTransactions(releaseTransactions);
            releaseTransactions.clear();
        }
    }

    @Override
    protected void before() {
        if (fileParser == null) {
            LOGGER.info("文件记录未添加");
            throw new CustomException("文件记录未添加", null);
        } else if (fileParser.getWriteStatus() == 1) {
            LOGGER.info("该文档已入库");
            throw new CustomException("该文档已入库", null);
        } else {
            fileParser.setReadStatus(0);
            messageService.updateFileParser(fileParser);
        }
        resources.clear();
        releases.clear();
        releaseTransactions.clear();
    }

    @Override
    protected void after() {
        fileParser.setReadStatus(1);
        LOGGER.info(fileParser.getFileName() + ": 已读取完！");
        messageService.updateFileParser(fileParser);
        if (!resources.isEmpty()) {
            messageService.insertResources(resources);
            resources.clear();
        }
        if (!releases.isEmpty()) {
            messageService.insertReleases(releases);
            releases.clear();
        }
        if (!releaseTransactions.isEmpty()) {
            messageService.insertReleaseTransactions(releaseTransactions);
            releaseTransactions.clear();
        }
        fileParser.setWriteStatus(1);
        LOGGER.info(fileParser.getFileName() + ": 已入库完！");
        messageService.updateFileParser(fileParser);
    }

    @Override
    protected void send(LinkedList<String> nodeList, Map<String, Object> resultMap) {
        String node = nodeList.peek();
        switch (Objects.requireNonNull(node)) {
            case "SalesReportToSocietyMessage":
                setEnd(true);
                break;
            case "MessageNotificationPeriod":
                messageService.insertHeader(((MessageHeader) resultMap.get("messageHeader")));
                LOGGER.info(fileParser.getFileName() + ": 已开始写入");
                break;
            case "SoundRecording":
                ((Resource) resultMap.get("resource")).setDisplayArtists();
                ((Resource) resultMap.get("resource")).setIndirectContributors();
                resources.add(((Resource) resultMap.get("resource")));
                resourcesInsert();
                break;
            case "Video":
                ((Resource) resultMap.get("resource")).setDisplayArtists();
                ((Resource) resultMap.get("resource")).setIndirectContributors();
                resources.add(((Resource) resultMap.get("resource")));
                resourcesInsert();
                break;
            case "Release":
                ((Release) resultMap.get("release")).setResourceReferences();
                ((Release) resultMap.get("release")).setDisplayArtists();
                releases.add(((Release) resultMap.get("release")));
                releasesInsert();
                break;
            case "ReleaseTransactions":
                releaseTransactions.add(((ReleaseTransaction) resultMap.get("releaseTransaction")));
                releaseTransactionsInsert();
                break;
            case "SalesReport":
                messageService.insertSalesReport((SalesReport) resultMap.get("salesReport"));
                break;
            default:
                break;
        }
    }

    @Override
    protected void construct(LinkedList<String> nodeList, Map<String, Object> resultMap) {
        String node = nodeList.peek();
        switch (Objects.requireNonNull(node)) {
            case "SalesReportToSocietyMessage":
                setStart(true);
                break;
            case "MessageHeader":
                resultMap.put("messageHeader", new MessageHeader(fileParser.getFilePath(), fileParser.getFilePathMd5()));
                break;
            case "SoundRecording":
                resultMap.put("resource", new Resource(new LinkedList<>(), new LinkedList<>(), fileParser.getFilePathMd5()));
                break;
            case "Video":
                resultMap.put("resource", new Resource(new LinkedList<>(), new LinkedList<>(), fileParser.getFilePathMd5()));
                break;
            case "DisplayArtist":
                constructDisplayArtist(nodeList, resultMap);
                break;
            case "IndirectResourceContributor":
                ((Resource) Objects.requireNonNull(resultMap.get("resource"))).getIndirectContributor().push(new Artist());
                break;
            case "Release":
                resultMap.put("release", new Release(new LinkedList<>(), fileParser.getFilePathMd5()));
                break;
            case "ReleaseResourceReferenceList":
                ((Release) Objects.requireNonNull(resultMap.get("release"))).setReleaseResourceReferenceList(new ArrayList<>());
                break;
            case "ReleaseTransactions":
                resultMap.put("releaseTransaction", new ReleaseTransaction(fileParser.getFilePathMd5()));
                break;
            case "SalesReport":
                resultMap.put("salesReport", new SalesReport(fileParser.getFilePathMd5()));
                break;
            default:
                break;
        }
    }

    @Override
    protected void convert(LinkedList<String> nodeList, Map<String, Object> resultMap, String value, Map<String, String> attributeMap) {
        String node = nodeList.peek();
        switch (Objects.requireNonNull(node)) {
            case "MessageThreadId":
                ((MessageHeader) resultMap.get("messageHeader")).setMessageThreadId(value);
                break;
            case "MessageId":
                ((MessageHeader) resultMap.get("messageHeader")).setMessageId(value);
                break;
            case "PartyId":
                convertPartyId(nodeList, resultMap, value);
                break;
            case "FullName":
                convertFullName(nodeList, resultMap, value);
                break;
            case "TradingName":
                ((MessageHeader) resultMap.get("messageHeader")).setTradingName(value);
                break;
            case "MessageCreatedDateTime":
                ((MessageHeader) resultMap.get("messageHeader")).setMessageCreatedTime(value);
                break;
            case "Comment":
                ((MessageHeader) resultMap.get("messageHeader")).setComment(value);
                break;
            case "StartDate":
                ((MessageHeader) resultMap.get("messageHeader")).setNotificationPeriodStartDate(value);
                break;
            case "EndDate":
                ((MessageHeader) resultMap.get("messageHeader")).setNotificationPeriodEndDate(value);
                break;
            case "ISRC":
                ((Resource) resultMap.get("resource")).setIsrc(value);
                break;
            case "ProprietaryId":
                convertProprietaryId(nodeList, resultMap, value, attributeMap);
                break;
            case "ISWC":
                ((Resource) resultMap.get("resource")).setIswc(value);
                break;
            case "ResourceReference":
                ((Resource) resultMap.get("resource")).setResourceReference(value);
                break;
            case "TitleText":
                convertTitleText(nodeList, resultMap, value);
                break;
            case "Duration":
                ((Resource) resultMap.get("resource")).setDuration(value);
                break;
            case "TerritoryCode":
                convertTerritoryCode(nodeList, resultMap, value);
                break;
            case "ArtistRole":
                convertArtistRole(nodeList, resultMap, value);
                break;
            case "IndirectResourceContributorRole":
                Objects.requireNonNull(((Resource) resultMap.get("resource")).getIndirectContributor().peek()).setRole(value);
                break;
            case "ReleaseReference":
                ((Release) resultMap.get("release")).setReleaseReference(value);
                break;
            case "ReleaseResourceReference":
                ((Release) resultMap.get("release")).getReleaseResourceReferenceList().add(value);
                break;
            case "ReleaseType":
                ((Release) resultMap.get("release")).setReleaseType(value);
                break;
            case "CommercialModelType":
                ((SalesReport) resultMap.get("salesReport")).setCommercialModelType(value);
                break;
            case "CurrencyOfAccounting":
                ((SalesReport) resultMap.get("salesReport")).setCurrencyOfAccounting(value);
                break;
            case "TransactionReleaseReference":
                ((ReleaseTransaction) resultMap.get("releaseTransaction")).setReleaseReference(value);
                break;
            case "UseType":
                convertUseType(nodeList, resultMap, value);
                break;
            case "UserInterfaceType":
                ((ReleaseTransaction) resultMap.get("releaseTransaction")).setUserInterfaceType(value);
                break;
            case "DistributionChannelType":
                convertDistributionChannelType(nodeList, resultMap, value);
                break;
            case "TransactionId":
                ((ReleaseTransaction) resultMap.get("releaseTransaction")).setTransactionId(value);
                break;
            case "NumberOfConsumerSalesGross":
                ((ReleaseTransaction) resultMap.get("releaseTransaction")).setNumberOfConsumerSalesGross(value);
                break;
            case "NumberOfFreeUnitsToConsumers":
                ((ReleaseTransaction) resultMap.get("releaseTransaction")).setNumberOfFreeUnitsToConsumers(value);
                break;
            case "PriceConsumerPaidExcSalesTax":
                ((ReleaseTransaction) resultMap.get("releaseTransaction")).setPriceConsumerPaidExcSalesTax(value);
                break;
            case "Quantity":
                ((SalesReport) resultMap.get("salesReport")).setNumberOfSubscribersQuantity(value);
                break;
            case "Amount":
                ((SalesReport) resultMap.get("salesReport")).setDspNetRevenueAmount(value);
                break;
            case "NumberOfSalesTransactionRecords":
                ((SalesReport) resultMap.get("salesReport")).setNumberOfSalesTransactionRecords(value);
                break;
            default:
                break;
        }
    }

    /**
     * 构造对象-Artist
     */
    private void constructDisplayArtist(LinkedList<String> nodeList, Map<String, Object> resultMap) {
        switch (nodeList.get(1)) {
            case "SoundRecordingDetailsByTerritory":
                ((Resource) Objects.requireNonNull(resultMap.get("resource"))).getDisplayArtist().push(new Artist());
                break;
            case "VideoDetailsByTerritory":
                ((Resource) Objects.requireNonNull(resultMap.get("resource"))).getDisplayArtist().push(new Artist());
                break;
            case "ReleaseDetailsByTerritory":
                ((Release) Objects.requireNonNull(resultMap.get("release"))).getDisplayArtist().push(new Artist());
                break;
            default:
                break;
        }
    }

    /**
     * 填充属性-PartyId
     */
    private void convertPartyId(LinkedList<String> nodeList, Map<String, Object> resultMap, String value) {
        switch (nodeList.get(1)) {
            case "MessageSender":
                ((MessageHeader) resultMap.get("messageHeader")).setSenderPartyId(value);
                break;
            case "MessageRecipient":
                ((MessageHeader) resultMap.get("messageHeader")).setRecipientPartyId(value);
                break;
            case "DSP":
                ((SalesReport) resultMap.get("salesReport")).setDspPartyId(value);
                break;
            case "Licensor":
                ((SalesReport) resultMap.get("salesReport")).setLicensorPartyId(value);
                break;
            default:
                break;
        }

    }

    /**
     * 填充属性-FullName
     */
    private void convertFullName(LinkedList<String> nodeList, Map<String, Object> resultMap, String value) {
        switch (nodeList.get(2)) {
            case "MessageSender":
                ((MessageHeader) resultMap.get("messageHeader")).setSenderPartyName(value);
                break;
            case "MessageRecipient":
                ((MessageHeader) resultMap.get("messageHeader")).setRecipientPartyName(value);
                break;
            case "DisplayArtist":
                switch (nodeList.get(3)) {
                    case "SoundRecordingDetailsByTerritory":
                        Objects.requireNonNull(((Resource) resultMap.get("resource")).getDisplayArtist().peek()).setName(value);
                        break;
                    case "VideoDetailsByTerritory":
                        Objects.requireNonNull(((Resource) resultMap.get("resource")).getDisplayArtist().peek()).setName(value);
                        break;
                    case "ReleaseDetailsByTerritory":
                        Objects.requireNonNull(((Release) resultMap.get("release")).getDisplayArtist().peek()).setName(value);
                        break;
                    default:
                        break;
                }
                break;
            case "IndirectResourceContributor":
                Objects.requireNonNull(((Resource) resultMap.get("resource")).getIndirectContributor().peek()).setName(value);
                break;
            case "DSP":
                ((SalesReport) resultMap.get("salesReport")).setDspPartyName(value);
                break;
            case "Licensor":
                ((SalesReport) resultMap.get("salesReport")).setLicensorPartyName(value);
                break;
            default:
                break;
        }
    }

    /**
     * 填充属性-ProprietaryId
     */
    private void convertProprietaryId(LinkedList<String> nodeList, Map<String, Object> resultMap, String value, Map<String, String> attributeMap) {
        switch (nodeList.get(2)) {
            case "SoundRecording":
                ((Resource) resultMap.get("resource")).setProprietaryId(value);
                ((Resource) resultMap.get("resource")).setProprietaryIdNamespace(attributeMap.get("Namespace"));
                break;
            case "Video":
                ((Resource) resultMap.get("resource")).setProprietaryId(value);
                ((Resource) resultMap.get("resource")).setProprietaryIdNamespace(attributeMap.get("Namespace"));
                break;
            case "Release":
                ((Release) resultMap.get("release")).setProprietaryId(value);
                ((Release) resultMap.get("release")).setProprietaryIdNamespace(attributeMap.get("Namespace"));
                break;
            case "ReleaseTransactions":
                ((ReleaseTransaction) resultMap.get("releaseTransaction")).setProprietaryId(value);
                ((ReleaseTransaction) resultMap.get("releaseTransaction")).setProprietaryIdNamespace(attributeMap.get("Namespace"));
                break;
            default:
                break;
        }
    }

    /**
     * 填充属性-TitleText
     */
    private void convertTitleText(LinkedList<String> nodeList, Map<String, Object> resultMap, String value) {
        switch (nodeList.get(1)) {
            case "ReferenceTitle":
                ((Resource) resultMap.get("resource")).setReferenceTitle(value);
                break;
            case "Title":
                ((Resource) resultMap.get("resource")).setTitle(value);
                break;
            default:
                break;
        }
    }

    /**
     * 填充属性-TerritoryCode
     */
    private void convertTerritoryCode(LinkedList<String> nodeList, Map<String, Object> resultMap, String value) {
        switch (nodeList.get(1)) {
            case "SoundRecordingDetailsByTerritory":
                ((Resource) resultMap.get("resource")).setTerritoryCode(value);
                break;
            case "VideoDetailsByTerritory":
                ((Resource) resultMap.get("resource")).setTerritoryCode(value);
                break;
            case "ReleaseDetailsByTerritory":
                ((Release) resultMap.get("release")).setTerritoryCode(value);
                break;
            case "DSP":
                ((SalesReport) resultMap.get("salesReport")).setDspTerritoryCode(value);
                break;
            case "SalesByTerritory":
                ((SalesReport) resultMap.get("salesReport")).setTerritoryCode(value);
                break;
            default:
                break;
        }
    }

    /**
     * 填充属性-ArtistRole
     */
    private void convertArtistRole(LinkedList<String> nodeList, Map<String, Object> resultMap, String value) {
        switch (nodeList.get(2)) {
            case "SoundRecordingDetailsByTerritory":
                Objects.requireNonNull(((Resource) resultMap.get("resource")).getDisplayArtist().peek()).setRole(value);
                break;
            case "VideoDetailsByTerritory":
                Objects.requireNonNull(((Resource) resultMap.get("resource")).getDisplayArtist().peek()).setRole(value);
                break;
            case "ReleaseDetailsByTerritory":
                Objects.requireNonNull(((Release) resultMap.get("release")).getDisplayArtist().peek()).setRole(value);
                break;
            default:
                break;
        }
    }

    /**
     * 填充属性-UseType
     */
    private void convertUseType(LinkedList<String> nodeList, Map<String, Object> resultMap, String value) {
        switch (nodeList.get(1)) {
            case "SalesTransaction":
                ((ReleaseTransaction) resultMap.get("releaseTransaction")).setUseType(value);
                break;
            case "NumberOfSubscribers":
                ((SalesReport) resultMap.get("salesReport")).setNumberOfSubscribersUseType(value);
                break;
            case "DspNetRevenue":
                ((SalesReport) resultMap.get("salesReport")).setDspNetRevenueUseType(value);
                break;
            default:
                break;
        }
    }

    /**
     * 填充属性-DistributionChannelType
     */
    private void convertDistributionChannelType(LinkedList<String> nodeList, Map<String, Object> resultMap, String value) {
        switch (nodeList.get(1)) {
            case "SalesTransaction":
                ((ReleaseTransaction) resultMap.get("releaseTransaction")).setDistributionChannelType(value);
                break;
            case "NumberOfSubscribers":
                ((SalesReport) resultMap.get("salesReport")).setNumberOfSubscribersDistributionChannelType(value);
                break;
            case "DspNetRevenue":
                ((SalesReport) resultMap.get("salesReport")).setDspNetRevenueDistributionChannelType(value);
                break;
            default:
                break;
        }
    }

    public void setFileParser(FileParser fileParser) {
        this.fileParser = fileParser;
    }

    public void setListDspFileBase(ListDspFileBase listDspFileBase) {
        this.listDspFileBase = listDspFileBase;
    }

    public void clean() {
        messageService.deleteMessage();
    }

    @Override
    public String getCompanyIdentification() {
        return "spotify";
    }
}
