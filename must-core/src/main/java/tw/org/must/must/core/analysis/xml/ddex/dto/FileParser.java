package tw.org.must.must.core.analysis.xml.ddex.dto;

import tw.org.must.must.model.list.ListFileQueue;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */
@Table(name = "`ddex_file_parser`")
public class FileParser {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "file_path")
    private String filePath;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "file_path_md5")
    private String filePathMd5;

    @Column(name = "type_id")
    private Long typeId;

    @Column(name = "type_name")
    private String typeName;

    @Column(name = "file_type")
    private String fileType;

    @Column(name = "read_status")
    private Integer readStatus;

    @Column(name = "write_status")
    private Integer writeStatus;

    @Column(name = "compute_status")
    private Integer computeStatus;

    @Column(name = "create_time")
    private Date createTime;
    
    @Column(name = "total_click_count")
    private BigDecimal totalClickCount;

    @Transient
    private ListFileQueue listFileQueue;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePathMd5() {
        return filePathMd5;
    }

    public void setFilePathMd5(String filePathMd5) {
        this.filePathMd5 = filePathMd5;
    }

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public Integer getReadStatus() {
        return readStatus;
    }

    public void setReadStatus(Integer readStatus) {
        this.readStatus = readStatus;
    }

    public Integer getWriteStatus() {
        return writeStatus;
    }

    public void setWriteStatus(Integer writeStatus) {
        this.writeStatus = writeStatus;
    }

    public Integer getComputeStatus() {
        return computeStatus;
    }

    public void setComputeStatus(Integer computeStatus) {
        this.computeStatus = computeStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

	public BigDecimal getTotalClickCount() {
		return totalClickCount;
	}

	public void setTotalClickCount(BigDecimal totalClickCount) {
		this.totalClickCount = totalClickCount;
	}

    public ListFileQueue getListFileQueue() {
        return listFileQueue;
    }

    public void setListFileQueue(ListFileQueue listFileQueue) {
        this.listFileQueue = listFileQueue;
    }
}
