package tw.org.must.must.core.claim;

import com.firstbrave.api.ccidv14.vo.HeaderRecordV14;
import com.firstbrave.api.ccidv14.vo.InvoiceDetailRecordV14;
import com.firstbrave.api.ccidv14.vo.TrailerRecordV14;
import com.firstbrave.api.parser.CCIDFileEncoder;
import com.xxl.job.core.log.XxlJobLogger;
import groovyjarjarpicocli.CommandLine;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.util.BeanCopyUtil;
import tw.org.must.must.core.claim.filesplit.ClaimOuputFileSplitContent;
import tw.org.must.must.core.claim.filesplit.ClaimOutputStrategyContext;
import tw.org.must.must.core.claim.name.AbstractFileNameBuilder;
import tw.org.must.must.core.service.list.ListDspFileBaseService;
import tw.org.must.must.model.claim.*;
import tw.org.must.must.model.claim.report.ClaimReportRightSummary;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

@Component
@Service
public class ClaimCcidPrintForCcid14 {

    private Logger LOGGER = LoggerFactory.getLogger(ClaimCcidPrintForCcid14.class);

    private static final String CURRENTVERSION = "14.1";
    private ClaimCcidHeader header;


    private ConcurrentHashMap<String, AtomicInteger> totalCountMap;

    private ConcurrentHashMap<String, AtomicReference<BigDecimal>> totalSumAmountLicensorMap;
    private ConcurrentHashMap<String, AtomicReference<BigDecimal>> totalSumAmountUnmatchedMap;
    private ConcurrentHashMap<String, AtomicReference<BigDecimal>> totalSumAmountCopconMap;

    private ConcurrentHashMap<String, AtomicReference<BigDecimal>> canClaimMap;
    private ConcurrentHashMap<String, AtomicReference<BigDecimal>> realClaimMap;
    private ConcurrentHashMap<String, AtomicReference<BigDecimal>> canClaimCountMap;
    private ConcurrentHashMap<String, AtomicReference<BigDecimal>> realClaimCountMap;

    private ConcurrentHashMap<String, FileWriter> writeToFileMap;

    private CCIDFileEncoder ccidFileEncoder;



    public ConcurrentHashMap<String, ClaimCcidHeader> dataFilterFileNameMap;

    public ConcurrentHashMap<String, ClaimReportRightSummary> claimReportRightSummaryConcurrentHashMap;




    private ListDspFileBaseService listDspFileBaseService;





    public ListDspFileBaseService getListDspFileBaseService() {
        return listDspFileBaseService;
    }








    private ClaimOutputStrategyContext claimOutputStrategyContent;
    public ClaimCcidPrintForCcid14() {
    }

    public ClaimCcidPrintForCcid14(ClaimCcidHeader header,ListDspFileBaseService listDspFileBaseService) {
        this.header = header;
        this.header.setVersion(CURRENTVERSION);
        this.ccidFileEncoder = new CCIDFileEncoder();

        this.writeToFileMap = new ConcurrentHashMap<>();
        this.totalCountMap = new ConcurrentHashMap<>();
        this.totalSumAmountLicensorMap = new ConcurrentHashMap<>();
        this.totalSumAmountUnmatchedMap = new ConcurrentHashMap<>();
        this.totalSumAmountCopconMap = new ConcurrentHashMap<>();
        this.dataFilterFileNameMap = new ConcurrentHashMap<>();
        this.canClaimMap = new ConcurrentHashMap<>();
        this.realClaimMap = new ConcurrentHashMap<>();
        this.canClaimCountMap = new ConcurrentHashMap<>();
        this.realClaimCountMap = new ConcurrentHashMap<>();
        this.claimReportRightSummaryConcurrentHashMap = new ConcurrentHashMap<>();
        this.listDspFileBaseService = listDspFileBaseService;

        claimOutputStrategyContent = new ClaimOutputStrategyContext(header,listDspFileBaseService);
    }













    private HeaderRecordV14 claimHeaderchangeToHeaderRecordForV14(ClaimCcidHeader header,ClaimResultCcid ccid) {
        // fixme   包含所有V13的头部信息，新增了7项
        HeaderRecordV14 hd = new HeaderRecordV14();
        hd.setRecordType("HD");
        String fileType = header.getFileType();

        hd.setVersion(header.getVersion());
        hd.setDate((new SimpleDateFormat("yyyyMMdd")).format(new java.util.Date()));
        String sender = header.getSender();

        //长度截取，发送方长度不能超过45个字符
        if (StringUtils.isNotBlank(sender)) {
            if (sender.length() > 45) {
                sender = sender.substring(0, 45);
            }
            hd.setSender(sender);
        }
        hd.setReceiver(header.getRecever());
        hd.setCcidId(header.getCcidNo());
        hd.setTerritory(header.getTerritory());
        hd.setStartDate(header.getStartDate() == null ? "" : new SimpleDateFormat("yyyyMMdd").format(header.getStartDate()));
        hd.setEndDate(header.getStartDate() == null ? "" : new SimpleDateFormat("yyyyMMdd").format(header.getEndDate()));
        hd.setRoyaltyCurrency(header.getRoyaltyCurrency());
        hd.setOriginalRevenueBasisCurrency(header.getOriginalRevenueBasisCurrency()); // 原始文件中的货币
        hd.setConversionRate(header.getConversionRate()); // 货币占比
        hd.setWorkCodeType(header.getWorkCodeType());
        hd.setCcidIdCorrectionReference(header.getCcidIdCorrectionReference());
        hd.setTypeOfClaim(header.getTypeOfClaim());
        hd.setCommercialModel(header.getCommercialModel());
        hd.setServiceDescription(header.getServiceDescription());
        hd.setUseType(header.getUseType());//usage 要通过mapping带入details


        String mechPercSplit = StringUtils.isBlank(header.getMechPercSplit()) ? "0" : new BigDecimal(header.getMechPercSplit()).stripTrailingZeros().toPlainString();// 計算出來的結果需要在乘以100  FIXME 修改日期20200812
        String perfPercSplit = StringUtils.isBlank(header.getPerfPercSplit()) ? "0" : new BigDecimal(header.getPerfPercSplit()).stripTrailingZeros().toPlainString();


        hd.setMechPercSplit(mechPercSplit);//  对应claimMininfo的mechanical_share
        hd.setPerfPercSplit(perfPercSplit);//  对应claimMininfo的public_share
        String recever = StringUtils.isBlank(header.getRecever()) ? "" : header.getRecever();
        recever = recever.toLowerCase();
        if (Constants.SPORIFY.equalsIgnoreCase(recever)) {
            hd.setAux(""); // 规则  前半部分为MUSIC的比例 后半部分为GEMUSIC的比例

            //获取拆分比
            BigDecimal minimaInfoMecShare = StringUtils.isBlank(header.getMinimaInfoMecShare()) ? BigDecimal.ZERO : new BigDecimal(header.getMinimaInfoMecShare());
            BigDecimal minimaInfoPerShare = StringUtils.isBlank(header.getMinimaInfoPerShare()) ? BigDecimal.ZERO : new BigDecimal(header.getMinimaInfoPerShare());
            BigDecimal totalShare = minimaInfoMecShare.add(minimaInfoPerShare);
            if (totalShare.compareTo(BigDecimal.ZERO)>0){
                hd.setMechPercSplit(minimaInfoMecShare.divide(totalShare,6,RoundingMode.HALF_UP).multiply(Constants.MAX_IP_SHARE).stripTrailingZeros().toPlainString());//  对应claimMininfo的mechanical_share
                hd.setPerfPercSplit(minimaInfoPerShare.divide(totalShare,6,RoundingMode.HALF_UP).multiply(Constants.MAX_IP_SHARE).stripTrailingZeros().toPlainString());//  对应claimMininfo的public_share
            }

        } else if (Constants.YOUTUBE.contains(recever.toLowerCase())) {
            if(StringUtils.contains(ccid.getProductName().toLowerCase(),"Audio".toLowerCase())){
                hd.setAux(String.format("%s|%s|%s",ccid.getRoyality(),ccid.getRoyality(),ccid.getRoyality()) );
            } else {
                //	FIXME 20210305,根据ccid v14 youtube guidelines for the subscription services v1.2修改成 agreement rate
                String minimaInfoMecShare = StringUtils.isBlank(header.getMinimaInfoMecShare()) ? "0" : new BigDecimal(header.getMinimaInfoMecShare()).stripTrailingZeros().toPlainString();
                String minimaInfoPerShare = StringUtils.isBlank(header.getMinimaInfoPerShare()) ? "0" : new BigDecimal(header.getMinimaInfoPerShare()).stripTrailingZeros().toPlainString();
                hd.setAux(minimaInfoPerShare + "|" + minimaInfoMecShare);
            }

        }

        return hd;
    }


    /**
     * 获取对应文件输出路径，会涉及多线程操作，所以需要加上锁
     * @param ccid
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    private  synchronized  String getFileWriter(ClaimResultCcid ccid) throws IOException, IllegalAccessException {


        ClaimOuputFileSplitContent claimOuputFileSplitContent = claimOutputStrategyContent.getOutFilePathContent(ccid);
        if (claimOuputFileSplitContent == null) {
            XxlJobLogger.log("warn is not found out file,id"+ccid.getId());
            return null;
        }


        String outFilePath = claimOuputFileSplitContent.getOutFullPath();

        FileWriter fileWriter = writeToFileMap.get(outFilePath);
        if (fileWriter == null) {

            //初始化文件写入，并写入文件头部
            ClaimCcidHeader newCcidHeader = claimOuputFileSplitContent.getClaimCcidHeader();

            //写入文件头
            HeaderRecordV14 headerRecord = claimHeaderchangeToHeaderRecordForV14(newCcidHeader,ccid);
            String writeHeader = ccidFileEncoder.codeByTab(headerRecord);

            XxlJobLogger.log("生成文件：" + outFilePath);
            File outFile = null;

            outFile = new File(outFilePath);
            File parent = outFile.getParentFile();
            if (parent != null && !parent.exists()) {
                parent.mkdirs();
            }
            outFile.createNewFile();

            fileWriter = new FileWriter(new File(outFilePath), false); // 覆盖数据
            fileWriter.write(writeHeader + "\r\n");
            writeToFileMap.put(outFilePath, fileWriter);

            ClaimReportRightSummary claimReportRightSummary = getClaimReportRightSummary(newCcidHeader);

            dataFilterFileNameMap.put(outFilePath, newCcidHeader);// 此处为了获取对应的拆分数据
            claimReportRightSummaryConcurrentHashMap.put(outFilePath, claimReportRightSummary);

        }
        return outFilePath;



    }

    private ClaimReportRightSummary getClaimReportRightSummary(ClaimCcidHeader claimCcidHeader1) {
        ClaimReportRightSummary claimReportRightSummary = new ClaimReportRightSummary();
        claimReportRightSummary.init();
        claimReportRightSummary.setHeaderId(claimCcidHeader1.getId());
        claimReportRightSummary.setStartDate(new SimpleDateFormat("yyyy-MM-dd").format(claimCcidHeader1.getStartDate()));
        claimReportRightSummary.setEndDate(new SimpleDateFormat("yyyy-MM-dd").format(claimCcidHeader1.getEndDate()));
        claimReportRightSummary.setProduct(claimCcidHeader1.getProductFullName());
        claimReportRightSummary.setCurrency(claimCcidHeader1.getOriginalRevenueBasisCurrency());
        claimReportRightSummary.setFileBaseId(claimCcidHeader1.getListFileBaseId());
        claimReportRightSummary.setRevenueShare(claimCcidHeader1.getRevenueShare());
        return claimReportRightSummary;
    }
    public void detailWriteToFileForCcidV14New(ClaimResultCcid ccid) throws Exception {
        String fileWriter = getFileWriter(ccid);

        writeToCcidDetails(ccid,fileWriter);
    }

    private void writeToCcidDetails(ClaimResultCcid ccid, String key) throws IOException, IllegalAccessException {
        InvoiceDetailRecordV14 detailRecord = createDetaiRecordForV14ByResultCcid(ccid);
        if (null == detailRecord) {
            return;
        }
        // 按照规则对 detailRecord 中的栏位数据进行改变 (过滤) 只需要改变 META
        if(header != null && "META".equals(header.getCompany())){
            saveDetailRecord(detailRecord);
        }

        if (StringUtils.isBlank(key)){
            return;
        }
        FileWriter fileWriter = writeToFileMap.get(key);
        if (fileWriter ==null){
            return;
        }

        fileWriter.write(ccidFileEncoder.codeByTab(detailRecord) + "\r\n");
        countTotalValue(key, detailRecord);
        countClaimValue(key, ccid);
    }

    private void saveDetailRecord(InvoiceDetailRecordV14 detailRecord) {
        // 空值检查
        if (detailRecord == null) {
            return;
        }
        try {
            // 1.22栏位claim_licensor_combined
            if (isValA(detailRecord.getClaimLicensorCombined())){
                detailRecord.setClaimLicensorCombined("100");
            }
            // 2.28栏位 claim_work_mech_share ==> CLAIM_LICENSOR_MECH
            if (isValA(detailRecord.getClaimLicensorMech())){
                detailRecord.setClaimLicensorMech("100");
            }
            // 3.29栏位 claim_work_perf_share ==> CLAIM_LICENSOR_PERF
            if (isValA(detailRecord.getClaimLicensorPerf())){
                detailRecord.setClaimLicensorPerf("100");
            }


            // 4.26栏位 claim_not_collected_combined
            if (isValB(detailRecord.getClaimCopconCombined())){
                detailRecord.setClaimNotCollectedCombined("0");
            }
            // 5.44栏位 claim_not_collected_mech
            if (isValB(detailRecord.getClaimCopconMech())){
                detailRecord.setClaimNotCollectedMech("0");
            }
            // 6.45栏位 claim_not_collected_perf
            if (isValB(detailRecord.getClaimNotCollectedrPerf())){
                detailRecord.setClaimNotCollectedrPerf("0");
            }
        } catch (NumberFormatException e) {
            // 处理非法输入
            System.err.println("非法数字格式: " + e.getMessage());
        }
    }

    /**
     * 判断栏位是否在 100—100.015区间之内
     * @param numberStr 栏位值
     * @return
     */
    private boolean isValA(String numberStr) throws NumberFormatException{
        if (StringUtils.isBlank(numberStr) ||  numberStr.trim().isEmpty()){
            return false;
        }
        // 定义下界和上界
        BigDecimal lowerBound = new BigDecimal("100");
        BigDecimal upperBound = new BigDecimal("100.015");
        // 字符串转BigDecimal（去除前后空格）
        BigDecimal number = new BigDecimal(numberStr.trim());
        // 判断区间：number >= 100 && number < 100.015
        return (number.compareTo(lowerBound) >= 0)
                && (number.compareTo(upperBound) < 0);
    }

    /**
     * 判断栏位是否大于 -0.015
     * @param numberStr 栏位值
     * @return
     */
    private boolean isValB(String numberStr) throws NumberFormatException{
        if (StringUtils.isBlank(numberStr) ||  numberStr.trim().isEmpty()){
            return false;
        }
        // 字符串转BigDecimal（去除前后空格）
        BigDecimal number = new BigDecimal(numberStr.trim());
        // 判断区间：number > -0.015
        return number.compareTo(new BigDecimal("-0.015")) > 0;
    }

    private synchronized void countClaimValue(String key, ClaimResultCcid ccid) {
        AtomicReference<BigDecimal> canAtomicReference = canClaimMap.get(key);
        String pubName = ccid.getPubName();
        BigDecimal revenueBasis = ccid.getRevenueBasis();
        BigDecimal mustTotalShare = ccid.getMustTotalShare();
        BigDecimal claimLicensorPerf = ccid.getClaimLicensorPerf();
        BigDecimal claimLicensorMech = ccid.getClaimLicensorMech();
        BigDecimal mustCanClaimBasic = revenueBasis.multiply(mustTotalShare);
        BigDecimal mustCanClaimPerf = mustCanClaimBasic.multiply(claimLicensorPerf);
        BigDecimal mustCanClaimMech = mustCanClaimBasic.multiply(claimLicensorMech);
        if (null == canAtomicReference) {
            canAtomicReference = new AtomicReference<>(BigDecimal.ZERO);
        }
        BigDecimal oldValue = canAtomicReference.get();
        if ("MUSTPR".equalsIgnoreCase(pubName)) {
            canAtomicReference.getAndSet(oldValue.add(mustCanClaimPerf));
        } else {
            canAtomicReference.getAndSet(oldValue.add(mustCanClaimMech));
        }
        canClaimMap.put(key, canAtomicReference);

        BigDecimal amountLicensorPerf = ccid.getAmountLicensorPerf();
        BigDecimal amountLicensorMech = ccid.getAmountLicensorMech();
        AtomicReference<BigDecimal> realAtomicReference = realClaimMap.get(key);
        if (null == realAtomicReference) {
            realAtomicReference = new AtomicReference<>(BigDecimal.ZERO);
        }
        BigDecimal realOldValue = realAtomicReference.get();

        if ("MUSTPR".equalsIgnoreCase(pubName)) {
            realAtomicReference.getAndSet(realOldValue.add(amountLicensorPerf));
        } else {
            realAtomicReference.getAndSet(realOldValue.add(amountLicensorMech));
        }
        realClaimMap.put(pubName, realAtomicReference);

        AtomicReference<BigDecimal> canClaimCount = canClaimCountMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : canClaimCountMap.get(key);
        BigDecimal originalUseQuantity = ccid.getOriginalUseQuantity();
        if (null != originalUseQuantity) {
            BigDecimal canClaimOldValue = canClaimCount.get() == null ? BigDecimal.ZERO : canClaimCount.get();
            canClaimCount.getAndSet(canClaimOldValue.add(originalUseQuantity));
        }
        canClaimCountMap.put(key, canClaimCount);

        String claimFlag = ccid.getClaimFlag();
        if (!"R".equalsIgnoreCase(claimFlag)) {
            AtomicReference<BigDecimal> realClaimCount = realClaimCountMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : realClaimCountMap.get(key);
            BigDecimal realQuantity = ccid.getOriginalUseQuantity();
            if (null != realQuantity) {
                BigDecimal realClaimOldValue = realClaimCount.get() == null ? BigDecimal.ZERO : realClaimCount.get();
                realClaimCount.getAndSet(realClaimOldValue.add(realQuantity));
            }
            realClaimCountMap.put(key, realClaimCount);
        }
    }

    private synchronized void countTotalValue(String key, InvoiceDetailRecordV14 detailRecord) {
        AtomicInteger atomicInteger = totalCountMap.get(key) == null ? new AtomicInteger(0) : totalCountMap.get(key);

        AtomicReference<BigDecimal> totalSumAmountLicensor = totalSumAmountLicensorMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : totalSumAmountLicensorMap.get(key);
        AtomicReference<BigDecimal> totalSumAmountUnmatched = totalSumAmountUnmatchedMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : totalSumAmountUnmatchedMap.get(key);
        AtomicReference<BigDecimal> totalSumAmountCopcon = totalSumAmountCopconMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : totalSumAmountCopconMap.get(key);

        String amountLicensorPerf = detailRecord.getAmountLicensorPerf();
        if (StringUtils.isNotBlank(amountLicensorPerf)) {
            BigDecimal oldValue = totalSumAmountLicensor.get();
            totalSumAmountLicensor.getAndSet(oldValue.add(new BigDecimal(amountLicensorPerf).setScale(6, RoundingMode.HALF_UP)));
        }
        String amountLicensorMech = detailRecord.getAmountLicensorMech();
        if (StringUtils.isNotBlank(amountLicensorMech)) {
            BigDecimal oldValue = totalSumAmountLicensor.get();
            totalSumAmountLicensor.getAndSet(oldValue.add(new BigDecimal(amountLicensorMech).setScale(6, RoundingMode.HALF_UP)));
        }


        String amountUnmatchedMech = detailRecord.getAmountUnmatchedMech();
        if (StringUtils.isNotBlank(amountUnmatchedMech)) {
            BigDecimal oldValue = totalSumAmountUnmatched.get();
            totalSumAmountUnmatched.getAndSet(oldValue.add(new BigDecimal(amountUnmatchedMech).setScale(6, RoundingMode.HALF_UP)));
        }

        String amountUnmatchedPerf = detailRecord.getAmountUnmatchedPerf();
        if (StringUtils.isNotBlank(amountUnmatchedPerf)) {
            BigDecimal oldValue = totalSumAmountUnmatched.get();
            totalSumAmountUnmatched.getAndSet(oldValue.add(new BigDecimal(amountUnmatchedPerf).setScale(6, RoundingMode.HALF_UP)));
        }

        String amountCopconMech = detailRecord.getAmountCopconMech();
        if (StringUtils.isNotBlank(amountCopconMech)) {
            BigDecimal oldValue = totalSumAmountCopcon.get();
            totalSumAmountCopcon.getAndSet(oldValue.add(new BigDecimal(amountCopconMech).setScale(6, RoundingMode.HALF_UP)));
        }

        String amountCopconPerf = detailRecord.getAmountCopconPerf();
        if (StringUtils.isNotBlank(amountCopconPerf)) {
            BigDecimal oldValue = totalSumAmountCopcon.get();
            totalSumAmountCopcon.getAndSet(oldValue.add(new BigDecimal(amountCopconPerf).setScale(6, RoundingMode.HALF_UP)));
        }
        atomicInteger.addAndGet(1);
//		LOGGER.info("当前为第"+atomicInteger+"行数据；当前totalSumAmountLicensor : "+totalSumAmountLicensor+";totalSumAmountUnmatched ==== "+totalSumAmountUnmatched+"; totalSumAmountCopcon"+ totalSumAmountCopcon);
        totalSumAmountLicensorMap.put(key, totalSumAmountLicensor);
        totalSumAmountUnmatchedMap.put(key, totalSumAmountUnmatched);
        totalSumAmountCopconMap.put(key, totalSumAmountCopcon);
        totalCountMap.put(key, atomicInteger);
    }


    @SuppressWarnings("deprecation")
    private InvoiceDetailRecordV14 createDetaiRecordForV14ByResultCcid(ClaimResultCcid ccidDetail) {
        // ccid 金额和比例必须小数点 FIXME  暂定保留4位
        InvoiceDetailRecordV14 ivd = new InvoiceDetailRecordV14();
        // 1	RECORD_TYPE		maxSize(2)
        ivd.setRecordType("ID");
        // 2	TRANSACTION_TYPE		maxSize(3)
        ivd.setTransactionType("ORI");
        // 3	REF_ID		maxSize(20)   应MUST要求此处改为我们自己的标识  即detailId  仅spotify进行修改
        String blockId = ccidDetail.getBlockId();
        if (StringUtils.isBlank(blockId)) {
            blockId = ccidDetail.getId() + "";
        }
        ivd.setRefId(blockId);
        // 4 	CORRECTION_REFERENCE	maxSize(20)
        ivd.setCorrectionReference("");
        // 5 	SALES_TRANSACTION_ID	maxSize(60)
        ivd.setSalesTransactionId(ccidDetail.getSalesTransactionId());
        // 6	Work_ID			maxSize(60)
        String workSoc = ccidDetail.getClaimWorkSoc() + "";
        String workId = ccidDetail.getClaimWorkId() + "";
        ivd.setWorkId(workId + "/" + workSoc);
        // 7	RELEASE_ID		maxSize(60)
        ivd.setReleaseId(ccidDetail.getOriginalReleaseId());
        // 8	RESOURCE_ID		maxSize(60)
        ivd.setResourceId(ccidDetail.getOriginalResourceId());
        // 9	ISRC		maxSize(12)
        ivd.setIsrc(ccidDetail.getClaimWorkIsrc());
        // 10	ISWC		maxSize(11)
        ivd.setIswc(ccidDetail.getClaimWorkIswc());
        // 11	WORKCODE	maxSize(39)
        ivd.setWorkcode(workId + "/" + workSoc);
        // 12	WORK_TITLE		maxSize(60)
        ivd.setWorkTitle(ccidDetail.getOriginalTitle());
        // 13 	USE_QUANTITY	maxSize(10)

        //20220415 must要求spotify 使用次数要整数
        if (header !=null && Constants.SPORIFY.equalsIgnoreCase(header.getRecever())){
            ivd.setUseQuantity(ccidDetail.getOriginalUseQuantity().intValue()+"");
        }else if(header !=null && Constants.META.equalsIgnoreCase(header.getCompany())){ // META 要求整数，且最小为1，等于0的时候输出1 第6栏设置为空
            if(ccidDetail.getOriginalUseQuantity().compareTo(BigDecimal.ZERO) == 0){
                ivd.setUseQuantity("1");
            } else {
                ivd.setUseQuantity(ccidDetail.getOriginalUseQuantity().intValue() + "");
            }

            ivd.setWorkId("");
            ivd.setWorkcode(workId + "-" + workSoc);
        }else {
            ivd.setUseQuantity(ccidDetail.getOriginalUseQuantity() + "");
        }


        // 14 	APPLIED_TARIFF  maxSize(10)
        if ("P".equalsIgnoreCase(ccidDetail.getRoyaltyType())) {
            ivd.setAppliedTariff(ccidDetail.getAppliedTariff());
        }
        // 15 	ROYALTY_TYPE	maxSize(2)
        ivd.setRoyaltyType(ccidDetail.getRoyaltyType());
        // 16 	REVENUE_BASIS	maxSize(30)
        ivd.setRevenueBasis(String.format("%.6f", ccidDetail.getRevenueBasis()));
        // 17 	ORIGINAL_RELEASE_REVENUE_BASIS	maxSize(30)
        ivd.setOriginalReleaseRevenueBasis(String.format("%.6f", ccidDetail.getOriginalReleaseRevenueBasis()));
        // 18 	ORIGINAL_RESOURCE_REVENUE_BASIS	maxSize(30)
        ivd.setOriginalResourceRevenueBasis(String.format("%.6f", ccidDetail.getOriginalResourceRevenueBasis()));
        // 19 	ROYALTY	maxSize(30) 基于AMOUNT_INVOICED_TOTAL/USQ_QUANTITY
        if (ccidDetail.getRoyality() == null || ccidDetail.getRoyality().compareTo(BigDecimal.ZERO) == 0) {
            ivd.setRoyality("");
        } else {
            ivd.setRoyality(String.format("%.6f", ccidDetail.getRoyality()));
        }

        // 20	RESOURCE_SHARE	maxSize(6)
        ivd.setResourceShare(ccidDetail.getResourceShare() + "");
        // 21	RESTRICTIONS	maxSize(10)
        ivd.setRestrictions(ccidDetail.getRestrictions());
        // 22	CLAIM_LICENSOR_COMBINED 	maxSize(6)
        ivd.setClaimLicensorCombined(String.format("%.2f", ccidDetail.getClaimLicensorCombined()));
        // 23	CLAIM_COPCON_COMBINED	maxSize(6)
        ivd.setClaimCopconCombined(String.format("%.2f", ccidDetail.getClaimCopconCombined()));
        // 24	CLAIM_UNMATCHED_COMBINED 	maxSize(6)
        ivd.setClaimUnmatchCombined(String.format("%.2f", ccidDetail.getClaimUnmatchCombined()));
        // 25	CLAIM_PD_COMBINED	maxSize(6)
        ivd.setClaimPdCombined(String.format("%.2f", ccidDetail.getClaimPdCombined()));
        // 26	CLAIM_NOT_COLLECTED_COMBINED	maxSize(6)			CLAIM_NOT_COLLECTED_MECH*MECH占比+CLAIM_NOT_COLLECTED_PERF*PERF占比（MUST AVOD中，MECH/PERF占比为50/50
        ivd.setClaimNotCollectedCombined(String.format("%.2f", ccidDetail.getClaimNotCollectedCombined()));
        // 27	AMOUNT_INVOICED_TOTAL	maxSize(30) 	AMOUNT_{LICENSOR,COPCON,UNMATCHED}_{MECH,PERF} 6项总和
        ivd.setAmountInvoicedTotal(String.format("%.6f", ccidDetail.getAmountInvoicedTotal()));
        // 28 	CLAIM_LICENSOR_MECH		maxSize(6)
        ivd.setClaimLicensorMech(String.format("%.2f", ccidDetail.getClaimLicensorMech()));
        // 29	CLAIM_LICENSOR_PERF		maxSize(6)
        ivd.setClaimLicensorPerf(String.format("%.2f", ccidDetail.getClaimLicensorPerf()));
        // 30	AMOUNT_LICENSOR_MECH	maxSize(30)
        ivd.setAmountLicensorMech(String.format("%.6f", ccidDetail.getAmountLicensorMech()));
        // 31	AMOUNT_LICENSOR_PERF	maxSize(30)
        ivd.setAmountLicensorPerf(String.format("%.6f", ccidDetail.getAmountLicensorPerf()));
        // 32	AMOUNT_COPCON_MECH		maxSize(30)
        ivd.setAmountCopconMech(String.format("%.6f", ccidDetail.getAmountCopconMech()));
        // 33	AMOUNT_COPCON_PERF		maxSize(30)
        ivd.setAmountCopconPerf(String.format("%.6f", ccidDetail.getAmountCopconPerf()));
        // 34	AMOUNT_PD_MECH			maxSize(30)
        ivd.setAmountPdMech(String.format("%.6f", ccidDetail.getAmountPdMech()));
        // 35	AMOUNT_PD_PERF			maxSize(30)
        ivd.setAmountPdPerf(String.format("%.6f", ccidDetail.getAmountPdPerf()));
        // 36	AMOUNT_NOT_COLLECTED_MECH	maxSize(30)
        ivd.setAmountNotCollectedMech(String.format("%.6f", ccidDetail.getAmountNotCollectedMech()));
        // 37	AMOUNT_NOT_COLLECTED_PERF	maxSize(30)
        ivd.setAmountNotCollectedPerf(String.format("%.6f", ccidDetail.getAmountNotCollectedPerf()));
        // 38	AMOUNT_UNMATCHED_MECH		maxSize(30)
        ivd.setAmountUnmatchedMech(String.format("%.6f", ccidDetail.getAmountUnmatchedMech()));
        // 39	AMOUNT_UNMATCHED_PERF		maxSize(30)
        ivd.setAmountUnmatchedPerf(String.format("%.6f", ccidDetail.getAmountUnmatchedPerf()));
        // 40	CLAIM_COPCONMECH		maxSize(6)
        ivd.setClaimCopconMech(String.format("%.2f", ccidDetail.getClaimCopconMech()));
        // 41	CLAIM_COPCON_PERF		maxSize(6)
        ivd.setClaimCopconPerf(String.format("%.2f", ccidDetail.getClaimCopconPerf()));
        // 42	CLAIM_PD_MECH			maxSize(6)
        ivd.setClaimPdMech(String.format("%.2f", ccidDetail.getClaimPdMech()));
        // 43	CLAIM_PD_PERF		maxSize(6)
        ivd.setClaimPdPerf(String.format("%.2f", ccidDetail.getClaimPdPerf()));
        // 44	CLAIM_NOT_COLLECTED_MECH		maxSize(6)
        ivd.setClaimNotCollectedMech(String.format("%.2f", ccidDetail.getClaimNotCollectedMech()));
        // 45	CLAIM_NOT_COLLECTEDR_PERF		maxSize(6)
        ivd.setClaimNotCollectedrPerf(String.format("%.2f", ccidDetail.getClaimNotCollectedPerf()));
        // 46	CLAIM_UNMATCHED_MECH		maxSize(6)
        ivd.setClaimUnmatchedMech(String.format("%.2f", ccidDetail.getClaimUnmatchedMech()));
        // 47	CLAIM_UNMATCHED_PERF		maxSize(6)
        ivd.setClaimUnmatchedPerf(String.format("%.2f", ccidDetail.getClaimUnmatchedPerf()));
        return ivd;
    }

    public List<ClaimReportRightSummary> trailerWriteToFileForCcidV14() {
        List<ClaimReportRightSummary> claimReportRightSummaryList = new ArrayList<>();
        //生成文件结尾对象
        Set<String> keySet = writeToFileMap.keySet();
        XxlJobLogger.log("总共有" + keySet.size() + "个文件待写入结尾。");
        try {
            for (String key : keySet) {
                XxlJobLogger.log("当前执行文件：" + key + "的结尾写入开始。。。。。。");

                // 更新  claim_report_right_summary表
                updateClaimReportRightSummary(claimReportRightSummaryList, key);

                TrailerRecordV14 trailerRecord = createTrailerRecordForV14(key);
                FileWriter fileWriter = writeToFileMap.get(key);
                fileWriter.write(ccidFileEncoder.codeByTab(trailerRecord));
                fileWriter.flush();
                fileWriter.close();
                XxlJobLogger.log("当前执行文件：" + key + "的结尾写入结束。。。。。。");
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println(e.getMessage());
        }
        return claimReportRightSummaryList;
    }

    private void updateClaimReportRightSummary(List<ClaimReportRightSummary> claimReportRightSummaryList, String key) {
        ClaimReportRightSummary claimReportRightSummary = claimReportRightSummaryConcurrentHashMap.get(key);
        AtomicReference<BigDecimal> canAtomicReference = canClaimMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : canClaimMap.get(key);
        AtomicReference<BigDecimal> realAtomicReference = realClaimMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : realClaimMap.get(key);
        AtomicReference<BigDecimal> canAtomicReferenceCount = canClaimCountMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : canClaimCountMap.get(key);
        AtomicReference<BigDecimal> realAtomicReferenceCount = realClaimCountMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : realClaimCountMap.get(key);
        AtomicReference<BigDecimal> atomicReference = totalSumAmountLicensorMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : totalSumAmountLicensorMap.get(key);

        BigDecimal canClaimValue = canAtomicReference.get() == null ? BigDecimal.ZERO : canAtomicReference.get();
        BigDecimal realClaimValue = realAtomicReference.get() == null ? BigDecimal.ZERO : realAtomicReference.get();

        BigDecimal canClaimRate = BigDecimal.ZERO;

        if (canClaimValue.compareTo(BigDecimal.ZERO) != 0) {
            canClaimRate = realClaimValue.divide(canClaimValue, 4, RoundingMode.HALF_UP); // 根据文件名称来进行取，所以如果是MUSTPR的话，那么所有的计算都是PERF的，如果是重制的话 那么所有的计算都是重制的
        }

        claimReportRightSummary.setSalesCount(canAtomicReferenceCount.get() == null ? BigDecimal.ZERO : canAtomicReferenceCount.get());
        claimReportRightSummary.setMatchSalesCount(realAtomicReferenceCount.get() == null ? BigDecimal.ZERO : realAtomicReferenceCount.get());
        claimReportRightSummary.setPerRoy(canClaimValue);
        claimReportRightSummary.setMecRoy(canClaimValue);
        claimReportRightSummary.setClaimRoy(atomicReference.get() == null ? BigDecimal.ZERO : atomicReference.get());
        claimReportRightSummary.setPerClaimRoy(realClaimValue);
        claimReportRightSummary.setMecClaimRoy(realClaimValue);
        claimReportRightSummary.setPerClaimRate(canClaimRate);
        claimReportRightSummary.setMecClaimRate(canClaimRate);
        claimReportRightSummaryList.add(claimReportRightSummary);
    }

    private TrailerRecordV14 createTrailerRecordForV14(String key) {
        AtomicInteger atomicInteger = totalCountMap.get(key);
        AtomicReference<BigDecimal> atomicReference = totalSumAmountLicensorMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : totalSumAmountLicensorMap.get(key);
        AtomicReference<BigDecimal> totalSumAmountUnmatched = totalSumAmountUnmatchedMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : totalSumAmountUnmatchedMap.get(key);
        AtomicReference<BigDecimal> totalSumAmountCopcon = totalSumAmountCopconMap.get(key) == null ? new AtomicReference<>(BigDecimal.ZERO) : totalSumAmountCopconMap.get(key);

        TrailerRecordV14 trailerRecord = new TrailerRecordV14();
        trailerRecord.setRecordType("TR");
        trailerRecord.setIdRecords(atomicInteger + "");
        trailerRecord.setDlRecords("0");
        trailerRecord.setSumAmountLicensor(String.format("%.6f", atomicReference.get() == null ? new AtomicReference<>(BigDecimal.ZERO) : atomicReference.get()));
        trailerRecord.setSumAmountCopcon(String.format("%.6f", totalSumAmountUnmatched.get() == null ? new AtomicReference<>(BigDecimal.ZERO) : totalSumAmountUnmatched.get()));
        trailerRecord.setSumAmountUnmatched(String.format("%.6f", totalSumAmountCopcon.get() == null ? new AtomicReference<>(BigDecimal.ZERO) : totalSumAmountCopcon.get()));
        return trailerRecord;
    }






    public void flush() throws IOException {
        for (FileWriter fileWriter : writeToFileMap.values()) {
            fileWriter.flush();
        }
    }
}
