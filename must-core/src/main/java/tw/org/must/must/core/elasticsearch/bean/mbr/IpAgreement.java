package tw.org.must.must.core.elasticsearch.bean.mbr;

import java.text.SimpleDateFormat;
import java.util.Date;

import tw.org.must.must.model.mbr.vo.MbrIpAgreementVO;

public class IpAgreement {

	private String ip_base_no;
	private Integer society_code;
	private String creation_class_code;
	private String right_code;
	private String role_code;

	private String valid_from;
	private String valid_to;
	private String cash_right;
	public IpAgreement() {
		
		
	}
	public IpAgreement(MbrIpAgreementVO mbrIpAgreementVO) {
		this.ip_base_no = mbrIpAgreementVO.getIpBaseNo();
		this.society_code = mbrIpAgreementVO.getSocietyCode();
		this.right_code = mbrIpAgreementVO.getRightCode();
		this.role_code = mbrIpAgreementVO.getRoleCode();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		Date validFrom = mbrIpAgreementVO.getValidFrom();
		if (validFrom != null) {
			this.valid_from = sdf.format(validFrom);
		}
		Date validTo = mbrIpAgreementVO.getValidTo();
		if (validTo != null) {
			this.valid_to = sdf.format(validTo);
		}
		this.cash_right = mbrIpAgreementVO.getCashRight();
	}

	public String getIp_base_no() {
		return ip_base_no;
	}

	public void setIp_base_no(String ip_base_no) {
		this.ip_base_no = ip_base_no;
	}

	public Integer getSociety_code() {
		return society_code;
	}

	public void setSociety_code(Integer society_code) {
		this.society_code = society_code;
	}

	public String getCreation_class_code() {
		return creation_class_code;
	}

	public void setCreation_class_code(String creation_class_code) {
		this.creation_class_code = creation_class_code;
	}

	public String getRight_code() {
		return right_code;
	}

	public void setRight_code(String right_code) {
		this.right_code = right_code;
	}

	public String getRole_code() {
		return role_code;
	}

	public void setRole_code(String role_code) {
		this.role_code = role_code;
	}

	public String getValid_from() {
		return valid_from;
	}

	public void setValid_from(String valid_from) {
		this.valid_from = valid_from;
	}

	public String getValid_to() {
		return valid_to;
	}

	public void setValid_to(String valid_to) {
		this.valid_to = valid_to;
	}

	public String getCash_right() {
		return cash_right;
	}

	public void setCash_right(String cash_right) {
		this.cash_right = cash_right;
	}

}
