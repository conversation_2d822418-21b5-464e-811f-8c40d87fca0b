package tw.org.must.must.core.elasticsearch.bean.wrk;

import tw.org.must.must.model.wrk.WrkIsrc;

public class Isrc {

	private String isrc;
	private String input_soc;

	public Isrc(WrkIsrc wrkIsrc) {
		this.isrc = wrkIsrc.getIsrc() != null ? wrkIsrc.getIsrc() : "";
		this.input_soc = wrkIsrc.getInputSoc() != null ? wrkIsrc.getInputSoc().toString() : "";
	}

	public String getIsrc() {
		return isrc;
	}

	public void setIsrc(String isrc) {
		this.isrc = isrc;
	}

	public String getInput_soc() {
		return input_soc;
	}

	public void setInput_soc(String input_soc) {
		this.input_soc = input_soc;
	}

}
