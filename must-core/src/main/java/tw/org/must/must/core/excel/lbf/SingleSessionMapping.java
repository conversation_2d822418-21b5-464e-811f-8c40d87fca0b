package tw.org.must.must.core.excel.lbf;

import java.util.HashMap;
import java.util.Map;

/**
 * @Desciption:
 * @Authoer: huyong
 * @Date: 04-01 2020
 */
public class SingleSessionMapping {

    // 必须字段
    public static final Map<Object, String> MAPPING = new HashMap<>();

    // 非必须字段
    public static final Map<Object, String> MAPPING_NO_MUST = new HashMap<>();


    //兼容CsvReader工具类读取表格数据转换
    public static final Map<String, String> MAPPING_2 = new HashMap<>();

    static {

        MAPPING.put("主辦/活動名稱", "topic");
        MAPPING.put("編號", "number");
        MAPPING.put("表演日期", "playDate");
        MAPPING.put("曲名(song title)", "songTitle");
        MAPPING.put("原演唱(performance)_OR_演唱(performance)", "performance"); // 包含 _OR_ 的需要特殊处理，两个表头都可以
        MAPPING.put("作曲(composer)", "composer");
        MAPPING.put("作詞(author)", "author");
        MAPPING.put("專輯名稱", "albumName");
        MAPPING.put("使用次數", "userCount");
        MAPPING.put("語言", "language");

        MAPPING_NO_MUST.put("group", "group");
        MAPPING_NO_MUST.put("系統專用1(免填)", "sys1");
        MAPPING_NO_MUST.put("系統專用2(免填)", "sys2");
        MAPPING_NO_MUST.put("系統專用3(免填)", "sys3");




        MAPPING_2.put("主辦/活動名稱", "topic");
        MAPPING_2.put("編號", "number");
        MAPPING_2.put("表演日期", "playDate");
        MAPPING_2.put("曲名(Song Title)", "songTitle");
        MAPPING_2.put("演唱(Performance)", "performance");
        MAPPING_2.put("作曲(Composer)", "composer");
        MAPPING_2.put("作詞(Author)", "author");
        MAPPING_2.put("專輯名稱", "albumName");
        MAPPING_2.put("使用次數", "userCount");
        MAPPING_2.put("語言", "language");




    }
}
