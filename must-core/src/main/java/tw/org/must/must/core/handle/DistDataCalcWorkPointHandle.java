package tw.org.must.must.core.handle;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.util.UsageUtil;
import tw.org.must.must.model.distdata.DistDataCalcWorkPoint;
import tw.org.must.must.model.distmrd.DistMrdSales;
import tw.org.must.must.model.distmrd.DistMrdSalesWork;
import tw.org.must.must.model.list.ListMatchDataBasicDone;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkComponent;
import tw.org.must.must.model.wrk.WrkWorkRight;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 计算积点
 *
 * <AUTHOR>
 */
public class DistDataCalcWorkPointHandle {


    /**
     * 计算积点
     *
     * @param listMatchDataBasicDone
     */
    public List<DistDataCalcWorkPoint> calAccumulationPoint(WrkWork wrkWork, WrkWorkRight wrkWorkRight, String distNo,
                                                                   ListMatchDataBasicDone listMatchDataBasicDone, List<WrkWorkComponent> wrkWorkComponentList,
                                                                   Map<String, String> refWrkUsageMap) {
        // 写入dist_data_calc_work_point
        List<DistDataCalcWorkPoint> distDataCalcWorkPointList = new ArrayList<>();
        if (listMatchDataBasicDone == null) {
            return distDataCalcWorkPointList;
        }
        // 开始计算积点
        String matchWorkType = listMatchDataBasicDone.getMatchWorkType();
        String uploadType = listMatchDataBasicDone.getUploadType();
        Integer durationM = listMatchDataBasicDone.getDurationM();
        Integer durationS = listMatchDataBasicDone.getDurationS();
        BigDecimal clickNumber = listMatchDataBasicDone.getClickNumber();
        if ("AV".equals(matchWorkType)) {
            // 使用 wrkWorkComponentList 计算积点
            for (WrkWorkComponent wrkWorkComponent : wrkWorkComponentList) {
                durationM = wrkWorkComponent.getDurationM();
                durationS = wrkWorkComponent.getDurationS();
                String usageType = wrkWorkComponent.getUsageType();
                String table = refWrkUsageMap.get(usageType);
                // 计算比例
                BigDecimal accumulationPoint = UsageUtil.getAccumulationPoint(durationM, durationS, table, clickNumber);

                DistDataCalcWorkPoint distDataCalcWorkPoint = getDistDataCalcWorkPoint(wrkWork, wrkWorkRight, distNo,
                        listMatchDataBasicDone, wrkWorkComponent, accumulationPoint);
                distDataCalcWorkPoint.setUsage(usageType);
                distDataCalcWorkPointList.add(distDataCalcWorkPoint);
            }

        } else {
            String table = UsageUtil.getTableByUploadType(uploadType);
            BigDecimal accumulationPoint = UsageUtil.getAccumulationPoint(durationM, durationS, table, clickNumber);
            DistDataCalcWorkPoint distDataCalcWorkPoint = getDistDataCalcWorkPoint(wrkWork, wrkWorkRight, distNo,
                    listMatchDataBasicDone, null, accumulationPoint);
            distDataCalcWorkPointList.add(distDataCalcWorkPoint);
        }
        return distDataCalcWorkPointList;
    }

    /**
     * 计算积点
     * @param wrkWork
     * @param wrkWorkRight
     * @param distNo
     * @param listMatchDataBasicDones
     * @param wrkWorkComponentList
     * @param refWrkUsageMap
     * @return
     */
    public static List<DistDataCalcWorkPoint> calAccumulationPoint(WrkWork wrkWork, WrkWorkRight wrkWorkRight, String distNo,
                                                                   List<ListMatchDataBasicDone> listMatchDataBasicDones, List<WrkWorkComponent> wrkWorkComponentList,
                                                                   Map<String, String> refWrkUsageMap) {

        List<DistDataCalcWorkPoint> distDataCalcWorkPointList = new ArrayList<>();
        if (CollectionUtils.isEmpty(listMatchDataBasicDones)) {
            return distDataCalcWorkPointList;
        }
        // 开始计算积点
        for(ListMatchDataBasicDone listMatchDataBasicDone : listMatchDataBasicDones){
            // 开始计算积点
            String matchWorkType = listMatchDataBasicDone.getMatchWorkType();
            String uploadType = listMatchDataBasicDone.getUploadType();
            Integer durationM = listMatchDataBasicDone.getDurationM();
            Integer durationS = listMatchDataBasicDone.getDurationS();
            BigDecimal clickNumber = listMatchDataBasicDone.getClickNumber();
            String usageType = listMatchDataBasicDone.getUsage();
            if ("AV".equals(matchWorkType)) {
                // 使用 wrkWorkComponentList 计算积点
                if(!uploadType.equals("PG") && wrkWorkComponentList.size() > 1){
                    clickNumber = clickNumber.divide(new BigDecimal(wrkWorkComponentList.size()),6,RoundingMode.HALF_UP);
                }
                for (WrkWorkComponent wrkWorkComponent : wrkWorkComponentList) {
                    if(uploadType.equals("PG")){
                        durationM = wrkWorkComponent.getDurationM();
                        durationS = wrkWorkComponent.getDurationS();
                        usageType = wrkWorkComponent.getUsageType();
                        if(StringUtils.isEmpty(usageType)){
                            usageType = wrkWorkComponent.getGenre();
                        }
                    } else {
                        wrkWorkComponent.setDurationS(durationS);
                        wrkWorkComponent.setDurationM(durationM);
                    }

                    String table = refWrkUsageMap.get(usageType);
                    // 计算比例
                    BigDecimal accumulationPoint = UsageUtil.getAccumulationPoint(durationM, durationS, table, clickNumber);

                    DistDataCalcWorkPoint distDataCalcWorkPoint = getDistDataCalcWorkPoint(wrkWork, wrkWorkRight, distNo,
                            listMatchDataBasicDone, wrkWorkComponent, accumulationPoint);
                    distDataCalcWorkPoint.setUsage(usageType);
                    distDataCalcWorkPoint.setCllickNumber(clickNumber);
                    distDataCalcWorkPointList.add(distDataCalcWorkPoint);
                }

            } else {
                String table = UsageUtil.getTableByUploadType(uploadType);
                BigDecimal accumulationPoint = UsageUtil.getAccumulationPoint(durationM, durationS, table, clickNumber);
                DistDataCalcWorkPoint distDataCalcWorkPoint = getDistDataCalcWorkPoint(wrkWork, wrkWorkRight, distNo,
                        listMatchDataBasicDone, null, accumulationPoint);
                distDataCalcWorkPointList.add(distDataCalcWorkPoint);
            }
        }
        return distDataCalcWorkPointList;

    }


    /**
     * 得到DistDataCalcWorkPoint实体
     *
     * @return
     */
    private static DistDataCalcWorkPoint getDistDataCalcWorkPoint(WrkWork wrkWork, WrkWorkRight wrkWorkRight, String distNo,
                                                                  ListMatchDataBasicDone listMatchDataBasicDone, WrkWorkComponent wrkWorkComponent,
                                                                  BigDecimal accumulationPoint) {
        DistDataCalcWorkPoint distDataCalcWorkPoint = new DistDataCalcWorkPoint();
        distDataCalcWorkPoint.init();

        distDataCalcWorkPoint.setDistNo(distNo);

        if (wrkWorkRight != null) {
            distDataCalcWorkPoint.setIsDist(StringUtils.equalsIgnoreCase(wrkWorkRight.getWorkSd(), Constants.STRING_DIST_YES) ? Constants.STRING_DIST_NO : Constants.STRING_DIST_YES);
        } else {
            distDataCalcWorkPoint.setIsDist(Constants.STRING_DIST_YES);
        }

        if(null != wrkWork) {
            distDataCalcWorkPoint.setRefWorkId(wrkWork.getRefWorkId());
            distDataCalcWorkPoint.setRefWorkSociety(wrkWork.getRefWorkSociety());
            distDataCalcWorkPoint.setRefWorkUniqueKey(Constants.getWorkUniqueKey(wrkWork.getRefWorkSociety(), wrkWork.getRefWorkId()));
        }
        distDataCalcWorkPoint.setCategoryCode(listMatchDataBasicDone.getCategoryCode());
        distDataCalcWorkPoint.setPoolRight(listMatchDataBasicDone.getPoolRight());
        distDataCalcWorkPoint.setPoolCode(listMatchDataBasicDone.getPoolCode());
        String matchWorkType = listMatchDataBasicDone.getMatchWorkType();
        if(matchWorkType == null){
            matchWorkType = wrkWork.getWorkType();
        }
        distDataCalcWorkPoint.setWorkType(matchWorkType);
        if ("AV".equals(matchWorkType)) {
            distDataCalcWorkPoint.setAvWorkId(listMatchDataBasicDone.getWorkId());
            distDataCalcWorkPoint.setAvWorksSocietyCode(listMatchDataBasicDone.getWorkSocietyCode());
            distDataCalcWorkPoint.setAvWorkUniqueKey(listMatchDataBasicDone.getWorkUniqueKey());
            distDataCalcWorkPoint.setAvTitleId(listMatchDataBasicDone.getMatchWorkTitleId());
            distDataCalcWorkPoint.setWorkTitleId(wrkWorkComponent.getTitleId());

            distDataCalcWorkPoint.setWorkId(wrkWorkComponent.getComponentWorkId());
            distDataCalcWorkPoint.setWorkSocietyCode(wrkWorkComponent.getComWorkSociety());
            distDataCalcWorkPoint.setWorkUniqueKey(StringUtils.leftPad(wrkWorkComponent.getComWorkSociety() + "", 3, "0") + "-" + wrkWorkComponent.getComponentWorkId());
            distDataCalcWorkPoint.setDurationM(wrkWorkComponent.getDurationM());
            distDataCalcWorkPoint.setDurationS(wrkWorkComponent.getDurationS());
        } else {
            distDataCalcWorkPoint.setWorkId(listMatchDataBasicDone.getWorkId());
            distDataCalcWorkPoint.setWorkSocietyCode(listMatchDataBasicDone.getWorkSocietyCode());
            distDataCalcWorkPoint.setWorkUniqueKey(listMatchDataBasicDone.getWorkUniqueKey());
            distDataCalcWorkPoint.setWorkTitleId(listMatchDataBasicDone.getMatchWorkTitleId());
            distDataCalcWorkPoint.setUsage(listMatchDataBasicDone.getUsage());
            distDataCalcWorkPoint.setDurationM(listMatchDataBasicDone.getDurationM());
            distDataCalcWorkPoint.setDurationS(listMatchDataBasicDone.getDurationS());
            distDataCalcWorkPoint.setCllickNumber(listMatchDataBasicDone.getClickNumber());
        }
        Integer durationM = distDataCalcWorkPoint.getDurationM();
        Integer durationS = distDataCalcWorkPoint.getDurationS();
        Integer durationTotalsS = UsageUtil.getSecond(durationM, durationS);
        distDataCalcWorkPoint.setDurationTotalsS(durationTotalsS.longValue());
        distDataCalcWorkPoint.setPerformTime(listMatchDataBasicDone.getPerformTime());
        distDataCalcWorkPoint.setGrossPoint(accumulationPoint);
        distDataCalcWorkPoint.setFileBaseId(listMatchDataBasicDone.getFileBaseId());
        distDataCalcWorkPoint.setUploadType(listMatchDataBasicDone.getUploadType());
        distDataCalcWorkPoint.setFileMappingId(listMatchDataBasicDone.getFileMappingId());
        return distDataCalcWorkPoint;
    }

    public static DistDataCalcWorkPoint calAccumulationPoint(DistMrdSalesWork distMrdSalesWork, String distNo, DistMrdSales distMrdSales) {
        // 写入dist_data_calc_work_point
        DistDataCalcWorkPoint distDataCalcWorkPoint = new DistDataCalcWorkPoint();
        if (distMrdSalesWork == null) {
            return distDataCalcWorkPoint;
        }
        distDataCalcWorkPoint.init();
        distDataCalcWorkPoint.setFileBaseId(distMrdSales.getId());
        distDataCalcWorkPoint.setFileMappingId(distMrdSalesWork.getSalesDetailId());
        distDataCalcWorkPoint.setDistNo(distNo);
        distDataCalcWorkPoint.setIsDist("Y");
        distDataCalcWorkPoint.setCllickNumber(BigDecimal.ONE);
        distDataCalcWorkPoint.setWorkId(distMrdSalesWork.getWorkId());
        distDataCalcWorkPoint.setWorkSocietyCode(distMrdSalesWork.getWorkSocietyCode());
        distDataCalcWorkPoint.setWorkUniqueKey(distMrdSalesWork.getWorkUniqueKey());
        distDataCalcWorkPoint.setNetPoint(distMrdSalesWork.getGrossWorkRoy().multiply(distMrdSales.getExchangeRate() == null ? BigDecimal.ONE : distMrdSales.getExchangeRate()));
        return distDataCalcWorkPoint;
    }

    // TODO zhy
//    public static List<DistDataCalcWorkPoint> calAccumulationPointByDsp(WrkWork wrkWork, String distNo, ListMatchDataDspDone listMatchDataDspDone, List<WrkWorkComponent> wrkWorkComponentList, Map<String, String> refWrkUsageMap) {
//        // 写入dist_data_calc_work_point
//        List<DistDataCalcWorkPoint> distDataCalcWorkPointList = new ArrayList<>();
//        if (listMatchDataDspDone == null) {
//            return distDataCalcWorkPointList;
//        }
//        // 开始计算积点
//        String matchWorkType = listMatchDataDspDone.getMatchWorkType();
//        String uploadType = listMatchDataDspDone.getWorkType();
//        Integer durationM = listMatchDataDspDone.getDurationM();
//        Integer durationS = listMatchDataDspDone.getDurationS();
//        BigDecimal clickNumber = listMatchDataDspDone.getClickNumber();
//        if ("AV".equals(matchWorkType)) {
//            // 使用 wrkWorkComponentList 计算积点
//            for (WrkWorkComponent wrkWorkComponent : wrkWorkComponentList) {
//                durationM = wrkWorkComponent.getDurationM();
//                durationS = wrkWorkComponent.getDurationS();
//                String usageType = wrkWorkComponent.getUsageType();
//                String table = refWrkUsageMap.get(usageType);
//                // 计算比例
//                double accumulationPoint = UsageUtil.getAccumulationPoint(durationM, durationS, table, clickNumber.intValue());
//
//                DistDataCalcWorkPoint distDataCalcWorkPoint = getDistDataCalcWorkPoint(wrkWork, distNo,
//                        listMatchDataBasicDone, wrkWorkComponent, accumulationPoint);
//                distDataCalcWorkPointList.add(distDataCalcWorkPoint);
//            }
//
//        } else {
//            String table = UsageUtil.getTableByUploadType(uploadType);
//            double accumulationPoint = UsageUtil.getAccumulationPoint(durationM, durationS, table, clickNumber.intValue());
//            DistDataCalcWorkPoint distDataCalcWorkPoint = getDistDataCalcWorkPoint(wrkWork, distNo,
//                    listMatchDataBasicDone, null, accumulationPoint);
//            distDataCalcWorkPointList.add(distDataCalcWorkPoint);
//        }
//        return distDataCalcWorkPointList;
//    }
}


