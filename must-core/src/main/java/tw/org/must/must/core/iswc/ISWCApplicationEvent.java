package tw.org.must.must.core.iswc;

import lombok.Data;
import org.springframework.context.ApplicationEvent;

/**
    *@ClassName: ISWCApplicationEvent
    *@Description: TODO
    *@Author: handa
    *@Date: 2020/5/28 20:22
    */
    

public class ISWCApplicationEvent extends ApplicationEvent {
    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    private String logString;
    public ISWCApplicationEvent(Object source,String logString) {
        super(source);
        this.logString = logString;
    }

    public String getLogString() {
        return logString;
    }
}
