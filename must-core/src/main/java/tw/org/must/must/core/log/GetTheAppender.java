package tw.org.must.must.core.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.filter.LevelFilter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import ch.qos.logback.core.rolling.RollingFileAppender;
import ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy;
import ch.qos.logback.core.util.FileSize;
import ch.qos.logback.core.util.OptionHelper;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import tw.org.must.must.model.sys.SysJobLog;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * @description:
 * @author: handa
 * @time: 2019/12/4 11:12
 */
@Repository
public class GetTheAppender {

    @Value("${root}")
    private String root;

    /**
     * @param jobId
     * @param jobLogId
     * @param name
     * @param level
     * @return
     * @description: 通过传入的名字和级别，动态设置appender
     */
    public RollingFileAppender<ILoggingEvent> getAppender(String jobId, Long jobLogId, String name, String className, Level level) {
        DateFormat format = DateFormat.getDateInstance(DateFormat.MEDIUM, Locale.SIMPLIFIED_CHINESE);
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();

        //这里是可以用来设置appender的，在xml配置文件里面，是这种形式：
        RollingFileAppender<ILoggingEvent> appender = new RollingFileAppender<>();

        //这里设置级别过滤器
        LevelController levelController = new LevelController();
        if (level != null) {
            LevelFilter levelFilter = levelController.getLevelFilter(level);
            levelFilter.start();
            appender.addFilter(levelFilter);
        }



        //设置上下文，每个logger都关联到logger上下文，默认上下文名称为default。
        // 但可以使用<contextName>设置成其他名字，用于区分不同应用程序的记录。一旦设置，不能修改。
        appender.setContext(context);
        //appender的name属性
        appender.setName(jobId.toString().concat("_").concat(className));
        //设置文件名
        appender.setFile(OptionHelper.substVars(root.concat( "/").
                concat(jobId.toString()).concat("_").concat(name).concat("/").
                concat(new SimpleDateFormat("yyyyMMdd").format(new Date())).concat(".log"),context));
        appender.setAppend(true);

        appender.setPrudent(false);

        //设置文件创建时间及大小的类
        SizeAndTimeBasedRollingPolicy<ILoggingEvent> policy = new SizeAndTimeBasedRollingPolicy<>();
        //文件名格式
        String fp = OptionHelper.substVars(root+ "/"+jobId+"_"+name + "/.%d{yyyy-MM-dd}.%i.log",context);
        //最大日志文件大小
        policy.setMaxFileSize(FileSize.valueOf("128MB"));
        //设置文件名模式
        policy.setFileNamePattern(fp);
        //设置最大历史记录为15条
        policy.setMaxHistory(15);
        //设置父节点是appender
        policy.setParent(appender);
        //设置上下文，每个logger都关联到logger上下文，默认上下文名称为default。
        // 但可以使用<contextName>设置成其他名字，用于区分不同应用程序的记录。一旦设置，不能修改。
        policy.setContext(context);
        //总大小限制
        policy.setTotalSizeCap(FileSize.valueOf("32GB"));
        policy.start();

        PatternLayoutEncoder encoder = new PatternLayoutEncoder();
        //设置上下文，每个logger都关联到logger上下文，默认上下文名称为default。
        // 但可以使用<contextName>设置成其他名字，用于区分不同应用程序的记录。一旦设置，不能修改。
        encoder.setContext(context);
        //设置格式
        encoder.setPattern("%d %p (%file:%line\\)- %m%n");
        encoder.start();

        //加入下面两个节点
        appender.setRollingPolicy(policy);
        appender.setEncoder(encoder);
        appender.start();
        return appender;
    }

    /**
    * @Description:  通过传入的名字和级别，动态设置appender
    * @Param: [appendName, rootPath, level]
    * @return: ch.qos.logback.core.rolling.RollingFileAppender<ch.qos.logback.classic.spi.ILoggingEvent>
    * @Author: hanDa
    * @Date: 2021/1/4 16:46
    */
    public RollingFileAppender<ILoggingEvent> getAppender(String appendName, String rootPath,Level level) {
        DateFormat format = DateFormat.getDateInstance(DateFormat.MEDIUM, Locale.SIMPLIFIED_CHINESE);
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();

        //这里是可以用来设置appender的，在xml配置文件里面，是这种形式：
        RollingFileAppender<ILoggingEvent> appender = new RollingFileAppender<>();

        //这里设置级别过滤器
        LevelController levelController = new LevelController();
        if (level != null) {
            LevelFilter levelFilter = levelController.getLevelFilter(level);
            levelFilter.start();
            appender.addFilter(levelFilter);
        }



        //设置上下文，每个logger都关联到logger上下文，默认上下文名称为default。
        // 但可以使用<contextName>设置成其他名字，用于区分不同应用程序的记录。一旦设置，不能修改。
        appender.setContext(context);
        //appender的name属性
        appender.setName(appendName);
        //设置文件名
        appender.setFile(OptionHelper.substVars(rootPath.concat( File.separator).
                concat(new SimpleDateFormat("yyyyMMdd").format(new Date())).concat(".log"),context));
        appender.setAppend(true);

        appender.setPrudent(false);

        //设置文件创建时间及大小的类
        SizeAndTimeBasedRollingPolicy<ILoggingEvent> policy = new SizeAndTimeBasedRollingPolicy<>();
        //文件名格式
        String fp = OptionHelper.substVars(rootPath+ File.separator + ".%d{yyyy-MM-dd}.%i.log",context);
        //最大日志文件大小
        policy.setMaxFileSize(FileSize.valueOf("128MB"));
        //设置文件名模式
        policy.setFileNamePattern(fp);
        //设置最大历史记录为15条
        policy.setMaxHistory(15);
        //设置父节点是appender
        policy.setParent(appender);
        //设置上下文，每个logger都关联到logger上下文，默认上下文名称为default。
        // 但可以使用<contextName>设置成其他名字，用于区分不同应用程序的记录。一旦设置，不能修改。
        policy.setContext(context);
        //总大小限制
        policy.setTotalSizeCap(FileSize.valueOf("32GB"));
        policy.start();

        PatternLayoutEncoder encoder = new PatternLayoutEncoder();
        //设置上下文，每个logger都关联到logger上下文，默认上下文名称为default。
        // 但可以使用<contextName>设置成其他名字，用于区分不同应用程序的记录。一旦设置，不能修改。
        encoder.setContext(context);
        //设置格式
        encoder.setPattern("%d %p (%file:%line\\)- %m%n");
        encoder.start();

        //加入下面两个节点
        appender.setRollingPolicy(policy);
        appender.setEncoder(encoder);
        appender.start();
        return appender;
    }

    public Appender<ILoggingEvent> startAppender(SysJobLog job) {
        return getAppender(job.getJobId(), null, job.getJobNameEn(), job.getJobName(), null);

    }

    public void stopAppender(Appender<ILoggingEvent> appender) {
        appender.stop();
    }
}
