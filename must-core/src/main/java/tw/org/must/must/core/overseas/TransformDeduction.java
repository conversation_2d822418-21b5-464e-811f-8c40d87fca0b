package tw.org.must.must.core.overseas;

import org.apache.commons.collections4.CollectionUtils;
import tw.org.must.must.model.listoverseas.ListOverseasReceiptStatement;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: hanDa
 * @Date: 2020/9/9 10:58
 * @Version:1.0
 * @Description:
 */
public class TransformDeduction implements TransformInterface{

    @Override
    public List<ListOverseasReceiptStatement> addListOverseasReceiptStatement(List<ListOverseasReceiptStatement> listOverseasReceiptStatementList) {
        //非FIE金額 deduction有扣除金额  本条拆分成一条
        List<ListOverseasReceiptStatement> listOverseasReceiptStatementList1 = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(listOverseasReceiptStatementList)){
            ListOverseasReceiptStatement deductionStatement = listOverseasReceiptStatementList.get(0);
            ListOverseasReceiptStatement deduction = new ListOverseasReceiptStatement();
            deduction.setType(TransformDistParamOverseas.STATEMENT_TYPE);
            deduction.setCurrencyCode(deductionStatement.getCurrencyCode());
            deduction.setReceiptAmount(deductionStatement.getReceiptAmount());
            deduction.setLocalAmount(deductionStatement.getLocalAmount());
            deduction.setStatementAmount(deductionStatement.getStatementAmount());
            deduction.setNoStatementAmount(deductionStatement.getNoStatementAmount());
            deduction.setPercentage(deductionStatement.getPercentage());
            deduction.setDeduction(deductionStatement.getDeduction());
            listOverseasReceiptStatementList1.add(deduction);
        }
        return listOverseasReceiptStatementList1;
    }
}