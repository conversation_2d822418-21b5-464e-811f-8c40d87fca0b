package tw.org.must.must.core.parse.meta;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tw.org.must.must.common.enums.DeletedEnum;
import tw.org.must.must.common.enums.IsShowEnum;
import tw.org.must.must.common.util.DateUtils;
import tw.org.must.must.core.parse.DspDataTemp;
import tw.org.must.must.core.service.claim.ClaimMinimaInfoService;
import tw.org.must.must.core.service.dsp.DspListUniqueKeyHandler;
import tw.org.must.must.core.service.list.ListDspFileBaseService;
import tw.org.must.must.core.service.list.ListDspFileDataMappingService;
import tw.org.must.must.model.claim.ClaimMinimaInfo;
import tw.org.must.must.model.list.ListDspFileBase;
import tw.org.must.must.model.list.ListDspFileDataMapping;
import tw.org.must.must.model.list.ListFileQueue;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import static tw.org.must.must.common.constants.Constants.BATCH_SIZE_1000;

/**
 * 解析youtube   tsv文件 (optimization)
 *
 * <AUTHOR>
 */
@Service
public class ParseMetaTxtService extends DspListUniqueKeyHandler {

    Logger logger = LoggerFactory.getLogger(ParseMetaTxtService.class);

    private String USAGE_START_DATE = "usage_start_date";
    private String USAGE_END_DATE = "usage_end_date";
    private String TERRITORY = "territory";
    private String PRODUCT = "product";
    private String EVENT_COUNT = "event_count";
    private String ELECTED_ISRC = "elected_isrc";
    private String ARTIST = "artist";
    private String TITLE = "title";
    private String ISRCS = "isrcs";
    private String ISWCS = "iswcs";
    private String SALES_TRANSACTION_ID = "sales_transaction_id";
    private String RESOURCE_ID = "resource_id";
    private String CUSTOM_IDS = "custom_ids";
    private String MECHANICAL_LICENSED_COLLECTION_SHARES = "mechanical_licensed_collection_shares";
    private String PERFORMANCE_LICENSED_COLLECTION_SHARES = "performance_licensed_collection_shares";
    private String SONGWRITERS = "songwriters";
    private String NET_REVENUE = "net_revenue";
    private String CURRENCY = "currency";

    private String COMPANY_META = "META";

    @Autowired
    private ListDspFileBaseService listDspFileBaseService;

    @Autowired
    private ListDspFileDataMappingService listDspFileDataMappingService;

    @Autowired
    private ClaimMinimaInfoService claimMinimaInfoService;

    private ListFileQueue listFileQueue;
    private List<ListDspFileDataMapping> listDspFileDataMappingList = new ArrayList<>();
    Map<String,ListDspFileBase> baseMap = new HashMap<>();
    Set<String> productNameSet = new HashSet<>();


    @Transactional
    public void parse(ListFileQueue lfq) throws Exception {
        String fileName = lfq.getFileName();
        if( fileName.contains("_AGG")){

        } else {
            listFileQueue = lfq;
//            String result = saveDspFileBase(lfq.getFilePath());
//            if(StringUtils.isNotBlank(result)){
//                XxlJobLogger.log("解析异常~" + result);
//                listFileQueue.setStatus(3);
//                listFileQueue.setDescription(result);
//                listDspFileDataMappingList.clear();
//                return;
//            }
            parse(lfq.getFilePath());
            //解析完， 同步base表中mapping的数量
            /*List<ListDspFileBase> listDspFileBases = listDspFileBaseService.getListDspFileBaseByQueueId(lfq.getId());
            if (!listDspFileBases.isEmpty()) {
                listDspFileDataMappingService.updateFileBaseCountByDspFileBaseInfo(listDspFileBases, fileBaseIdTotalClickNumMap);
            }*/
        }
    }

    public void parse(String filepath){
        LineIterator lineIterator = null;
        String result = "";
        int i = 0;
        try{
            Map<Long, String> fileBaseIdMap = new HashMap<>(); // <fileBaseId, tableName>;
            Map<Long, List<DspDataTemp>> fileBaseIdPlayCountMap = new HashMap<>(); // <fileBaseId, <mapping.id, mapping.clickNumber>>
            lineIterator = FileUtils.lineIterator(new File(filepath), "utf-8");
            String[] headers = new String[18];
            while (lineIterator.hasNext()){
                i++;
                String line = lineIterator.nextLine();
                if (StringUtils.isBlank(line)) {
                    continue;
                }

                if(i == 1){
                    headers = StringUtils.splitPreserveAllTokens(line, "\t");
                } else {
                    dealLine(line,headers);
                }

                if(listDspFileDataMappingList.size() >= 1000){
                    saveDspFileDataMapping(fileBaseIdMap,fileBaseIdPlayCountMap);
                    this.listDspFileDataMappingList.clear();
                }
            }

            // 最後剩餘數據入庫
            if (listDspFileDataMappingList.size() > 0) {
                saveDspFileDataMapping(fileBaseIdMap, fileBaseIdPlayCountMap);
                listDspFileDataMappingList.clear();
            }

            // 数据排序后同步到正式表，删除临时表
            if (fileBaseIdMap.size() > 0) {
                CountDownLatch countDownLatch = new CountDownLatch(fileBaseIdMap.size());
                ReentrantLock lock = new ReentrantLock(true); // 公平锁，写锁（排他锁），用来顺序（唯一）插入mapping数据

                long startTime = System.currentTimeMillis();
                for (Long fileBaseId : fileBaseIdMap.keySet()) {
                    List<DspDataTemp> dspDataTemps = fileBaseIdPlayCountMap.getOrDefault(fileBaseId,new ArrayList<>());
                    if (CollectionUtils.isEmpty(dspDataTemps)) {
                        countDownLatch.countDown();
                        continue;
                    }
                    String tableName = fileBaseIdMap.get(fileBaseId);
                    // 异步方法里不共用事务
                    CompletableFuture.runAsync(() -> { // 该方法默认线程池大小是当前电脑核数 - 1
                        // TODO: 2021-08-02 huyong: 如果该任务卡主，可能是该方法异步线程出错不会报错的，偶发性问题，需要使用自定义线程池，同时中断其他线程并向上抛出异常，可参考 syncOfficialTable 方法
                        long listSize = 0;
                        long a = System.currentTimeMillis();
                        List<Long> collect = dspDataTemps.stream().sorted(Comparator.comparing(DspDataTemp::getWorkPrice, Comparator.reverseOrder()).thenComparing(DspDataTemp::getClickNumber, Comparator.reverseOrder())).map(DspDataTemp::getId).collect(Collectors.toList());
                        BigDecimal clickNumber = dspDataTemps.stream().map(DspDataTemp::getClickNumber).reduce(BigDecimal.ZERO,BigDecimal::add);
                        BigDecimal netRevenue = dspDataTemps.stream().map(DspDataTemp::getWorkPrice).reduce(BigDecimal.ZERO,BigDecimal::add);
                        long b = System.currentTimeMillis() - a;
                        logger.info("临时表【{}】，workPrice, clickNumber倒叙排序耗时：【{}】", tableName, b > 1000 ? (b / 1000) + " 秒" : b + " 毫秒");
                        List<List<Long>> partition = Lists.partition(collect, BATCH_SIZE_1000);
                        for (List<Long> ids : partition) {
                            XxlJobLogger.log("临时表【{}】，查询id个数：【{}】", tableName, ids.size());
                            List<ListDspFileDataMapping> listDspFileDataMappings = listDspFileDataMappingService.selectTemporaryByIds(ids, tableName);
                            try {
                                lock.lock();
                                XxlJobLogger.log("开始插入正式表，fid:【{}】, size:【{}】", fileBaseId, listDspFileDataMappings.size());
                                listDspFileDataMappingService.addList(listDspFileDataMappings);
                                listSize += listDspFileDataMappings.size();
                            } catch (Exception e) {
                                XxlJobLogger.log("tzk001,msg: ");
                                XxlJobLogger.log(e);
                            } finally {
                                lock.unlock();
                            }
                        }
                        // 跟新base的关于mapping的个数
                        ListDspFileBase base = new ListDspFileBase();
                        base.setId(fileBaseId);
                        base.setListFileTotal(listSize);
                        base.setClickNumber(clickNumber);
                        base.setListTotalRoy(netRevenue);
                        base.setFileStatus(1);
                        base.init();
                        XxlJobLogger.log("更新base: {}", JSON.toJSONString(base));
                        listDspFileBaseService.updateSelective(base);
                        XxlJobLogger.log("更新base完成: {}", JSON.toJSONString(base));
                        countDownLatch.countDown();
                        XxlJobLogger.log("临时表【{}】end，countDownLatch个数：【{}】", tableName, countDownLatch.getCount());
                    });
                }
                countDownLatch.await();
                XxlJobLogger.log("countDownLatch end");
                while (lock.isLocked()) {
                    //自旋，等待最后一个公平锁释放
                }
                for (String tableName : fileBaseIdMap.values()) {
                    // 需要等待异步线程执行完后，才能删除表，否则会出现查询的时候表没了，导致程序卡死
                    logger.info("临时表数据-> 正式表数据完成, drop临时表【{}】", tableName);
                    listDspFileDataMappingService.dropTemporaryTable(tableName);
                    logger.info("临时表数据-> 正式表数据完成, drop临时表【{}】成功！", tableName);
                }
                XxlJobLogger.log("lock end");
                long consuming = System.currentTimeMillis() - startTime;
                XxlJobLogger.log("fid:【{}】, 临时表->正式表完成, time consuming：【{}】", fileBaseIdMap.keySet(), consuming > 1000 ? (consuming / 1000) + " 秒" : consuming + " 毫秒");
            }

            listFileQueue.setStatus(2);
            listFileQueue.setDescription("解析完成~~~~");

        }catch (Exception e){
            String message = e.getMessage();
            XxlJobLogger.log("================== "+ message);
            if (null != message && message.length() > 5000) {
                message = message.substring(0, 4900);
            }
            listFileQueue.setStatus(3);
            if (StringUtils.isNotBlank(result)) {
                listFileQueue.setDescription(String.format("解析出错：%s", result));
            } else {
                listFileQueue.setDescription(String.format("解析出错：%s", message));
            }
            e.printStackTrace();
        } finally {
            this.listDspFileDataMappingList.clear();
            this.productNameSet.clear();
            LineIterator.closeQuietly(lineIterator);
        }
    }

    /**
     * @param line      当前解析的内容（数据）
     * @param headerSplit 标题头
     * @return
     */
    protected void dealLine(String line, String[] headerSplit) {
        String[] lineSplit = StringUtils.splitPreserveAllTokens(line, "\t");
        if (lineSplit.length != headerSplit.length) {
            //数据与标题个数不一致，则忽略该行后面的，提示下
            logger.info("=================数据与标题匹配不上======================");
            logger.info("line: " + line);
        }
        try {
            Map<String, String> map = this.convert2Map(headerSplit, lineSplit);
            // 读取一行数据就将product保存起来
            String product = map.get(PRODUCT);
            if (StringUtils.isNotBlank(product)) {
                if (!productNameSet.contains(product)){
                    productNameSet.add(product);
                    saveDspFileBase(listFileQueue.getFilePath(), product);
                }
            }
            this.parseData(map);
        } catch (Exception e) {
            logger.error("convert error,the error is " + e);
//            logger.info("error line content: " + line);
            logger.info("error line content: " + String.join("||",lineSplit));
        }
    }

    private Map<String, String> convert2Map(String[] headerSplit, String[] lineSplit) {
        Map<String, String> map = new HashMap<>();
        headerSplit[0] = headerSplit[0].replace("#", "");
        for (int i = 0; i < headerSplit.length; i++) {
            String value = null;
            // 当标题与数据的长度不一致，忽略缺失数据（多余数据）
            if (i < lineSplit.length) {
                value = lineSplit[i];
            }
            map.put(headerSplit[i], value);
        }
        return map;
    }

    private void saveBaseMap(String string){

    }

    /**
     * 解析数据
     *
     * @param map
     * @return
     */
    private void parseData(Map<String, String> map) {

        ListDspFileDataMapping listDspFileDataMapping = new ListDspFileDataMapping();
        listDspFileDataMapping.init();

        listDspFileDataMapping.setTitle(map.get(TITLE));
        listDspFileDataMapping.setUseArea(map.get(TERRITORY));
        listDspFileDataMapping.setProduct(map.get(PRODUCT));
        String eventCount = map.get(EVENT_COUNT);
        BigDecimal click_number = StringUtils.isBlank(eventCount) ? BigDecimal.ZERO : new BigDecimal(eventCount);
        listDspFileDataMapping.setClickNumber(click_number);
        listDspFileDataMapping.setIsrc(map.get(ISRCS));
        listDspFileDataMapping.setIswc(map.get(ISWCS));
        listDspFileDataMapping.setWorkArtist(map.get(ARTIST));
        listDspFileDataMapping.setSalesTransactionId(map.get(SALES_TRANSACTION_ID));
        listDspFileDataMapping.setResourceId(map.get(RESOURCE_ID));
        listDspFileDataMapping.setCompCustomId(map.get(CUSTOM_IDS));
        listDspFileDataMapping.setAuthor(map.get(SONGWRITERS));
        BigDecimal workPrice =  StringUtils.isBlank(map.get(NET_REVENUE)) ? BigDecimal.ZERO : new BigDecimal(map.get(NET_REVENUE));
        listDspFileDataMapping.setWorkPrice(workPrice);
        listDspFileDataMapping.setCompany(COMPANY_META);
        listDspFileDataMapping.setSalesTransactionId(map.get(SALES_TRANSACTION_ID));
        if(listFileQueue.getMatchMark().equals("N")){
            listDspFileDataMapping.setStatus(5);
        }else {
            listDspFileDataMapping.setStatus(0);
        }

        ListDspFileBase listDspFileBase = baseMap.get(map.get(PRODUCT));
        if(listDspFileBase != null){
            listDspFileDataMapping.setCommercialModel(listDspFileBase.getCommercialModel());
            listDspFileDataMapping.setFileBaseId(listDspFileBase.getId());
        }

        setUniqueKey(listDspFileDataMapping);

        String usageStartDate = map.get(USAGE_START_DATE);
        String usageEndDate = map.get(USAGE_END_DATE);
        try {
            if(usageStartDate != null){
                listDspFileDataMapping.setListFileStartTime(new SimpleDateFormat("yyyy-MM-dd").parse(usageStartDate));
            }
            if(usageEndDate != null){
                listDspFileDataMapping.setListFileEndTime(new SimpleDateFormat("yyyy-MM-dd").parse(usageEndDate));
            }
        } catch (ParseException e) {
            //直接抛出异常，并不在执行下去
            throw new RuntimeException("格式不正确");
        }


        Map<String, String> extJson = new HashMap<>();
        extJson.put(CURRENCY,map.get(CURRENCY));
        extJson.put(ELECTED_ISRC,map.get(ELECTED_ISRC));
        extJson.put(MECHANICAL_LICENSED_COLLECTION_SHARES,map.get(MECHANICAL_LICENSED_COLLECTION_SHARES));
        extJson.put(PERFORMANCE_LICENSED_COLLECTION_SHARES,map.get(PERFORMANCE_LICENSED_COLLECTION_SHARES));
        extJson.put(SALES_TRANSACTION_ID,map.get(SALES_TRANSACTION_ID));
        extJson.put(CUSTOM_IDS,map.get(CUSTOM_IDS));

        listDspFileDataMapping.setExtJson(JSON.toJSONString(extJson));

        listDspFileDataMappingList.add(listDspFileDataMapping);
    }

    private void saveDspFileDataMapping( Map<Long, String> fileBaseIdMap, Map<Long, List<DspDataTemp>> fileBaseIdPlayCountMap) {

        if(!CollectionUtils.isEmpty(listDspFileDataMappingList)){
            Map<Long, List<ListDspFileDataMapping>> listMap = listDspFileDataMappingList.stream().collect(Collectors.groupingBy(ListDspFileDataMapping::getFileBaseId));
            for (Long fileBaseId : listMap.keySet()) {
                List<ListDspFileDataMapping> mappings = listMap.get(fileBaseId);
                if (fileBaseIdMap.containsKey(fileBaseId)) {
                    int i = listDspFileDataMappingService.addListTemporary(fileBaseIdMap.get(fileBaseId), mappings);
                } else {
                    String temporaryTable = listDspFileDataMappingService.createTemporaryTable(fileBaseId);
                    fileBaseIdMap.put(fileBaseId, temporaryTable);
                    int i = listDspFileDataMappingService.addListTemporary(temporaryTable, mappings);
                }
                List<DspDataTemp> transfer = transfer(mappings);
                List<DspDataTemp> orDefault = fileBaseIdPlayCountMap.getOrDefault(fileBaseId, new ArrayList<>());
                orDefault.addAll(transfer);
                fileBaseIdPlayCountMap.put(fileBaseId, orDefault);
            }
        }
    }


    public ListFileQueue getListFileQueue() {
        return listFileQueue;
    }

    public void setListFileQueue(ListFileQueue listFileQueue) {
        this.listFileQueue = listFileQueue;
    }

    @Override
    public String getCompanyIdentification() {
        return "meta";
    }

    public String saveDspFileBase(String filepath) throws ParseException {
        String result = "";
        String extJson = listFileQueue.getExtJson();
        JSONObject object = JSONObject.fromObject(extJson);
        Long claimSetInfoId = object.getLong("claimSetInfoId");
        if (null == claimSetInfoId) {
            result = "队列表中缺少claimSetid!联系工作人员~";
            return result;
        }

        String fileNameExt = listFileQueue.getFileName();
        String fileName = fileNameExt.replace(".txt","");
        String[] filenames = fileName.split("_");
        String commercialModel = String.join("_",Arrays.asList(filenames[1],filenames[2],filenames[3]));
        Date[] season = DateUtils.getSeasonDate(Integer.parseInt(filenames[4]),filenames[5]);
        List<String> productNameList = new ArrayList<>();
        productNameList.add(filenames[3] + "IG");
        productNameList.add(filenames[3] + "FB");
        for(String productName : productNameList){
            Map<String, Object> extJson2 = new HashMap<>();
            ListDspFileBase listDspFileBase = new ListDspFileBase();
            listDspFileBase.setCurrency("USD");
            listDspFileBase.setUseType("STREAMINGONDEMAND");


            if(season != null && season.length == 2 ){
                listDspFileBase.setListFileStartTime(DateUtils.getFirstDateOfMonth(season[0]));
                listDspFileBase.setListFileEndTime(DateUtils.getLastDateOfMonth(season[1]));
            }
//			String fileNumber = header.getFileNumber();
            listDspFileBase.setDspCompany("META");
            listDspFileBase.setDist(0);
            listDspFileBase.setDistStatus(0);
            listDspFileBase.setFilePath(filepath);
//            String fileName = "DSR_MUST-CS_YouTube_Usage-AdSupport-Music-All_2019-Q4_TW_1of1_20200122T132333.tsv";
            listDspFileBase.setFileName(fileNameExt);
            listDspFileBase.setFileStatus(1);
            listDspFileBase.setCategoryCode(listFileQueue.getCategoryCode());
            listDspFileBase.setFileQuenceId(listFileQueue.getId());
            listDspFileBase.setDeleted(DeletedEnum.normal.code());
            listDspFileBase.setFileExt(".txt");
            listDspFileBase.setCommercialModel(commercialModel);
            listDspFileBase.setIsShow(IsShowEnum.show.code());

            listDspFileBase.setProductName(productName);
            listDspFileBase.setMinimaCalcStatus(0);

            ClaimMinimaInfo meta = claimMinimaInfoService.getClaimMinimaInfoByProductShorNameAndSetId(productName, claimSetInfoId);
            listDspFileBase.setClaimMinimaInfoId(meta.getId());

            extJson2.put("claimSetInfoId", meta.getClaimSetId());
            listDspFileBase.setExtJson(JSON.toJSONString(extJson2));
            listDspFileBase.setClickNumber(listDspFileBase.getListTotalClickCount());
            listDspFileBase.setMatchMark(listFileQueue.getMatchMark());

            listDspFileBase.init();
            listDspFileBaseService.add(listDspFileBase);
            baseMap.put(productName,listDspFileBase);
        }

        return result;
    }


    public String saveDspFileBase(String filepath,String productName) throws ParseException {
        String result = "";
        String extJson = listFileQueue.getExtJson();
        JSONObject object = JSONObject.fromObject(extJson);
        Long claimSetInfoId = object.getLong("claimSetInfoId");
        if (null == claimSetInfoId) {
            result = "队列表中缺少claimSetid!联系工作人员~";
            return result;
        }

        String fileNameExt = listFileQueue.getFileName();
        String fileName = fileNameExt.replace(".txt","");
        String[] filenames = fileName.split("_");
        String commercialModel = String.join("_",Arrays.asList(filenames[1],filenames[2],filenames[3]));
        Date[] season = DateUtils.getSeasonDate(Integer.parseInt(filenames[4]),filenames[5]);
        Map<String, Object> extJson2 = new HashMap<>();

        ListDspFileBase listDspFileBase = new ListDspFileBase();
        listDspFileBase.setCurrency("USD");
        listDspFileBase.setUseType("STREAMINGONDEMAND");
        if (season != null && season.length == 2) {
            listDspFileBase.setListFileStartTime(DateUtils.getFirstDateOfMonth(season[0]));
            listDspFileBase.setListFileEndTime(DateUtils.getLastDateOfMonth(season[1]));
        }
//			String fileNumber = header.getFileNumber();
        listDspFileBase.setDspCompany("META");
        listDspFileBase.setDist(0);
        listDspFileBase.setDistStatus(0);
        listDspFileBase.setFilePath(filepath);
//            String fileName = "DSR_MUST-CS_YouTube_Usage-AdSupport-Music-All_2019-Q4_TW_1of1_20200122T132333.tsv";
        listDspFileBase.setFileName(fileNameExt);
        listDspFileBase.setFileStatus(1);
        listDspFileBase.setCategoryCode(listFileQueue.getCategoryCode());
        listDspFileBase.setFileQuenceId(listFileQueue.getId());
        listDspFileBase.setDeleted(DeletedEnum.normal.code());
        listDspFileBase.setFileExt(".txt");
        listDspFileBase.setCommercialModel(commercialModel);
        listDspFileBase.setIsShow(IsShowEnum.show.code());

        listDspFileBase.setProductName(productName);
        listDspFileBase.setMinimaCalcStatus(0);

        ClaimMinimaInfo meta = claimMinimaInfoService.getClaimMinimaInfoByProductShorNameAndSetId(productName, claimSetInfoId);
        listDspFileBase.setClaimMinimaInfoId(meta.getId());

        extJson2.put("claimSetInfoId", meta.getClaimSetId());
        listDspFileBase.setExtJson(JSON.toJSONString(extJson2));
        listDspFileBase.setClickNumber(listDspFileBase.getListTotalClickCount());
        listDspFileBase.setMatchMark(listFileQueue.getMatchMark());

        listDspFileBase.init();
        listDspFileBaseService.add(listDspFileBase);
        baseMap.put(productName, listDspFileBase);
        return "";
    }

    /**
     *
     * @param filepath 文件全路径名
     * @throws IOException IO异常
     */
    public void parseTest(String filepath) {
        LineIterator lineIterator = null;
        int i = 0;
        try{
            lineIterator = FileUtils.lineIterator(new File(filepath), "utf-8");
            String[] headers = new String[18];
            while (lineIterator.hasNext()){
                i++;
                String line = lineIterator.nextLine();
                if (StringUtils.isBlank(line)) {
                    continue;
                }

                if(i == 1){
                    headers = StringUtils.splitPreserveAllTokens(line, "\t");
                } else {
                    dealLine(line,headers);
                }

                if(listDspFileDataMappingList.size() >= 10){
                    for(ListDspFileDataMapping mapping : listDspFileDataMappingList){
                        System.out.println("==============================================================");
                        System.out.println(JSON.toJSONString(mapping));
                        System.out.println("==============================================================");
                    }
                    break;
                }

            }


        } catch (Exception e) {
//            String message = e.getMessage();
            logger.error("error",e);
        } finally {
            LineIterator.closeQuietly(lineIterator);
        }
    }


    public static void main(String[] args) {
        /*ParseMetaTxtService test = new ParseMetaTxtService();
        test.parseTest("F:\\must\\需求\\META\\META\\新合約-做Claim及分配\\2022_Q4\\must_TW_Cons_UGCINTENSIVE_2022_Q4.txt");*/

    }
}
