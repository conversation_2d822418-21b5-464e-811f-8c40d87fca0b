package tw.org.must.must.core.parse.youtube.tsv.entity.v1_3;

import lombok.Data;
import tw.org.must.must.core.parse.youtube.tsv.entity.v1_2.BaseTsvEntity;

@Data
public class SY0203 extends BaseTsvEntity {
    private String SummaryRecordId;
    private String DistributionChannel;
    private String DistributionChannelDPID;
    private String CommercialModel;
    private String UseType;
    private String Territory;
    private String ServiceDescription;
    private String TotalUsages;
    private String Users;
    private String CurrencyOfReporting;
    private String NetRevenue;
    private String RightsController;
    private String RightsControllerPartyId;
    private String AllocatedUsages;
    private String AllocatedRevenue;
    private String AllocatedNetRevenue;
    private String RightsType;
    private String ContentCategory;
    private String CurrencyOfTransaction;
    private String ExchangeRate;
    private String RightsTypePercentage;
}
