package tw.org.must.must.core.service.claim;


import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.core.service.claim.impl.CliamWorkIpShareRightProcess;
import tw.org.must.must.model.claim.ClaimCcidExportView;
import tw.org.must.must.model.claim.ClaimCcidHeader;
import tw.org.must.must.model.claim.ClaimMaxRevenue;
import tw.org.must.must.model.claim.ClaimMinimaInfo;
import tw.org.must.must.model.match.MatchWrkDetailInfo;

import java.util.Date;
import java.util.List;

public interface ClaimCcidExportViewService extends BaseService<ClaimCcidExportView> {

	
	List<ClaimCcidExportView> selectList(Long cliamSetId,Date startDate,Date endDate);
	








	int calcCCid(ClaimCcidHeader claimCcidHeader ,List<MatchWrkDetailInfo> matchWrkDetailInfo, ClaimMinimaInfo claimMinimaInfo,
			ClaimMaxRevenue claimMaxRevenue, CliamWorkIpShareRightProcess rightProcess);
}