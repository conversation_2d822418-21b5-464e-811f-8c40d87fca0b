package tw.org.must.must.core.service.claim.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.claim.ClaunAppleMusicUsage;
import tw.org.must.must.core.service.claim.ClaimAppleMusicUsageService;
import tw.org.must.must.mapper.claim.ClaimMaxRevenueMapper;
import tw.org.must.must.model.claim.ClaimMaxRevenue;

import java.util.*;

@Service
public class ClaimAppleMusicUsageServiceImpl extends BaseServiceImpl<ClaimMaxRevenue> implements ClaimAppleMusicUsageService {

    final ClaimMaxRevenueMapper claimMaxRevenueMapper;
    @Autowired
    ClaunAppleMusicUsage claunAppleMusicUsage;

    @Autowired
    public ClaimAppleMusicUsageServiceImpl(ClaimMaxRevenueMapper claimMaxRevenueMapper) {
        super(claimMaxRevenueMapper);
        this.claimMaxRevenueMapper = claimMaxRevenueMapper;
    }


    @Override
    public void handle(Map<String, String> map) {

        List<ClaimMaxRevenue> claimMaxRevenueList = claimMaxRevenueMapper.getClaimMaxRevenueList();
        System.out.println(claimMaxRevenueList);
    }

}
