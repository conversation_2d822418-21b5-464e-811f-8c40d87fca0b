package tw.org.must.must.core.service.claim.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.util.BeanCopyUtil;
import tw.org.must.must.common.util.excel.ExcelUtil;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.vcp.CommonUtil;
import tw.org.must.must.core.service.claim.ClaimFilterOtherIpService;
import tw.org.must.must.core.service.claim.ClaimFilterOtherWorkService;
import tw.org.must.must.core.service.claim.ClaimFilterOthersocService;
import tw.org.must.must.core.service.list.ListMatchDataDspDoneService;
import tw.org.must.must.core.service.mbr.MbrIpNameService;
import tw.org.must.must.core.service.ref.RefSocietyService;
import tw.org.must.must.mapper.claim.ClaimFilterOtherWorkMapper;
import tw.org.must.must.mapper.claim.ClaimFilterOthersocMapper;
import tw.org.must.must.model.claim.ClaimFilterOtherIp;
import tw.org.must.must.model.claim.ClaimFilterOtherWork;
import tw.org.must.must.model.claim.ClaimFilterOthersoc;
import tw.org.must.must.model.mbr.vo.IpNameVO;
import tw.org.must.must.model.ref.RefSociety;

import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class ClaimFilterOtherWorkServiceImpl extends BaseServiceImpl<ClaimFilterOtherWork> implements ClaimFilterOtherWorkService {

    private Logger logger = LoggerFactory.getLogger(ClaimFilterOtherWorkServiceImpl.class);

    private final ClaimFilterOtherWorkMapper claimFilterOtherWorkMapper;

    @Autowired
    private ClaimFilterOtherIpService claimFilterOtherIpService;

    @Autowired
    private ClaimFilterOthersocService claimFilterOtherSocService;

    @Autowired
    private RefSocietyService refSocietyService;

    @Autowired
    protected MbrIpNameService mbrIpNameService;

    @Autowired
    public ClaimFilterOtherWorkServiceImpl(ClaimFilterOtherWorkMapper claimFilterOtherWorkMapper) {
        super(claimFilterOtherWorkMapper);
        this.claimFilterOtherWorkMapper = claimFilterOtherWorkMapper;
    }

    // 读取excel对应的 指定的的work_id 和 work_society_code 的标题
    private static final String EXCEL_WORK_NUM = "work_id";
    private static final String EXCEL_WORK_SOC = "work_society_code";
    private static final String RIGHT_PER = "per";
    private static final String RIGHT_MEC = "mec";
    private static final String EXT_INFO = "ext_info";
    private static final String IP_BASE_NO = "ip_base_no";
    private static final String IP_NAME = "ip_name";
    private static final String SOC = "soc";

    // 根据 workUniqueKey 查询 claimFilterOtherSoc 用于Claim计算的时候 共用
    @Override
    public boolean isContainsClaimFilterOtherWork(String workUniqueKey) {
        List<ClaimFilterOtherWork> claimFilterOtherWorkList = this.getClaimFilterOtherWorkByWorkUniqueKey(workUniqueKey);
        if (null != claimFilterOtherWorkList && claimFilterOtherWorkList.size() > 0) {
            return true;
        }
        return false;
    }

    public List<ClaimFilterOtherWork> getWorkUniqueKeyByCompany(String tableName, String company, Long startId){

        return claimFilterOtherWorkMapper.getWorkUniqueKeyByCompany(tableName,company,startId);
    }

    @Override
    public Set<String> getAllWorkUniqueKeyByCompany(String company) {
        Set<String> results = new HashSet<>();
        if(StringUtils.isBlank(company)){
            return results;
        }

        String[] filterTables = {"claim_filter_other_work","claim_filter_other_ip","claim_filter_other_soc"};

        for(String tableName : filterTables){
            Long startid= 0L;

            while (true){

                List<ClaimFilterOtherWork> claimFilterOtherWorkList = getWorkUniqueKeyByCompany(tableName,company,startid);
                if(CollectionUtils.isEmpty(claimFilterOtherWorkList)){
                    break;
                }

                startid = claimFilterOtherWorkList.get(claimFilterOtherWorkList.size()-1).getId();

                results.addAll(claimFilterOtherWorkList.stream().map(c -> c.getWorkUniqueKey()).collect(Collectors.toSet()));

            }
        }

        return results;
    }

    @Override
    public List<ClaimFilterOtherWork> getClaimFilterOtherWorkByWorkUniqueKey(String workUniqueKey) {
        if (StringUtils.isBlank(workUniqueKey)) {
            return new ArrayList<>();
        }
        Example example = new Example(ClaimFilterOtherWork.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("workUniqueKey", workUniqueKey);
        return claimFilterOtherWorkMapper.selectByExample(example);
    }

    @Override
    @Transactional
    public int importFile(File targetFile,boolean delWork,boolean dwlSoc,boolean delIp,String company) throws Exception {

        if(delWork) deleteByCompany(company);
        int workCount = importWork(targetFile,company);

        if (delIp) claimFilterOtherIpService.deleteByCompany(company);
        int ipCount = importIp(targetFile,company);

        if (dwlSoc) claimFilterOtherSocService.deleteByCompany(company);
        int socCount = importSoc(targetFile,company);

        return workCount + ipCount + socCount;
    }

    public int importWork(File targetFile,String company) throws Exception {
        AtomicInteger result = new AtomicInteger(0);
        // 导入之前需要清空数据表
//        claimFilterOtherWorkMapper.truncateTable();
        Map<String,ClaimFilterOtherWork> claimFilterOtherWorkAddMap = new HashMap<>();
        Map<String,ClaimFilterOtherWork> claimFilterOtherWorkUpdateMap = new HashMap<>();
        // 指定sheetName为filter-work
        ExcelUtil.read(targetFile,1,"work", (t, u) -> {
            if (u.isEmpty()) {
                return;
            }
            String workId = u.get(EXCEL_WORK_NUM);
            String workSocietyCode = u.get(EXCEL_WORK_SOC);
//            String company = u.get(COMPANY);
            String per = u.get(RIGHT_PER);
            String mec = u.get(RIGHT_MEC);
            String extInfo = u.get(EXT_INFO);
            if(StringUtils.isBlank(workId) || StringUtils.isBlank(workSocietyCode)) {
//                throw new MustException(String.format("%s字段不能爲空！", EXCEL_WORK_NUM));
                return;
            }

            if(StringUtils.isBlank(per) && StringUtils.isBlank(mec)){
//                throw new MustException(String.format("%s和%s字段不能同时爲空！", RIGHT_PER,RIGHT_MEC));
                return;
            }

            ClaimFilterOtherWork claimFilterOtherWork = new ClaimFilterOtherWork();
            Long workNum = Long.valueOf(workId);
            Integer soc = Integer.valueOf(workSocietyCode);
            String workUniqueKey = CommonUtil.getWorkUniqueKeyByWrkSocAndNum(soc, workNum);
            String key = company + workUniqueKey;

            List<ClaimFilterOtherWork> existList = getByParam(company, workNum, soc, null,null);
            if(CollectionUtils.isNotEmpty(existList)){
                claimFilterOtherWork = existList.get(0);
                claimFilterOtherWorkUpdateMap.put(key,claimFilterOtherWork);
            } else {
                claimFilterOtherWorkAddMap.put(key,claimFilterOtherWork);
            }

            claimFilterOtherWork.setWorkId(workNum);
            claimFilterOtherWork.setWorkSoc(soc);
            claimFilterOtherWork.setCompany(company);
            claimFilterOtherWork.setWorkUniqueKey(workUniqueKey);
            claimFilterOtherWork.setPer(StringUtils.isBlank(per) ? 0 : 1);
            claimFilterOtherWork.setMec(StringUtils.isBlank(mec) ? 0 : 1);
            if (StringUtils.isNotBlank(extInfo)) {
                claimFilterOtherWork.setExtInfo(extInfo);
            }
            claimFilterOtherWork.init();
//            claimFilterOtherWorkAddList.add(claimFilterOtherWork);
        });

        List<ClaimFilterOtherWork> claimFilterOtherWorkAddList = claimFilterOtherWorkAddMap.values().stream().collect(Collectors.toList());
        List<ClaimFilterOtherWork> claimFilterOtherWorkUpdateList = claimFilterOtherWorkUpdateMap.values().stream().collect(Collectors.toList());
        if (claimFilterOtherWorkUpdateList.size() > Constants.BATCH_SIZE_1000) {
            List<List<ClaimFilterOtherWork>> partitions = Lists.partition(claimFilterOtherWorkUpdateList, Constants.BATCH_SIZE_1000);
            for(List<ClaimFilterOtherWork> partition : partitions) {
                result.addAndGet(claimFilterOtherWorkMapper.updateBatchByPrimaryKey(partition));
            }
        } else if (claimFilterOtherWorkUpdateList.size() > 0) {
            result.addAndGet(claimFilterOtherWorkMapper.updateBatchByPrimaryKey(claimFilterOtherWorkUpdateList));
        }

        if (claimFilterOtherWorkAddList.size() > Constants.BATCH_SIZE_1000) {
            List<List<ClaimFilterOtherWork>> partitions = Lists.partition(claimFilterOtherWorkAddList, Constants.BATCH_SIZE_1000);
            for(List<ClaimFilterOtherWork> partition : partitions) {
                result.addAndGet(claimFilterOtherWorkMapper.insertList(partition));
            }
        } else if( claimFilterOtherWorkAddList.size() > 0) {
            result.addAndGet(claimFilterOtherWorkMapper.insertList(claimFilterOtherWorkAddList));
        }

        return result.get();
    }

    public int importIp(File targetFile, String company) throws Exception {
        AtomicInteger result = new AtomicInteger(0);
        // 导入之前需要清空数据表
//        claimFilterOtherWorkMapper.truncateTable();
        Map<String,ClaimFilterOtherIp> claimFilterOtherIpAddMap = new HashMap<>();
        Map<String,ClaimFilterOtherIp> claimFilterOtherIpUpdateMap = new HashMap<>();
        // 指定sheetName为filter-work
        ExcelUtil.read(targetFile,1,"ip", (t, u) -> {
            if (u.isEmpty()) {
                return;
            }
            String workId = u.get(EXCEL_WORK_NUM);
            String workSocietyCode = u.get(EXCEL_WORK_SOC);
//            String company = u.get(COMPANY);
            String ipBaseNo = u.get(IP_BASE_NO);
            //不解析per字段 String per = u.get(RIGHT_PER);
            String mec = u.get(RIGHT_MEC);
            String ipName = u.get(IP_NAME);
            String extInfo = u.get(EXT_INFO);

            if(StringUtils.isBlank(workId) || StringUtils.isBlank(workSocietyCode) || StringUtils.isBlank(ipBaseNo)){
//                throw new MustException(String.format("%s字段不能爲空！", EXCEL_WORK_NUM));
                return;
            }

            // 如果mec为空，跳过该记录
            if(StringUtils.isBlank(mec)){
                return;
            }
//            if(StringUtils.isBlank(per) && StringUtils.isBlank(mec)){
////                throw new MustException(String.format("%s和%s字段不能同时爲空！", RIGHT_PER,RIGHT_MEC));
//                return;
//            }

            /*company = company.toUpperCase();
            if(!StringUtils.equalsAny(company,"YOUTUBE","SPOTIFY","APPLE","META")){
                return;
            }*/

            ClaimFilterOtherIp claimFilterOtherIp = new ClaimFilterOtherIp();
            Long workNum = Long.valueOf(workId);
            Integer soc = Integer.valueOf(workSocietyCode);
            String workUniqueKey = CommonUtil.getWorkUniqueKeyByWrkSocAndNum(soc, workNum);
            String key = company + workUniqueKey + ipBaseNo;


            List<ClaimFilterOtherIp> existList = claimFilterOtherIpService.getByParam(company, workNum, soc, ipBaseNo,null,null,null);
            if(CollectionUtils.isNotEmpty(existList)){
                claimFilterOtherIp = existList.get(0);
                claimFilterOtherIpUpdateMap.put(key,claimFilterOtherIp);
            } else {
                claimFilterOtherIpAddMap.put(key,claimFilterOtherIp);
            }

            claimFilterOtherIp.setWorkId(workNum);
            claimFilterOtherIp.setWorkSoc(soc);
            claimFilterOtherIp.setCompany(company);
            claimFilterOtherIp.setName(ipName);
            claimFilterOtherIp.setIpBaseNo(ipBaseNo);
            claimFilterOtherIp.setWorkUniqueKey(workUniqueKey);
            claimFilterOtherIp.setPer(0); // 将per字段固定设置为0
            claimFilterOtherIp.setMec(1); // 将mec字段固定设置为1
//            claimFilterOtherIp.setPer(StringUtils.isBlank(per) ? 0 : 1);
//            claimFilterOtherIp.setMec(StringUtils.isBlank(mec) ? 0 : 1);
            if (StringUtils.isNotBlank(extInfo)) {
                claimFilterOtherIp.setExtInfo(extInfo);
            }
            claimFilterOtherIp.init();
//            claimFilterOtherWorkAddList.add(claimFilterOtherWork);
        });

        List<ClaimFilterOtherIp> claimFilterOtherIpAddList = claimFilterOtherIpAddMap.values().stream().collect(Collectors.toList());
        List<ClaimFilterOtherIp> claimFilterOtherIpUpdateList = claimFilterOtherIpUpdateMap.values().stream().collect(Collectors.toList());
        if (claimFilterOtherIpUpdateList.size() > Constants.BATCH_SIZE_1000) {
            List<List<ClaimFilterOtherIp>> partitions = Lists.partition(claimFilterOtherIpUpdateList, Constants.BATCH_SIZE_1000);
            for(List<ClaimFilterOtherIp> partition : partitions) {
                result.addAndGet(claimFilterOtherIpService.updateBatchByPrimaryKey(partition));
            }
        } else if( claimFilterOtherIpUpdateList.size() > 0) {
            result.addAndGet(claimFilterOtherIpService.updateBatchByPrimaryKey(claimFilterOtherIpUpdateList));
        }

        if (claimFilterOtherIpAddList.size() > Constants.BATCH_SIZE_1000) {
            List<List<ClaimFilterOtherIp>> partitions = Lists.partition(claimFilterOtherIpAddList, Constants.BATCH_SIZE_1000);
            for(List<ClaimFilterOtherIp> partition : partitions) {
                List<String> ipBaseNos = partition.stream().map(ClaimFilterOtherIp::getIpBaseNo).collect(Collectors.toList());
                List<IpNameVO> ipNameVOList = mbrIpNameService.getPANameByIpBaseNos(ipBaseNos);
                Map<String,String> ipNameMap = ipNameVOList.stream().collect(Collectors.toMap(IpNameVO::getIpBaseNo,IpNameVO::getNameNo));
                partition.forEach(claimFilterOtherIp -> {
                    claimFilterOtherIp.setPaNameNo(ipNameMap.get(claimFilterOtherIp.getIpBaseNo()));
                });
                result.addAndGet(claimFilterOtherIpService.saveList(partition));
            }
        }else if( claimFilterOtherIpAddList.size() > 0){
            result.addAndGet(claimFilterOtherIpService.saveList(claimFilterOtherIpAddList));
        }

        return result.get();
    }

    public int importSoc(File targetFile, String company) throws Exception {
        AtomicInteger result = new AtomicInteger(0);
        // 导入之前需要清空数据表
//        claimFilterOtherWorkMapper.truncateTable();
        Map<String,ClaimFilterOthersoc> claimFilterOtherSocAddMap = new HashMap<>();
        Map<String,ClaimFilterOthersoc> claimFilterOtherSocUpdateMap = new HashMap<>();
        Map<Integer, RefSociety> refSocietyMap = refSocietyService.getAllSocietyMap();
        // 指定sheetName为filter-work
        ExcelUtil.read(targetFile,1,"soc", (t, u) -> {
            if (u.isEmpty()) {
                return;
            }
            String workId = u.get(EXCEL_WORK_NUM);
            String workSocietyCode = u.get(EXCEL_WORK_SOC);
            String socCode = u.get(SOC);
            //String per = u.get(RIGHT_PER);
            String mec = u.get(RIGHT_MEC);
            String extInfo = u.get(EXT_INFO);

            if(StringUtils.isBlank(workId) || StringUtils.isBlank(workSocietyCode) || StringUtils.isBlank(socCode)){
                return;
            }

            if(StringUtils.isBlank(mec)){
                return;
            }

//            if(StringUtils.isBlank(per) && StringUtils.isBlank(mec)){
////                throw new MustException(String.format("%s和%s字段不能同时爲空！", RIGHT_PER,RIGHT_MEC));
//                return;
//            }

            Integer soc = Integer.valueOf(socCode);
            if(!refSocietyMap.containsKey(soc)){
                return;
            }

            ClaimFilterOthersoc claimFilterOthersoc = new ClaimFilterOthersoc();
            Long workNum = Long.valueOf(workId);
            Integer workSoc = Integer.valueOf(workSocietyCode);
            RefSociety refSociety = refSocietyMap.get(soc);
            String workUniqueKey = CommonUtil.getWorkUniqueKeyByWrkSocAndNum(workSoc, workNum);
            String key = company + workUniqueKey + soc;


            List<ClaimFilterOthersoc> existList = claimFilterOtherSocService.getByParam(company, workNum, workSoc,soc, null,null);
            if(CollectionUtils.isNotEmpty(existList)){
                claimFilterOthersoc = existList.get(0);
                claimFilterOtherSocUpdateMap.put(key,claimFilterOthersoc);
            } else {
                claimFilterOtherSocAddMap.put(key,claimFilterOthersoc);
            }

            claimFilterOthersoc.setWorkId(workNum);
            claimFilterOthersoc.setWorkSoc(workSoc);
            claimFilterOthersoc.setCompany(company);
            claimFilterOthersoc.setSocietyCode(soc);
            claimFilterOthersoc.setSocietyName(refSociety.getSocietyName());
            claimFilterOthersoc.setWorkUniqueKey(workUniqueKey);
            //claimFilterOthersoc.setPer(StringUtils.isBlank(per) ? 0 : 1);
            //claimFilterOthersoc.setMec(StringUtils.isBlank(mec) ? 0 : 1);
            claimFilterOthersoc.setPer(0);//设置per固定值    
            claimFilterOthersoc.setMec(1);//设置mec固定值
            if (StringUtils.isNotBlank(extInfo)) {
                claimFilterOthersoc.setExtInfo(extInfo);
            }
            claimFilterOthersoc.init();
        });

        List<ClaimFilterOthersoc> claimFilterOthersocAddList = claimFilterOtherSocAddMap.values().stream().collect(Collectors.toList());
        List<ClaimFilterOthersoc> claimFilterOthersocUpdateList = claimFilterOtherSocUpdateMap.values().stream().collect(Collectors.toList());
        if (claimFilterOthersocUpdateList.size() > Constants.BATCH_SIZE_1000) {
            List<List<ClaimFilterOthersoc>> partitions = Lists.partition(claimFilterOthersocUpdateList, Constants.BATCH_SIZE_1000);
            for(List<ClaimFilterOthersoc> partition : partitions) {
                result.addAndGet(claimFilterOtherSocService.updateBatchByPrimaryKey(partition));
            }
        } else if( claimFilterOthersocUpdateList.size() > 0){
            result.addAndGet(claimFilterOtherSocService.updateBatchByPrimaryKey(claimFilterOthersocUpdateList));
        }

        if (claimFilterOthersocAddList.size() > Constants.BATCH_SIZE_1000) {
            List<List<ClaimFilterOthersoc>> partitions = Lists.partition(claimFilterOthersocAddList, Constants.BATCH_SIZE_1000);
            for(List<ClaimFilterOthersoc> partition : partitions) {
                result.addAndGet(claimFilterOtherSocService.saveList(partition));
            }
        }else if( claimFilterOthersocAddList.size() > 0){
            result.addAndGet(claimFilterOtherSocService.saveList(claimFilterOthersocAddList));
        }

        return result.get();
    }

    @Override
    public List<ClaimFilterOtherWork> getByParam(String company, Long workId, Integer workSoc, Integer per, Integer mec) {
        Example example = new Example(ClaimFilterOtherWork.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("company", company);
        if(null != workId) {
            criteria.andEqualTo("workId", workId);
        }
        if(null != workSoc) {
            criteria.andEqualTo("workSoc", workSoc);
        }
        if(per != null){
            criteria.andEqualTo("per", per);
        }
        if(mec != null){
            criteria.andEqualTo("mec", mec);
        }
        example.orderBy("amendTime").desc();
        return claimFilterOtherWorkMapper.selectByExample(example);
    }

    public void deleteByCompany(String company){
        claimFilterOtherWorkMapper.deleteByCompany(company);
    }
}