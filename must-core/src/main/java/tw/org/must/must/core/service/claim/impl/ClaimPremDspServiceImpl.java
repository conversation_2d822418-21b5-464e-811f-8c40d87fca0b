package tw.org.must.must.core.service.claim.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.model.claim.ClaimPremDsp;
import tw.org.must.must.mapper.claim.ClaimPremDspMapper;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.claim.ClaimPremDspService;

@Service
public class ClaimPremDspServiceImpl extends BaseServiceImpl<ClaimPremDsp> implements ClaimPremDspService {

    private final ClaimPremDspMapper claimPremDspMapper;

    @Autowired
    public ClaimPremDspServiceImpl(ClaimPremDspMapper claimPremDspMapper) {
        super(claimPremDspMapper);
        this.claimPremDspMapper = claimPremDspMapper;
    }

    @Override
    public ClaimPremDsp getClaimPremDspByUniqueKey(String dataUuniqueKey) {
        if (StringUtils.isBlank(dataUuniqueKey)) {
            return null;
        }

        Example example = new Example(ClaimPremDsp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("uniqueKey", dataUuniqueKey);
        return claimPremDspMapper.selectOneByExample(example);
    }

    @Override
    public ClaimPremDsp getClaimPremDspByWorkUniqueKey(String workUniqueKey) {
        if (StringUtils.isBlank(workUniqueKey) || !workUniqueKey.contains("-")) {
            return null;
        }
        String[] split = workUniqueKey.split("-");
        Example example = new Example(ClaimPremDsp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("cWorkcode", Long.valueOf(split[1]));
        criteria.andEqualTo("cWorkSoc", Integer.valueOf(split[0]));
        return claimPremDspMapper.selectOneByExample(example);
    }
}