package tw.org.must.must.core.service.claim.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.org.must.must.common.base.IdBaseServiceImpl;
import tw.org.must.must.core.service.claim.ClaimProductService;
import tw.org.must.must.mapper.claim.ClaimProductMapper;
import tw.org.must.must.model.claim.ClaimProduct;

@Service
public class ClaimProductServiceImpl extends IdBaseServiceImpl<ClaimProduct> implements ClaimProductService {

	private final ClaimProductMapper claimProductMapper;

    @Autowired
    public ClaimProductServiceImpl(ClaimProductMapper claimProductMapper) {
        super(claimProductMapper);
        this.claimProductMapper = claimProductMapper;
    }
}