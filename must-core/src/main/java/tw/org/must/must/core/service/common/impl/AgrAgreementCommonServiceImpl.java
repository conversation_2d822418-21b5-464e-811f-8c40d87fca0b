package tw.org.must.must.core.service.common.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.org.must.must.core.service.agr.*;
import tw.org.must.must.core.service.common.AgrAgreementCommonService;
import tw.org.must.must.core.task.transfer.DealAgrHandle;
import tw.org.must.must.model.agr.*;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class AgrAgreementCommonServiceImpl implements AgrAgreementCommonService {

    @Autowired
    private AgrContentService agrContentService;

    @Autowired
    private AgrAgreementExtendService agrAgreementExtendService;

    @Autowired
    private AgrAgreementRemarkService agrAgreementRemarkService;

    @Autowired
    private AgrAgreementSourceService agrAgreementSourceService;

    @Autowired
    private AgrAgreementTerritoryService agrAgreementTerritoryService;

    @Autowired
    private AgrAssigneeService agrAssigneeService;

    @Autowired
    private AgrAssignorService agrAssignorService;

    @Override
    public void dealAgrAssignor(DealAgrHandle dealAgrHandle) {
        List<AgrAssignor> delMoneyAgrAssignorList = dealAgrHandle.getDelMoneyAgrAssignorList();
        if(null != delMoneyAgrAssignorList && delMoneyAgrAssignorList.size()>0){
            List<Long> assignorIds = delMoneyAgrAssignorList.stream().map(AgrAssignor::getId).collect(Collectors.toList());
            agrAssignorService.delete(assignorIds);
        }

        List<AgrAssignor> addMoneyAgrAssignorList = dealAgrHandle.getAddMoneyAgrAssignorList();
        if(null != addMoneyAgrAssignorList && addMoneyAgrAssignorList.size()>0){
            agrAssignorService.addList(addMoneyAgrAssignorList);
        }

        List<AgrAssignor> updMoneyAgrAssignorList = dealAgrHandle.getUpdMoneyAgrAssignorList();
        if(null != updMoneyAgrAssignorList && updMoneyAgrAssignorList.size()>0){
            agrAssignorService.updateBatchByPrimaryKey(updMoneyAgrAssignorList);
        }

    }

    @Override
    public void dealAgrAssignee(DealAgrHandle dealAgrHandle) {
        List<AgrAssignee> delMoneyAgrAssigneeList = dealAgrHandle.getDelMoneyAgrAssigneeList();
        if(null != delMoneyAgrAssigneeList && delMoneyAgrAssigneeList.size()>0){
            List<Long> assigneeIds = delMoneyAgrAssigneeList.stream().map(AgrAssignee::getId).collect(Collectors.toList());
            agrAssigneeService.delete(assigneeIds);
        }

        List<AgrAssignee> addMoneyAgrAssigneeList = dealAgrHandle.getAddMoneyAgrAssigneeList();
        if(null != addMoneyAgrAssigneeList && addMoneyAgrAssigneeList.size()>0){
            agrAssigneeService.addList(addMoneyAgrAssigneeList);
        }

        List<AgrAssignee> updMoneyAgrAssigneeList = dealAgrHandle.getUpdMoneyAgrAssigneeList();
        if(null != updMoneyAgrAssigneeList && updMoneyAgrAssigneeList.size()>0){
            agrAssigneeService.updateBatchByPrimaryKey(updMoneyAgrAssigneeList);
        }

    }

    @Override
    public void dealAgrAgrAgreementExtend(DealAgrHandle dealAgrHandle) {
        List<AgrAgreementExtend> delMoneyAgrAgreementExtendList = dealAgrHandle.getDelMoneyAgrAgreementExtendList();
        if(null != delMoneyAgrAgreementExtendList && delMoneyAgrAgreementExtendList.size()>0){
            List<Long> agrAgreementExtendIds = delMoneyAgrAgreementExtendList.stream().map(AgrAgreementExtend::getId).collect(Collectors.toList());
            agrAgreementExtendService.delete(agrAgreementExtendIds);
        }

        List<AgrAgreementExtend> addMoneyAgrAgreementExtendList = dealAgrHandle.getAddMoneyAgrAgreementExtendList();
        if(null != addMoneyAgrAgreementExtendList && addMoneyAgrAgreementExtendList.size()>0){
            agrAgreementExtendService.addList(addMoneyAgrAgreementExtendList);
        }

        List<AgrAgreementExtend> updMoneyAgrAgreementExtendList = dealAgrHandle.getUpdMoneyAgrAgreementExtendList();
        if(null != updMoneyAgrAgreementExtendList && updMoneyAgrAgreementExtendList.size()>0){
            agrAgreementExtendService.updateBatchByPrimaryKey(updMoneyAgrAgreementExtendList);
        }

    }

    @Override
    public void dealAgrAgreementRemark(DealAgrHandle dealAgrHandle) {
        List<AgrAgreementRemark> delMoneyAgrAgreementRemarkList = dealAgrHandle.getDelMoneyAgrAgreementRemarkList();
        if(null != delMoneyAgrAgreementRemarkList && delMoneyAgrAgreementRemarkList.size()>0){
            List<Long> agrRemarkIds = delMoneyAgrAgreementRemarkList.stream().map(AgrAgreementRemark::getId).collect(Collectors.toList());
            agrAgreementRemarkService.delete(agrRemarkIds);
        }

        List<AgrAgreementRemark> addMoneyAgrAgreementRemarkList = dealAgrHandle.getAddMoneyAgrAgreementRemarkList();
        if(null != addMoneyAgrAgreementRemarkList && addMoneyAgrAgreementRemarkList.size()>0){
            agrAgreementRemarkService.addList(addMoneyAgrAgreementRemarkList);
        }

        List<AgrAgreementRemark> updMoneyAgrAgreementRemarkList = dealAgrHandle.getUpdMoneyAgrAgreementRemarkList();
        if(null != updMoneyAgrAgreementRemarkList && updMoneyAgrAgreementRemarkList.size()>0){
            agrAgreementRemarkService.updateBatchByPrimaryKey(updMoneyAgrAgreementRemarkList);
        }

    }

    @Override
    public void dealAgrAgreementSource(DealAgrHandle dealAgrHandle) {
        List<AgrAgreementSource> delMoneyAgrAgreementSourceList = dealAgrHandle.getDelMoneyAgrAgreementSourceList();
        if(null != delMoneyAgrAgreementSourceList && delMoneyAgrAgreementSourceList.size()>0){
            List<Long> agrSourceIds = delMoneyAgrAgreementSourceList.stream().map(AgrAgreementSource::getId).collect(Collectors.toList());
            agrAgreementSourceService.delete(agrSourceIds);
        }

        List<AgrAgreementSource> addMoneyAgrAgreementSourceList = dealAgrHandle.getAddMoneyAgrAgreementSourceList();
        if(null != addMoneyAgrAgreementSourceList && addMoneyAgrAgreementSourceList.size()>0){
            agrAgreementSourceService.addList(addMoneyAgrAgreementSourceList);
        }

        List<AgrAgreementSource> updMoneyAgrAgreementSourceList = dealAgrHandle.getUpdMoneyAgrAgreementSourceList();
        if(null != updMoneyAgrAgreementSourceList && updMoneyAgrAgreementSourceList.size()>0){
            agrAgreementSourceService.updateBatchByPrimaryKey(updMoneyAgrAgreementSourceList);
        }

    }

    @Override
    public void dealAgrAgreementTerritory(DealAgrHandle dealAgrHandle) {
        List<AgrAgreementTerritory> delMoneyAgrAgreementTerritoryList = dealAgrHandle.getDelMoneyAgrAgreementTerritoryList();
        if(null != delMoneyAgrAgreementTerritoryList && delMoneyAgrAgreementTerritoryList.size()>0){
            List<Long> territoryIds = delMoneyAgrAgreementTerritoryList.stream().map(AgrAgreementTerritory::getId).collect(Collectors.toList());
            agrAgreementTerritoryService.delete(territoryIds);
        }

        List<AgrAgreementTerritory> addMoneyAgrAgreementTerritoryList = dealAgrHandle.getAddMoneyAgrAgreementTerritoryList();
        if(null != addMoneyAgrAgreementTerritoryList && addMoneyAgrAgreementTerritoryList.size()>0){
            agrAgreementTerritoryService.addList(addMoneyAgrAgreementTerritoryList);
        }

        List<AgrAgreementTerritory> updMoneyAgrAgreementTerritoryList = dealAgrHandle.getUpdMoneyAgrAgreementTerritoryList();
        if(null != updMoneyAgrAgreementTerritoryList && updMoneyAgrAgreementTerritoryList.size()>0){
            agrAgreementTerritoryService.updateBatchByPrimaryKey(updMoneyAgrAgreementTerritoryList);
        }

    }
}

