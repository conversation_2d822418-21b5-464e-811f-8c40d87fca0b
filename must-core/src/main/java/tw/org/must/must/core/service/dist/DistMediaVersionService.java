package tw.org.must.must.core.service.dist;

import java.util.List;

import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.model.dist.DistMediaVersion;

public interface DistMediaVersionService extends BaseService<DistMediaVersion> {

	List<DistMediaVersion> listDistMediaVersionWithPage(Integer pageNum, Integer pageSize);

	DistMediaVersion saveDistMediaVersion(DistMediaVersion distMediaVersion);

	List<DistMediaVersion> getDistMediaVersionListByVersionName(String versionName);

}