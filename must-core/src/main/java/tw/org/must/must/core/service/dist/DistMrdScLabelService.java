package tw.org.must.must.core.service.dist;


import tw.org.must.must.model.dist.DistMrdScLabel;
import tw.org.must.must.common.base.BaseService;

import java.util.List;

public interface DistMrdScLabelService extends BaseService<DistMrdScLabel> {
    List<DistMrdScLabel> listDistMrdScLabelWithPage(Long id,String producerCode, String producerName, String labelName);
    void editDistMrdScLabel(DistMrdScLabel distMrdScLabel);
    void checkUsage(Long id);
}