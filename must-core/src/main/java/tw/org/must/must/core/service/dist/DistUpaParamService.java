package tw.org.must.must.core.service.dist;


import tw.org.must.must.model.dist.DistUpaParam;

import java.util.List;
import java.util.Map;

import tw.org.must.must.common.base.BaseService;

public interface DistUpaParamService extends BaseService<DistUpaParam> {
	
	/**
	 * 保存数据
	 * @param param
	 */
	void insertUpaParam(DistUpaParam param);
	
	/**
	 * 根据清单ID查询对应的upa信息
	 * @param paramInfoId
	 * @return
	 */
	DistUpaParam findUpaParamByInfoId(Long paramInfoId);

	Map<String, DistUpaParam> selectDistUpaParamMapByDistNoList(List<String> distNoList);

	DistUpaParam getByDistNo(String distNo);
}