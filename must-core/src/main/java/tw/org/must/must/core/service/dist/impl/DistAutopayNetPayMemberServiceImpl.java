
package tw.org.must.must.core.service.dist.impl;
import com.github.pagehelper.PageHelper;
import net.sf.jasperreports.engine.JRException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.common.util.DateUtils;
import tw.org.must.must.common.util.ExampleUtil;
import tw.org.must.must.common.util.LocalCommonMethodUtils;
import tw.org.must.must.common.util.MapUtil;
import tw.org.must.must.common.util.excel.GenerateExcelUtil;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.core.report.PdfDownloadUtil;
import tw.org.must.must.core.service.dist.DistAutopayNetPayMemberService;
import tw.org.must.must.core.service.export.DistAutoNetPayExportService;
import tw.org.must.must.mapper.dist.DistAutopayMemberRetainMapper;
import tw.org.must.must.mapper.dist.DistAutopayNetPayMemberMapper;
import tw.org.must.must.model.dist.DistAutopayMemberRetain;
import tw.org.must.must.model.dist.DistAutopayNetPayMember;
import tw.org.must.must.model.dist.vo.DistAutoPay750Request;
import tw.org.must.must.model.dist.vo.Fsris100Request;
import tw.org.must.must.model.dist.vo.IncomeMemberNetVo;
import tw.org.must.must.model.dist.vo.v750.*;
import tw.org.must.must.model.dist.vo.v800.DistNoDetail800;
import tw.org.must.must.model.dist.vo.v800.mem.BasesMem800;
import tw.org.must.must.model.dist.vo.v800.mem.DataMem800;
import tw.org.must.must.model.dist.vo.v800.mem.DetailMem800;
import tw.org.must.must.model.dist.vo.v800.mem.DistNoDetailMem800;
import tw.org.must.must.model.report.JasperParam;
import tw.org.must.must.model.report.SummaryTotal;
import tw.org.must.must.model.report.distAutoPay.*;
import tw.org.must.report.export.PdfReport;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class DistAutopayNetPayMemberServiceImpl extends BaseServiceImpl<DistAutopayNetPayMember> implements DistAutopayNetPayMemberService {

    private final DistAutopayNetPayMemberMapper distAutopayNetPayMemberMapper;
    private final DistAutopayMemberRetainMapper distAutopayMemberRetainMapper;
    @Lazy
    @Autowired
    private DistAutoNetPayExportService distAutoNetPayExportService;

//    String EXPORT_FILE_PATH = "/home/<USER>/webFile/upload/shareListFile/PAY_PDF";

//    private final String TV_ROYALTIES = "Tv Royalties" ;
    private final String ROY_SUB_TOTAL = "royaltyAmount";
    private final String IP_TYPE = "ipType" ;
    private final String PA_NAME_NO = "paNameNo" ;
    private final String MUST_CURRENT_SUCCESSOR_NAME = "Must Current Successor Name";
    private final String MUST_CURRENT_SUCCESSOR_TO_WITHDRAWAL_PUBLISHER = "Must Current Successor to Withdrawal Publisher" ;
    private final String ADJ_SUB_TOTAL = "adjRoyaltyAmount" ;
    private final String TRANSACTION_TAXABLE_INCOME = "taxableAmount ";
    private final String TOTAL_COMMISSION_AMT = "commissionAmount";
    private final String ADDITIONAL_COMMISSION_AMT = "adjCommissionAmount" ;
    private final String NET_PAYMENT = "payAmount ";
//    private final String REF_IP_BASE_NO = "refIpBaseNo" ;


    private static final Logger logger = LoggerFactory.getLogger(DistAutopayNetPayMemberServiceImpl.class);

    @Autowired
    public DistAutopayNetPayMemberServiceImpl(DistAutopayNetPayMemberMapper distAutopayNetPayMemberMapper, DistAutopayMemberRetainMapper distAutopayMemberRetainMapper) {
        super(distAutopayNetPayMemberMapper);
        this.distAutopayNetPayMemberMapper = distAutopayNetPayMemberMapper;
        this.distAutopayMemberRetainMapper = distAutopayMemberRetainMapper;
    }


    @Override
    public List<DistAutopayNetPayMember> lisDistAutopayNetPayMember(Integer pageNum, Integer pageSize, String ipBaseNo, String ipType, String paName, String distNo) {
        PageHelper.startPage(pageNum, pageSize);
        Example example = new Example(DistAutopayNetPayMember.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(ipBaseNo)) {
            criteria.andLike("ipBaseNo", ExampleUtil.exampleLikeAll(ipBaseNo));
        }
        if (StringUtils.isNotBlank(ipType)) {
            criteria.andLike("ipType", ExampleUtil.exampleLikeAll(ipType));
        }
        if (StringUtils.isNotBlank(paName)) {
            criteria.andLike("paName", ExampleUtil.exampleLikeAll(paName));
        }
        if (StringUtils.isNotBlank(distNo)) {
            criteria.andLike("distNo", ExampleUtil.exampleLikeAll(distNo));
        }
        criteria.andEqualTo("pay", "N");//未支付
        example.orderBy("createTime").desc();
        return distAutopayNetPayMemberMapper.selectByExample(example);
    }

    @Override
    public List<DistAutopayNetPayMember> lisDistAutopayNetPayMemberForPay(Integer pageNum, Integer pageSize, String autopayNo, String ipBaseNo, String ipType, String paNameNo, String paName, String distNo, Integer statYear, Integer endYear) {
        PageHelper.startPage(pageNum, pageSize);
        Example example = new Example(DistAutopayNetPayMember.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(autopayNo)) {
            criteria.andEqualTo("autopayNo", autopayNo);
        }
        if (StringUtils.isNotBlank(ipBaseNo)) {
            criteria.andLike("ipBaseNo", ExampleUtil.exampleLikeAll(ipBaseNo));
        }
        if (StringUtils.isNotBlank(ipType)) {
            criteria.andLike("ipType", ExampleUtil.exampleLikeAll(ipType));
        }
        if (StringUtils.isNotBlank(paName)) {
            criteria.andLike("paName", ExampleUtil.exampleLikeAll(paName));
        }
        if (StringUtils.isNotBlank(paNameNo)) {
            criteria.andEqualTo("paNameNo", paNameNo);
        }
        if (StringUtils.isNotBlank(distNo)) {
            criteria.andLike("distNo", ExampleUtil.exampleLikeAll(distNo));
        }
        if (Objects.nonNull(statYear)) {
            criteria.andGreaterThanOrEqualTo("taxYear", statYear);
        }
        if (Objects.nonNull(endYear)) {
            criteria.andLessThanOrEqualTo("taxYear", endYear);
        }
        criteria.andEqualTo("pay", "Y");//已支付
        example.orderBy("createTime").desc();
        return distAutopayNetPayMemberMapper.selectByExample(example);
    }

    @Override
    public IncomeMemberNetVo lisDistAutopayNetPayMember(String autopayNo, String ipBaseNo, String ipType, String paNameNo, String paName, String distNo, Integer statYear, Integer endYear) {
        if (StringUtils.isBlank(ipBaseNo) && StringUtils.isBlank(paName)) {
            throw new MustException(ResultCode.Unknown_Exception.getCode(), "ip base no和name至少一个不能為空!");
        }
        Example example = new Example(DistAutopayNetPayMember.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(autopayNo)) {
            criteria.andEqualTo("autopayNo", autopayNo);
        }
        if (StringUtils.isNotBlank(ipBaseNo)) {
            criteria.andLike("ipBaseNo", ExampleUtil.exampleLikeAll(ipBaseNo));
        }
        if (StringUtils.isNotBlank(ipType)) {
            criteria.andLike("ipType", ExampleUtil.exampleLikeAll(ipType));
        }
        if (StringUtils.isNotBlank(paName)) {
            criteria.andLike("paName", ExampleUtil.exampleLikeAll(paName));
        }
        if (StringUtils.isNotBlank(paNameNo)) {
            criteria.andEqualTo("paNameNo", paNameNo);
        }
        if (StringUtils.isNotBlank(distNo)) {
            criteria.andLike("distNo", ExampleUtil.exampleLikeAll(distNo));
        }
        if (Objects.nonNull(statYear)) {
            criteria.andGreaterThanOrEqualTo("taxYear", statYear);
        }
        if (Objects.nonNull(endYear)) {
            criteria.andLessThanOrEqualTo("taxYear", endYear);
        }
        criteria.andEqualTo("pay", "Y");//已支付
        example.orderBy("createTime").desc();
        List<DistAutopayNetPayMember> distAutopayNetPayMemberList = distAutopayNetPayMemberMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(distAutopayNetPayMemberList)) {
            return null;
        }
        IncomeMemberNetVo incomeMemberNetVo = new IncomeMemberNetVo();
        incomeMemberNetVo.setDistAutopayNetPayMemberList(distAutopayNetPayMemberList);
        //统计
        incomeMemberNetVo.setAdvance(distAutopayNetPayMemberList.stream().map(DistAutopayNetPayMember::getAdvance).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeMemberNetVo.setPayAmount(distAutopayNetPayMemberList.stream().map(DistAutopayNetPayMember::getPayAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeMemberNetVo.setLocalTaxableIncome(distAutopayNetPayMemberList.stream().map(DistAutopayNetPayMember::getLocalTaxableIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeMemberNetVo.setOverseasTaxableIncome(distAutopayNetPayMemberList.stream().map(DistAutopayNetPayMember::getOverseasTaxableIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeMemberNetVo.setWithheldTax(distAutopayNetPayMemberList.stream().map(DistAutopayNetPayMember::getWithheldTax).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeMemberNetVo.setCommissionAmount(distAutopayNetPayMemberList.stream().map(DistAutopayNetPayMember::getCommissionAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeMemberNetVo.setNonTaxableIncome(distAutopayNetPayMemberList.stream().map(DistAutopayNetPayMember::getNonTaxableIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeMemberNetVo.setRoyaltyAmount(distAutopayNetPayMemberList.stream().map(DistAutopayNetPayMember::getRoyaltyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeMemberNetVo.setDeduction(distAutopayNetPayMemberList.stream().map(DistAutopayNetPayMember::getDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));

        return incomeMemberNetVo;
    }

    @Override
    public Integer distAutopayNetPayMemberSetInvNo(DistAutopayNetPayMember distAutopayNetPayMember) {
        /*if(StringUtils.isBlank(distAutopayNetPayMember.getIds())){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),String.format(ResultCode.PARAMTER_IS_NULL.getMsg(),"ids"));
        }*/
        if (StringUtils.isBlank(distAutopayNetPayMember.getInvNo())) {
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "invNo"));
        }
        if (distAutopayNetPayMember.getSalesTaxRate() == null) {
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "SalesTaxRate"));
        }
        Example example = new Example(DistAutopayNetPayMember.class);
        Example.Criteria criteria = example.createCriteria();
//        criteria.andIn("id", Arrays.asList(distAutopayNetPayMember.getIds().split(",")));
        DistAutopayNetPayMember updateDistAutopayNetPayMember = new DistAutopayNetPayMember();
        updateDistAutopayNetPayMember.setInvNo(distAutopayNetPayMember.getInvNo());
        updateDistAutopayNetPayMember.setSalesTaxRate(distAutopayNetPayMember.getSalesTaxRate());
        return distAutopayNetPayMemberMapper.updateByExampleSelective(updateDistAutopayNetPayMember, example);
    }

    @Override
    public List<Map<String, Object>> groupDistAutopayNetPayMember(String ipBaseNo, String startTime, String endTime) {
        if (StringUtils.isBlank(ipBaseNo)) {
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "ipBaseNo"));
        }
        Integer taxStart = null;
        Integer taxEnd = null;
        if (StringUtils.isNoneBlank(startTime)) {
            taxStart = Integer.valueOf(StringUtils.substring(startTime, 0, 4));
        }
        if (StringUtils.isNoneBlank(endTime)) {
            taxEnd = Integer.valueOf(StringUtils.substring(endTime, 0, 4));
        }
        Map<String, Object> param = new HashMap<>();
        param.put("ipBaseNo", ipBaseNo);
        param.put("taxStart", taxStart);
        param.put("taxEnd", taxEnd);
        List<Map<String, Object>> returnMap = distAutopayNetPayMemberMapper.groupDistAutopayNetPayMember(param);
        return returnMap;
    }

    @Override
    public Integer insertList(List<DistAutopayNetPayMember> distAutopayNetPayMemberList) {
        if (CollectionUtils.isEmpty(distAutopayNetPayMemberList)) {
            return 0;
        }
        return distAutopayNetPayMemberMapper.insertList(distAutopayNetPayMemberList);
    }

    @Override
    public List<DistAutopayNetPayMember> selectByAutoPay(String autopayNo, String paNameNo, String distNo, List<String> list) {
        Example example = new Example(DistAutopayNetPayMember.class);

        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("autopayNo", autopayNo);
        if (StringUtils.isNotBlank(paNameNo)) {
            criteria.andEqualTo("paNameNo", paNameNo);

        }
        if (StringUtils.isNotBlank(distNo)) {
            criteria.andEqualTo("distNo", distNo);
        }
        if (!CollectionUtils.isEmpty(list)) {
            criteria.andIn("paymentMethod", list);
        }
        return distAutopayNetPayMemberMapper.selectByExample(example);
    }

    @Override
    public List<DistAutopayNetPayMember> selectByAutoPay(String autopayNo, List<String> paNameNoList, String distNo, List<String> list, Date payDate) {
        Example example = new Example(DistAutopayNetPayMember.class);

        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("autopayNo", autopayNo);
        if (StringUtils.isNotBlank(distNo)) {
            criteria.andEqualTo("distNo", distNo);
        }
        if(payDate != null){
            criteria.andEqualTo("payDate",payDate);
        }
        if (!CollectionUtils.isEmpty(paNameNoList)) {
            criteria.andIn("paNameNo", paNameNoList);

        }
        if (!CollectionUtils.isEmpty(list)) {
            criteria.andIn("paymentMethod", list);
        }

        return distAutopayNetPayMemberMapper.selectByExample(example);
    }

    @Override
    public List<DistAutopayNetPayMember> selectNetPayMemberForFsris100(String autopayNo, List<String> paNameNoList, List<String> distNos, Date startDate, Date endDate) {
        Example example = new Example(DistAutopayNetPayMember.class);

        Example.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotEmpty(autopayNo)){
            criteria.andEqualTo("autopayNo", autopayNo);
        }
        if (CollectionUtils.isEmpty(distNos)) {
            criteria.andIn("distNo", distNos);
        }
        if(startDate != null){
            criteria.andGreaterThanOrEqualTo("payDate",startDate);
        }
        if(endDate != null){
            criteria.andLessThan("payDate",endDate);
        }
        if (!CollectionUtils.isEmpty(paNameNoList)) {
            criteria.andIn("paNameNo", paNameNoList);

        }

        return distAutopayNetPayMemberMapper.selectByExample(example);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public Integer redo(Long id) {
        DistAutopayNetPayMember distAutopayNetPayMember = distAutopayNetPayMemberMapper.selectByPrimaryKey(id);
        DistAutopayMemberRetain distAutopayMemberRetain = new DistAutopayMemberRetain();
//        BeanUtils.copyProperties(distAutopayNetPayMember, distAutopayMemberRetain);
//        distAutopayMemberRetain.setId(null);
//        distAutopayMemberRetain.init();
        distAutopayMemberRetain.setPay("Y");

        Example example = new Example(DistAutopayNetPayMember.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id",distAutopayNetPayMember.getRetainId());
        distAutopayMemberRetainMapper.updateByExampleSelective(distAutopayMemberRetain,example);
        return distAutopayNetPayMemberMapper.delete(distAutopayNetPayMember);
    }

    @Override
    public List<DistAutoPay750Vo> getDistAutoPay750List(String autopayNo) {
        return distAutopayNetPayMemberMapper.getDistAutoPay750List(autopayNo, null, null);
    }

    public List<DistAutoPay750Vo> getDistAutoPay750List(String autopayNo, String orderWay) {
        return distAutopayNetPayMemberMapper.getDistAutoPay750List(autopayNo, orderWay, null);
    }

    public List<DistAutoPay750Vo> getDistAutoPay750List(String autopayNo, String orderWay, List<String> paNameNoList) {
        return distAutopayNetPayMemberMapper.getDistAutoPay750List(autopayNo, orderWay, paNameNoList);
    }

    @Override
    public List<DistAutoPay750Vo> getDistAutoPay880List(String autopayNo, String ipBaseNo, String paNameNo, String distNo) {
        return distAutopayNetPayMemberMapper.getDistAutoPay880List(autopayNo, ipBaseNo, paNameNo, distNo);
    }

    @Override
    public List<DistAutopayNetPayMember> getIpBaseNoList(String paymentNo, List<String> ipBaseNoList) {
        Example example = new Example(DistAutopayNetPayMember.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(paymentNo)) {
            criteria.andEqualTo("autopayNo", paymentNo);
        }
        if (!CollectionUtils.isEmpty(ipBaseNoList)) {
            criteria.andIn("ipBaseNo", ipBaseNoList);
        }
        return distAutopayNetPayMemberMapper.selectByExample(example);
    }

    @Override
    public void reportDistAutoPay750(DistAutoPay750Request request,
                                     HttpServletResponse httpServletResponse) throws FileNotFoundException, JRException {
        String JRXML_PATH = DistAutopayNetPayMemberServiceImpl.class.getResource("/ireport").getPath();
        List<DistAutoPay750Vo> distAutoPay750VoList = this.getDistAutoPay750List(request.getAutopayNo(), request.getOrderWay(), request.getPaNameNoList());
        if (CollectionUtils.isEmpty(distAutoPay750VoList)) {
            throw new MustException("無可導出數據!");
        }


        Bases750 bases750 = getBases750(distAutoPay750VoList,request.getShowDistNo());
        Map<String,Object> param = new HashMap<>();
        param.put("date", DateUtils.formatDate(new Date(), "yyyyMMdd HH:mm"));
        param.put("reportId", "ERDIS750");
        param.put("autoPayNo", request.getAutopayNo());
        param.put("distribution", StringUtils.isBlank(request.getDistNoDetails()) ? "" : StringUtils.replace(request.getDistNoDetails(), ";", " "));
        param.put("count", String.valueOf(bases750.getDetailList().size()));
        BigDecimal totalBalanceValue = distAutoPay750VoList.stream()
                .map(DistAutoPay750Vo::getBalanceA)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        DecimalFormat df = new DecimalFormat("#,##0.00");
        String totalBalanceFormatted = df.format(totalBalanceValue);
        param.put("totalBalance", totalBalanceFormatted);
        //param.put("totalBalance", "$".concat(distAutoPay750VoList.stream().map(DistAutoPay750Vo::getBalanceA).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP).toString()));
        param.put("summaryTotal", request.getShowDistNo() ? "Summary Total" : " ");
        param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
        param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");

        // 處理新增的參數
        param.put("paNameNoList", request.getPaNameNoList());
        param.put("nameList", request.getNameList());
        param.put("showDistNo", request.getShowDistNo() != null ? request.getShowDistNo() : false);

        List<JasperParam> jaspers = new ArrayList<>();
        jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(bases750).getBytes()));
        PdfDownloadUtil.download(jaspers, "/ireport/jrxml/distAutoPays/DistAutoPay750_main.jrxml", httpServletResponse, request.getAutopayNo());
    }

    @Override
    public void reportDistAutoPay880(String autopayNo, String detail, HttpServletResponse httpServletResponse, String ipBaseNo, String paNameNo, String distNo) throws FileNotFoundException, JRException {
        String JRXML_PATH = DistAutopayNetPayMemberServiceImpl.class.getResource("/ireport").getPath();
        List<DistAutoPay750Vo> distAutoPay750VoList = this.getDistAutoPay880List(autopayNo, ipBaseNo, paNameNo, distNo);
        if (CollectionUtils.isEmpty(distAutoPay750VoList)) {
            throw new MustException("無可導出數據!");
        }
        Bases750 bases750 = getBases880(distAutoPay750VoList);
        Map<String,Object> param = new HashMap<>();
        param.put("date", DateUtils.formatDate(new Date(), "yyyyMMdd HH:mm"));
        param.put("reportId", "ERDIS880");
        param.put("autoPayNo", autopayNo);
        param.put("distribution", distNo);
        param.put("count", String.valueOf(bases750.getDetailList().size()));
        BigDecimal totalBalanceValue = distAutoPay750VoList.stream()
                .map(DistAutoPay750Vo::getBalanceA)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        DecimalFormat df = new DecimalFormat("#,##0.00");
        String totalBalanceFormatted = df.format(totalBalanceValue);
        param.put("totalBalance", totalBalanceFormatted);
        param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
        param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");
        List<JasperParam> jaspers = new ArrayList<>();
        jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(bases750).getBytes()));
        PdfDownloadUtil.download(jaspers, "/ireport/jrxml/distAutoPays/DistAutoPay880_main.jrxml", httpServletResponse, autopayNo);
    }

    @Override
    public void reportDistAutoPayMem800(HttpServletResponse httpServletResponse, String autopayNo, String autopayDescription, String paNameNo, String distNo) throws Exception {
        String JRXML_PATH = DistAutopayNetPayMemberServiceImpl.class.getResource("/ireport").getPath();
        //获得测试数据

        DataMem800 dataMem800 = sealDistAutoPayMem800Data(autopayNo, paNameNo, distNo);
        Map<String,Object> param = new HashMap<>();
        param.put("date", dataMem800.getDate());
        param.put("reportId", dataMem800.getReportId());
        param.put("autoPayNo", autopayNo);
        param.put("distribution", autopayDescription);
        param.put("count", dataMem800.getCount());
        param.put("total", dataMem800.getTotal());
        param.put("paymentTotal", dataMem800.getPaymentTotal());
        param.put("handlingCharges", dataMem800.getHandlingCharges());
        param.put("taxAmount", dataMem800.getTaxAmount());
        param.put("commissionAmount", dataMem800.getCommissionAmount());

        param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
        param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");

        //此处是测试蒋两个pdf合并在一起，如果只数据一个pdf的，则传入一个JasperParam即可
        List<JasperParam> jaspers = new ArrayList<>();
        jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(dataMem800.getBases()).getBytes()));

        //调用生成pdf方法
        PdfDownloadUtil.download(jaspers, "/ireport/jrxml/distAutoPays/DistAutoPayMem800_main.jrxml", httpServletResponse, autopayNo);
    }

    private DataMem800 sealDistAutoPayMem800Data(String autopayNo, String paNameNo, String distNo) {
        DataMem800 dataMem800 = new DataMem800();
        BasesMem800 basesMem800 = new BasesMem800();
        List<DistNoDetailMem800> distNoDetailMem800TotalList = new ArrayList<>();
        List<DetailMem800> detailMem800List = distAutopayNetPayMemberMapper.getDetailMem800(autopayNo, paNameNo, distNo);
        if (CollectionUtils.isEmpty(detailMem800List)) {
            throw new MustException(ResultCode.Unknown_Exception.getCode(), "無可導出數據!");
        }
        //detailList
        detailMem800List.forEach(detail800 -> {
            List<DistNoDetailMem800> distNoDetailMem800List = distAutopayNetPayMemberMapper.getDistNoDetailMem800(autopayNo, detail800.getPaNameNo(), distNo);
            BigDecimal royalties = distNoDetailMem800List.stream().
                    map(distNoDetail800 -> distNoDetail800.getORoyalties() == null ? BigDecimal.ZERO : new BigDecimal(distNoDetail800.getORoyalties())).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            detail800.setRoyalties(royalties.toString());
            BigDecimal payAbleAmount = distNoDetailMem800List.stream().
                    map(distNoDetail800 -> distNoDetail800.getOPayAbleAmount() == null ? BigDecimal.ZERO : new BigDecimal(distNoDetail800.getOPayAbleAmount())).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            detail800.setPayAbleAmount(payAbleAmount.toString());
            BigDecimal handlingCharge = distNoDetailMem800List.stream().
                    map(distNoDetail800 -> distNoDetail800.getOTaxAmount() == null ? BigDecimal.ZERO : new BigDecimal(distNoDetail800.getOTaxAmount())).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            detail800.setHandlingCharge(handlingCharge.toString());
            detail800.setDistNoLists(distNoDetailMem800List);
            distNoDetailMem800TotalList.addAll(distNoDetailMem800List);
        });
        basesMem800.setDetailList(detailMem800List);
        dataMem800.setBases(basesMem800);
        //data800其他数据
        dataMem800.setDate(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm"));
        dataMem800.setReportId("ERDIS800");
        dataMem800.setAutoPayNo(autopayNo);
        dataMem800.setCount(String.valueOf(detailMem800List.size()));
        //计算各金额总和
        BigDecimal paymentTotal = distNoDetailMem800TotalList.stream().collect(Collectors.reducing(BigDecimal.ZERO, DistNoDetail800::getPayA, BigDecimal::add));
        BigDecimal taxAmount = distNoDetailMem800TotalList.stream().collect(Collectors.reducing(BigDecimal.ZERO, DistNoDetail800::getTaxA, BigDecimal::add));
        BigDecimal commissionAmount = distNoDetailMem800TotalList.stream().collect(Collectors.reducing(BigDecimal.ZERO, DistNoDetail800::getCommissionA, BigDecimal::add));
        BigDecimal reciprocalAmount = distNoDetailMem800TotalList.stream().collect(Collectors.reducing(BigDecimal.ZERO, DistNoDetail800::getReciprocalA, BigDecimal::add));
        dataMem800.setPaymentTotal(paymentTotal.toString());
        dataMem800.setTaxAmount(taxAmount.toString());
        dataMem800.setHandlingCharges(taxAmount.toString());//FIXME HandlingChargesruhe 源数据来源
        dataMem800.setCommissionAmount(commissionAmount.toString());
        dataMem800.setReciprocalAmount(reciprocalAmount.toString());
        dataMem800.setTotal(paymentTotal.add(taxAmount).add(commissionAmount).add(reciprocalAmount).toString());
        return dataMem800;
    }

    private Bases750 getBases750(List<DistAutoPay750Vo> distAutoPay750VoList,boolean showDistNo) {
        // 使用 LinkedHashMap 保持插入顺序
        Map<DistAutoPay750Vo, List<DistAutoPay750Vo>> distAutoPay750VoListMap = distAutoPay750VoList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                    distAutoPay750Vo -> distAutoPay750Vo,
                    LinkedHashMap::new,
                    Collectors.toList()
                ));

        Bases750 bases750 = new Bases750();
        List<Detail750> detail750List = new ArrayList<>();
        distAutoPay750VoListMap.forEach((k, v) -> {
            Detail750 detail750 = new Detail750();
            List<AccountDetail750> accountDetail750List = new ArrayList<>();
            BeanUtils.copyProperties(k, detail750);
            v.stream().forEach(distAutoPay750Vo -> {
                AccountDetail750 accountDetail750 = new AccountDetail750();
                BeanUtils.copyProperties(distAutoPay750Vo, accountDetail750);
                if(!showDistNo) {
                    // 将 distNo 属性设置为空字符串
                    accountDetail750.setDistNo("");
                }
                accountDetail750List.add(accountDetail750);
            });
            detail750.setDetailList(accountDetail750List);
            detail750List.add(detail750);
        });

        Set<String> seenKeys = new HashSet<>();

        for (Detail750 item : detail750List) {
            String paNameNo = item.getPaNameNo();
            String paName = item.getPaName();

            // 构造唯一 key，比如用 paNameNo + "_" + paName
            String key = paNameNo + "_" + paName;

            if (seenKeys.contains(key)) {
                // 如果已经存在，设置为空字符串
                item.setPaNameNo("");
                item.setPaName("");
            } else {
                // 第一次出现，记录下来
                seenKeys.add(key);
            }
        }

        bases750.setDetailList(detail750List);

        if(!showDistNo) {
            bases750.setSummaryTotal(null);
            return bases750;
        }
        List<SummaryTotal750> summaryTotal750List = new ArrayList<>();
        Map<String, BigDecimal> distAmount = distAutoPay750VoList.stream().filter(Objects::nonNull).
                collect(Collectors.groupingBy(DistAutoPay750Vo::getDistNo, Collectors.reducing(BigDecimal.ZERO, DistAutoPay750Vo::getBalanceA, BigDecimal::add)));
        distAmount.forEach((s, a) -> summaryTotal750List.add(new SummaryTotal750(s, a)));
        //summaryTotal750List.forEach(summaryTotal750 -> summaryTotal750.setDistNo(""));
        bases750.setSummaryTotal(summaryTotal750List);
        return bases750;
    }

    private Bases750 getBases880(List<DistAutoPay750Vo> distAutoPay750VoList) {
        // 使用 LinkedHashMap 保持插入顺序
        Map<DistAutoPay750Vo, List<DistAutoPay750Vo>> distAutoPay750VoListMap = distAutoPay750VoList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                    distAutoPay750Vo -> distAutoPay750Vo,
                    LinkedHashMap::new,
                    Collectors.toList()
                ));

        Bases750 bases750 = new Bases750();
        List<Detail750> detail750List = new ArrayList<>();
        distAutoPay750VoListMap.forEach((k, v) -> {
            Detail750 detail750 = new Detail750();
            List<AccountDetail750> accountDetail750List = new ArrayList<>();
            BeanUtils.copyProperties(k, detail750);
            v.stream().forEach(distAutoPay750Vo -> {
                AccountDetail750 accountDetail750 = new AccountDetail750();
                BeanUtils.copyProperties(distAutoPay750Vo, accountDetail750);
                accountDetail750List.add(accountDetail750);
            });
            detail750.setDetailList(accountDetail750List);
            detail750List.add(detail750);
        });
        bases750.setDetailList(detail750List);
        List<SummaryTotal750> summaryTotal750List = new ArrayList<>();
        Map<String, BigDecimal> distAmount = distAutoPay750VoList.stream().filter(Objects::nonNull).
                collect(Collectors.groupingBy(DistAutoPay750Vo::getDistNo, Collectors.reducing(BigDecimal.ZERO, DistAutoPay750Vo::getBalanceA, BigDecimal::add)));
        distAmount.forEach((s, a) -> summaryTotal750List.add(new SummaryTotal750(s, a)));
        bases750.setSummaryTotal(summaryTotal750List);
        return bases750;
    }

    @Override
    public void reportDistAutoPayMemCheck730(HttpServletResponse httpServletResponse, String autopayNo, List<String> paNameNoList, String distNo, Date payDate) throws FileNotFoundException, JRException {
        String JRXML_PATH = DistAutopayNetPayMemberServiceImpl.class.getResource("/ireport").getPath();
        //list – 支票（C）
        List<DistDistributionAccount> distDistributionAccountList = distAutoNetPayExportService.exportDistribution(autopayNo, Arrays.asList(new String[]{"C"}), paNameNoList, distNo, payDate);
        //此处是测试蒋两个pdf合并在一起，如果只数据一个pdf的，则传入一个JasperParam即可
        List<JasperParam> jaspers = new ArrayList<>();
        distDistributionAccountList.forEach(distDistributionAccount -> {
            Map<String,Object> param = new HashMap<>();
            param.put("date", distDistributionAccount.getDate());
            param.put("autoPayNo", distDistributionAccount.getAutoPayNo());
            param.put("totalCommission", LocalCommonMethodUtils.formate(distDistributionAccount.getTotalCommission()));
            param.put("totalTax", LocalCommonMethodUtils.formate(distDistributionAccount.getTotalTax()));
            param.put("distributionPayment", LocalCommonMethodUtils.formate(distDistributionAccount.getDistributionPayment()));
            param.put("netPayment", LocalCommonMethodUtils.formate(distDistributionAccount.getNetPayment()));
            param.put("totalRoyalties", LocalCommonMethodUtils.formate(distDistributionAccount.getTotalRoyalties()));
            param.put("summaryTotal", "0");

            param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
            param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");

            jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(distDistributionAccount.getBases()).getBytes()));
        });
        //调用生成pdf方法
        PdfDownloadUtil.download(jaspers, "/ireport/jrxml/distAutoPays/DistAutoPayZP730_main.jrxml", httpServletResponse, autopayNo);

    }

    @Override
    public void reportDistAutoPayMemCheck730(DistAutopayNetPayMember distAutopayNetPayMember) {
        String autopayNo = distAutopayNetPayMember.getAutopayNo();
        if(StringUtils.isEmpty(autopayNo)){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),
                    String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "支付編號"));
        }

        String paNameNo = distAutopayNetPayMember.getPaNameNo();
        List<String> paNameNoList = new ArrayList<>();
        if(StringUtils.isNotEmpty(paNameNo)){
            paNameNoList = Arrays.asList(paNameNo.split(","));
        }

        String JRXML_PATH = DistAutopayNetPayMemberServiceImpl.class.getResource("/ireport").getPath();

        List<String> finalPaNameNoList = paNameNoList;
        CompletableFuture.runAsync(() -> {
            //list – 支票（C）
            Map<String,DistDistributionAccount> distDistributionAccountMap = distAutoNetPayExportService.exportDistributionGroup(autopayNo, Arrays.asList(new String[]{"C"}), finalPaNameNoList, distAutopayNetPayMember.getDistNo(),distAutopayNetPayMember.getPayDate());
            //此处是测试蒋两个pdf合并在一起，如果只数据一个pdf的，则传入一个JasperParam即可
            distDistributionAccountMap.forEach((k,v) -> {
                List<JasperParam> jaspers = new ArrayList<>();
                Map<String,Object> param = new HashMap<>();
                param.put("date", v.getDate());
                param.put("autoPayNo", v.getAutoPayNo());
                param.put("totalCommission", LocalCommonMethodUtils.formate(v.getTotalCommission()));
                param.put("totalTax", LocalCommonMethodUtils.formate(v.getTotalTax()));
                param.put("distributionPayment", LocalCommonMethodUtils.formate(v.getDistributionPayment()));
                param.put("netPayment", LocalCommonMethodUtils.formate(v.getNetPayment()));
                param.put("totalRoyalties", LocalCommonMethodUtils.formate(v.getTotalRoyalties()));
                param.put("summaryTotal", "0");

                param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
                param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");

                jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(v.getBases()).getBytes()));
//            String outFile = String.format("%s/FSRIS730/%s/使用報酬分配明細表_%s.pdf",EXPORT_FILE_PATH, k,k,k);
                String outFile = String.format("F:\\Test\\PAY_PDF\\FSRIS730\\%s\\使用報酬分配明細表_%s.pdf", k,k,k);
                //调用生成pdf方法
                try{
                    PdfReport.exprtPdf(outFile, jaspers, "/ireport/jrxml/distAutoPays/DistAutoPayZP730_main.jrxml");
                } catch (Exception e){
                    logger.error("使用報酬分配明細表生成失败：paNameNo:{}",k);
                }
            });
        });

    }

    @Override
    public void reportDistAutoPayMem730(HttpServletResponse httpServletResponse, String autopayNo, String autopayDescription, String paNameNo, String distNo) throws FileNotFoundException, JRException {
        String JRXML_PATH = DistAutopayNetPayMemberServiceImpl.class.getResource("/ireport").getPath();
        //list –  汇款 （T、A）
        List<DistDistributionAccount> distDistributionAccountList = distAutoNetPayExportService.exportDistribution(autopayNo, Arrays.asList(new String[]{"T", "A"}), paNameNo, distNo);
        //此处是测试蒋两个pdf合并在一起，如果只数据一个pdf的，则传入一个JasperParam即可
        List<JasperParam> jaspers = new ArrayList<>();
        distDistributionAccountList.forEach(distDistributionAccount -> {
            Map<String,Object> param = new HashMap<>();
            param.put("date", distDistributionAccount.getDate());
            param.put("autoPayNo", distDistributionAccount.getAutoPayNo());
            param.put("totalCommission", LocalCommonMethodUtils.formate(distDistributionAccount.getTotalCommission()));
            param.put("totalTax", LocalCommonMethodUtils.formate(distDistributionAccount.getTotalTax()));
            param.put("distributionPayment", LocalCommonMethodUtils.formate(distDistributionAccount.getDistributionPayment()));
            param.put("handingCharge", LocalCommonMethodUtils.formate(distDistributionAccount.getHandingCharge()));
            param.put("netPayment", LocalCommonMethodUtils.formate(distDistributionAccount.getNetPayment()));

            param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
            param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");

            jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(distDistributionAccount.getBases()).getBytes()));
        });
        //调用生成pdf方法
        PdfDownloadUtil.download(jaspers, "/ireport/jrxml/distAutoPays/DistAutoPay730_main.jrxml", httpServletResponse, autopayNo);

    }


    @Override
    public void reportDistAutoPay780(HttpServletResponse httpServletResponse, String autopayNo, Boolean bankInfo, Integer societyCode, String distNo) throws FileNotFoundException, JRException {
        String JRXML_PATH = DistAutopayNetPayMemberServiceImpl.class.getResource("/ireport").getPath();

        // 获取海外收据数据
        List<DistAutoPayOverseasReceipt> receipts = distAutoNetPayExportService.exportOverseasReceipt(autopayNo, bankInfo, societyCode, distNo);
        if (CollectionUtils.isEmpty(receipts)) {
            throw new MustException("無可導出數據!");
        }

        List<JasperParam> jaspers = new ArrayList<>();
        receipts.forEach(receipt -> {
            Map<String, Object> param = new HashMap<>();

            // 设置基本参数
            param.put("date", receipt.getDate());
            param.put("societyName", receipt.getSocietyName());
            param.put("societyCode", receipt.getSocietyCode() != null ? receipt.getSocietyCode().toString() : "");
            param.put("payCurrency", receipt.getPayCurrency());
            param.put("paymentMethod", receipt.getPaymentMethod());

            // 使用货币格式化方法设置货币参数
            String currencySymbol = "$"; // 可以根据需要调整货币符号
            param.put("feeInError", formatCurrency(receipt.getFeeInError(), currencySymbol));
            param.put("adjTotalAmount", formatCurrency(receipt.getAdjTotalAmount(), currencySymbol));
            param.put("adjSendAmount", formatCurrency(receipt.getAdjSendAmount(), currencySymbol));
            param.put("affiliated", formatCurrency(receipt.getAffiliated(), currencySymbol));
            param.put("bankCharge", formatCurrency(receipt.getBankCharge(), currencySymbol));
            param.put("netPayment", formatCurrency(receipt.getNetPayment(), currencySymbol));
            param.put("exchangeRate", formatCurrency(receipt.getExchangeRate(), currencySymbol));
            param.put("totalPayment", formatCurrency(receipt.getTotalPayment(), currencySymbol));

            param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
            param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");

            // 创建格式化后的 bases 数据
            Map<String, Object> formattedBases = createFormattedDistAutoPayData(receipt.getBases(), currencySymbol);

            jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(formattedBases).getBytes()));
        });

        // 调用生成pdf方法
        PdfDownloadUtil.download(jaspers, "/ireport/jrxml/distAutoPays/DistAutoPay780_main.jrxml", httpServletResponse, autopayNo);
    }

    /**
     * 格式化货币值
     * @param amount 金额
     * @param currencySymbol 货币符号
     * @return 格式化后的货币字符串
     */
    private String formatCurrency(BigDecimal amount, String currencySymbol) {
        if (amount == null) {
            return currencySymbol + "0.00";
        }

        DecimalFormat df = new DecimalFormat("#,##0.00");
        boolean isNegative = amount.compareTo(BigDecimal.ZERO) < 0;
        String formattedAmount = df.format(amount.abs());

        return isNegative ? "-" + currencySymbol + formattedAmount : currencySymbol + formattedAmount;
    }

    /**
     * 创建格式化后的 DistAutoPay 对象副本，用于 JasperReports
     * @param original 原始 DistAutoPay 对象
     * @param currencySymbol 货币符号
     * @return 包含格式化金额的 Map 对象
     */
    private Map<String, Object> createFormattedDistAutoPayData(DistAutoPay original, String currencySymbol) {
        Map<String, Object> formattedData = new HashMap<>();

        if (original.getDistAutoPayReceipts() != null) {
            List<Map<String, Object>> formattedReceipts = new ArrayList<>();
            for (DistAutoPayReceipt receipt : original.getDistAutoPayReceipts()) {
                Map<String, Object> formattedReceipt = new HashMap<>();
                formattedReceipt.put("royaltiesType", receipt.getRoyaltiesType());

                if (receipt.getDistAutoPayReceiptDetail() != null) {
                    List<Map<String, Object>> formattedDetails = new ArrayList<>();
                    for (DistAutoPayReceiptDetail detail : receipt.getDistAutoPayReceiptDetail()) {
                        Map<String, Object> formattedDetail = new HashMap<>();
                        formattedDetail.put("distNo", detail.getDistNo());
                        formattedDetail.put("royaltyAmount", formatCurrency(detail.getRoyaltyAmount(), currencySymbol));
                        formattedDetail.put("royalDescribe", detail.getRoyalDescribe());

                        // 格式化 DistAutoPayDetail 中的金额字段
                        if (detail.getDistAutoPayDetail() != null) {
                            formattedDetail.put("distAutoPayDetail", formatDistAutoPayDetail(detail.getDistAutoPayDetail(), currencySymbol));
                        }

                        formattedDetails.add(formattedDetail);
                    }
                    formattedReceipt.put("distAutoPayReceiptDetail", formattedDetails);
                }

                formattedReceipts.add(formattedReceipt);
            }
            formattedData.put("distAutoPayReceipts", formattedReceipts);
        }

        // 处理其他包含金额的字段
        if (original.getPrsAffiliated() != null) {
            formattedData.put("prsAffiliated", original.getPrsAffiliated());
        }

        // 格式化 SummaryTotal 中的金额字段
        if (original.getSummaryTotal() != null) {
            List<Map<String, Object>> formattedSummaryTotal = new ArrayList<>();
            for (SummaryTotal summaryTotal : original.getSummaryTotal()) {
                Map<String, Object> formattedSummary = new HashMap<>();
                formattedSummary.put("distNo", summaryTotal.getDistNo());
                formattedSummary.put("totalAmount", formatCurrency(summaryTotal.getTotalAmount(), currencySymbol));
                formattedSummaryTotal.add(formattedSummary);
            }
            formattedData.put("summaryTotal", formattedSummaryTotal);
        }

        // 格式化 DistPayDetails 中的金额字段
        if (original.getDetails() != null) {
            List<Map<String, Object>> formattedDetails = new ArrayList<>();
            for (DistPayDetails detail : original.getDetails()) {
                Map<String, Object> formattedDetail = new HashMap<>();
                formattedDetail.put("paNameNo", detail.getPaNameNo());
                formattedDetail.put("paName", detail.getPaName());
                formattedDetail.put("bankName", detail.getBankName());
                formattedDetail.put("bankCode", detail.getBankCode());
                formattedDetail.put("branchNo", detail.getBranchNo());
                formattedDetail.put("accountNumber", detail.getAccountNumber());
                formattedDetail.put("payName", detail.getPayName());
                formattedDetail.put("payMethod", detail.getPayMethod());
                formattedDetail.put("payDescribe", detail.getPayDescribe());

                // 格式化 DistNoDetail 中的金额字段
                if (detail.getDetailList() != null) {
                    List<Map<String, Object>> formattedDistNoDetails = new ArrayList<>();
                    for (DistNoDetail distNoDetail : detail.getDetailList()) {
                        Map<String, Object> formattedDistNoDetail = new HashMap<>();
                        formattedDistNoDetail.put("distNo", distNoDetail.getDistNo());
                        formattedDistNoDetail.put("balance", formatCurrency(distNoDetail.getBalance(), currencySymbol));
                        formattedDistNoDetail.put("bankAccountName", distNoDetail.getBankAccountName());
                        formattedDistNoDetails.add(formattedDistNoDetail);
                    }
                    formattedDetail.put("detailList", formattedDistNoDetails);
                }

                formattedDetails.add(formattedDetail);
            }
            formattedData.put("details", formattedDetails);
        }

        // 其他非金额字段直接复制
        if (original.getDetailList() != null) {
            formattedData.put("detailList", original.getDetailList());
        }
        if (original.getBaseNoLists() != null) {
            formattedData.put("baseNoLists", original.getBaseNoLists());
        }

        return formattedData;
    }

    /**
     * 格式化 DistAutoPayDetail 中的所有金额字段
     * @param detail 原始 DistAutoPayDetail 对象
     * @param currencySymbol 货币符号
     * @return 包含格式化金额的 Map 对象
     */
    private Map<String, Object> formatDistAutoPayDetail(DistAutoPayDetail detail, String currencySymbol) {
        Map<String, Object> formattedDetail = new HashMap<>();

        // 格式化所有金额字段
        formattedDetail.put("year", detail.getYear());
        formattedDetail.put("performRoyalties", formatCurrency(detail.getPerformRoyalties(), currencySymbol));
        formattedDetail.put("allocation", formatCurrency(detail.getAllocation(), currencySymbol));
        formattedDetail.put("royaltiesAdj", formatCurrency(detail.getRoyaltiesAdj(), currencySymbol));
        formattedDetail.put("royaltiesAdj2", formatCurrency(detail.getRoyaltiesAdj2(), currencySymbol));
        formattedDetail.put("memberRetain", formatCurrency(detail.getMemberRetain(), currencySymbol));
        formattedDetail.put("commissionRate", formatCurrency(detail.getCommissionRate(), currencySymbol));
        formattedDetail.put("commissionAmount", formatCurrency(detail.getCommissionAmount(), currencySymbol));
        formattedDetail.put("taxableRate", formatCurrency(detail.getTaxableRate(), currencySymbol));
        formattedDetail.put("taxableAmount", formatCurrency(detail.getTaxableAmount(), currencySymbol));
        formattedDetail.put("reciprocalRate", formatCurrency(detail.getReciprocalRate(), currencySymbol));
        formattedDetail.put("reciprocalAmount", formatCurrency(detail.getReciprocalAmount(), currencySymbol));
        formattedDetail.put("royaltyAmount", formatCurrency(detail.getRoyaltyAmount(), currencySymbol));
        formattedDetail.put("salesTaxRate", formatCurrency(detail.getSalesTaxRate(), currencySymbol));
        formattedDetail.put("salesTaxAmount", formatCurrency(detail.getSalesTaxAmount(), currencySymbol));
        formattedDetail.put("additionalCommissionRate", formatCurrency(detail.getAdditionalCommissionRate(), currencySymbol));
        formattedDetail.put("additionalCommissionAmount", formatCurrency(detail.getAdditionalCommissionAmount(), currencySymbol));
        formattedDetail.put("deduction", formatCurrency(detail.getDeduction(), currencySymbol));

        // 复制非金额字段
        formattedDetail.put("firstDescribe", detail.getFirstDescribe());
        formattedDetail.put("distributionDescribe", detail.getDistributionDescribe());
        formattedDetail.put("memberDescribe", detail.getMemberDescribe());
        formattedDetail.put("secondDescribe", detail.getSecondDescribe());
        formattedDetail.put("thirdDescribe", detail.getThirdDescribe());
        formattedDetail.put("royalDescribe", detail.getRoyalDescribe());

        return formattedDetail;
    }

    @Override
    public void exportSaleTaxReport(HttpServletResponse httpServletResponse, List<String> paNameNoList) throws JRException, FileNotFoundException {
        String JRXML_PATH = DistAutopayNetPayMemberServiceImpl.class.getResource("/ireport").getPath();
        //list –  汇款 （T、A）
        List<SalesTaxReport> salesTaxReports = distAutoNetPayExportService.exportSaleTaxReport(paNameNoList);
        //此处是测试蒋两个pdf合并在一起，如果只数据一个pdf的，则传入一个JasperParam即可
        List<JasperParam> jaspers = new ArrayList<>();
        salesTaxReports.forEach(salesTaxReport -> {
            Map<String,Object> param = new HashMap<>();
            param.put("memberName", salesTaxReport.getMemberName());
            param.put("ipBaseNo", salesTaxReport.getIpBaseNo());
            param.put("paNameNo", salesTaxReport.getPaNameNo());
            param.put("distNo", salesTaxReport.getDistNo());
            param.put("note", salesTaxReport.getDistNo());
            param.put("payCurrency",salesTaxReport.getPayCurrency());
            param.put("totalRoyalties", LocalCommonMethodUtils.formate(salesTaxReport.getTotalRoyalties()));
            param.put("totalSale", LocalCommonMethodUtils.formate(salesTaxReport.getTotalSale()));
            param.put("totalSaleRate", LocalCommonMethodUtils.formate(salesTaxReport.getTotalSale()));
            param.put("netPayment", LocalCommonMethodUtils.formate(salesTaxReport.getNetPayment()));
            param.put("totalAmount", LocalCommonMethodUtils.formate(salesTaxReport.getTotalAmount()));
            param.put("date", salesTaxReport.getDate());
            param.put("brNo",salesTaxReport.getBrNo());
            param.put("royaltiesType","royaltiesType");
            param.put("distAutoPayReceiptDetail",salesTaxReport.getDistAutoPayReceiptDetail());

            param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
            param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");

            jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(salesTaxReport.getBases()).getBytes()));
        });
        //调用生成pdf方法
        PdfDownloadUtil.download(jaspers, "/ireport/jrxml/distAutoPays/DistAutoPay700_main.jrxml", httpServletResponse, "700");
    }

    @Override
    public void exportTxtReport(HttpServletResponse httpServletResponse, String autopayNo) {

        List<String> list = distAutoNetPayExportService.txtReport(autopayNo) ;
        /*String fileOutPath = "F:\\Test\\export\\autopayNo.txt";
        FileWriter fileWriter = new FileWriter(fileOutPath,true);*/
        httpServletResponse.setCharacterEncoding("Big5");
        httpServletResponse.setContentType("text/plain");
        httpServletResponse.addHeader(
                "Content-Disposition",
                "attachment; filename=" + autopayNo + ".txt");//通过后缀可以下载不同的文件格式

        BufferedOutputStream buff = null;
        ServletOutputStream outStr = null;

        try {
           /* for(String s : list){
                fileWriter.write(s+"\r\n");
                fileWriter.flush();
            }*/

            outStr = httpServletResponse.getOutputStream();
            buff = new BufferedOutputStream(outStr);
            for(String str : list){
                buff.write(str.getBytes("Big5"));
                buff.flush();
            }
            buff.close();
        } catch (Exception e) {
            logger.error("导出文件文件出错，e:{}", e);
        }finally {
//            fileWriter.close();

            try {
                buff.close();
                outStr.close();
            } catch (Exception e) {
                logger.error("关闭流对象出错 e:{}", e);
            }
        }

    }

    @Override
    public void exportFSRIS100Report(HttpServletResponse response, Fsris100Request fsris100Request) {

        List<DistAutopayNetPayMember> netPayMembers = selectNetPayMemberForFsris100(fsris100Request.getAutopayNo(),fsris100Request.getPaNameNos(),fsris100Request.getDistNos(),fsris100Request.getStart(),fsris100Request.getEnd());
        Long memberCount = netPayMembers.stream().map(DistAutopayNetPayMember :: getIpBaseNo).distinct().count();
        Integer totalCount = netPayMembers.size();

        GenerateExcelUtil<Map<String,Object>> generateExcelUtil = null;
        try{
            String filename = "FSRIS100-支付統計報表(MEMBER)" + System.currentTimeMillis() + ".xlsx" ;

           List<Map<String,Object>> exports = new ArrayList<>() ;

           netPayMembers.forEach(n ->  {
               Map<String,Object> map = MapUtil.objectToMap(n) ;
               setPaymentMethod(map,n.getPaymentMethod()) ;
           });

//            exports.addAll(netPayMembers);

            Map<String,Object> map1 = new HashMap<>();
            exports.add(map1) ;
            exports.add(map1) ;

            Map<String,Object> map2 = new HashMap<>() ;
            map2.put(PA_NAME_NO,"MEMBER COUNT") ;
            map2.put(MUST_CURRENT_SUCCESSOR_TO_WITHDRAWAL_PUBLISHER,memberCount) ;
            exports.add(map2) ;

            Map<String,Object> map3 = new HashMap<>() ;
            map3.put(PA_NAME_NO,"TOTAL SCORE") ;
            map3.put(MUST_CURRENT_SUCCESSOR_TO_WITHDRAWAL_PUBLISHER,totalCount) ;
            exports.add(map3) ;

            List<String> titleList = new ArrayList<>();
            titleList.addAll(titles1);
            titleList.addAll(titles2);
            titleList.addAll(titles3);

            List<String> titleKey = new ArrayList<>() ;
            titleKey.addAll(values1);
            titleKey.addAll(values2);
            titleKey.addAll(values3);

            response.addHeader("Content-Disposition",
                    "attachment;filename=" + filename);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setCharacterEncoding("utf-8");

            generateExcelUtil = new GenerateExcelUtil<>(titleList.toArray(new String[titleList.size()]),titleKey.toArray(new String[titleKey.size()]), false);

//            Sheet sheet = generateExcelUtil.getWorkbook().getSheet("");

            exports.addAll(groupByDistNo(netPayMembers)) ;
            generateExcelUtil.add("Payment MEMBER", exports);
            generateExcelUtil.getWorkbook().write(response.getOutputStream());
        }catch (Exception e){
            logger.error("error",e);
        }

    }

    @Override
    public Integer delete(String autopayNo) {
        Example example = new Example(DistAutopayNetPayMember.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("autopayNo", autopayNo);
        List<DistAutopayNetPayMember> distAutopayNetPayMembers = distAutopayNetPayMemberMapper.selectByExample(example);
        for(DistAutopayNetPayMember d : distAutopayNetPayMembers){
            redo(d.getId());
        }

        return distAutopayNetPayMembers.size();
    }

    /**
     * 如果字符串对象为 null，则返回空字符串，否则返回去掉字符串前后空格的字符串
     *
     * @param str
     * @return
     */
    public static String delNull (String str) {
        String returnStr = "";
        if (!StringUtils.isEmpty(str)) {
            returnStr = str.trim();
        }
        return returnStr;
    }

    public List<Map<String,Object>> groupByDistNo(List<DistAutopayNetPayMember> netPayMembers){
        List<Map<String,Object>> list = new ArrayList<>() ;
        Map<String,Object>  map1 = new HashMap<>() ;
        list.add(map1) ;
        Map<String,Object>  map2 = this.calcAmount(netPayMembers); ;
        map2.put(PA_NAME_NO,"TOTAL ROYALTIES") ;
        list.add(map2);
        list.add(map1);
        Map<String,Object>  map3 = initMap2() ;
        map3.put(IP_TYPE,"人数");
        list.add(map3);
        Map<String,Object>  map4 = new HashMap<>() ;
        map4.put(PA_NAME_NO,"Dist No") ;
        list.add(map4);
        Map<String,List<DistAutopayNetPayMember>> distGroupMap = netPayMembers.stream().collect(Collectors.groupingBy(DistAutopayNetPayMember::getDistNo)) ;
        for(Map.Entry<String,List<DistAutopayNetPayMember>> entry : distGroupMap.entrySet()){
            String distNo = entry.getKey();
            List<DistAutopayNetPayMember> groupNetPayMembers = entry.getValue();
            Long memberCount = groupNetPayMembers.stream().map(DistAutopayNetPayMember :: getIpBaseNo).distinct().count();
            Map<String,Object> map = this.calcAmount(groupNetPayMembers);
            map.put(IP_TYPE,memberCount);
            map.put(MUST_CURRENT_SUCCESSOR_NAME,distNo);
            list.add(map);
        }

        list.add(map1);
        Map<String,Object>  map5 = new HashMap<>() ;
        map5.put(PA_NAME_NO,"Payment Method") ;
        list.add(map5);
        List<String> paymentMethods = Arrays.asList("A","C","T","D","O","X");
        Map<String,List<DistAutopayNetPayMember>> payMethodGroupMap = netPayMembers.stream().filter(p -> StringUtils.isNotEmpty(p.getPaymentMethod())).collect(Collectors.groupingBy(DistAutopayNetPayMember::getPaymentMethod)) ;
        int i = 0 ;
        for(String paymentMethod : paymentMethods){
            List<DistAutopayNetPayMember> groupNetPayMembers = payMethodGroupMap.get(paymentMethod);
            if(groupNetPayMembers == null){
                Map<String,Object> map = initMap();
                map.put(IP_TYPE,0);
                map.put(MUST_CURRENT_SUCCESSOR_NAME,paymentMethodC[i]);
                map.put(MUST_CURRENT_SUCCESSOR_TO_WITHDRAWAL_PUBLISHER,paymentMethodE[i]);
                list.add(map);
            } else {
                Long memberCount = groupNetPayMembers.stream().map(DistAutopayNetPayMember :: getIpBaseNo).distinct().count();
                Map<String,Object> map = this.calcAmount(groupNetPayMembers);
                map.put(IP_TYPE,memberCount);
                map.put(MUST_CURRENT_SUCCESSOR_NAME,paymentMethodC[i]);
                map.put(MUST_CURRENT_SUCCESSOR_TO_WITHDRAWAL_PUBLISHER,paymentMethodE[i]);
                list.add(map);
            }
            i ++ ;
        }

        list.add(map1);
        Map<String,Object>  map6 = new HashMap<>() ;
        map6.put(PA_NAME_NO,"Payment No") ;
        list.add(map6);
        Map<String,List<DistAutopayNetPayMember>> paymentNoGroupMap = netPayMembers.stream().filter(p -> StringUtils.isNotEmpty(p.getPaymentMethod())).collect(Collectors.groupingBy(DistAutopayNetPayMember::getAutopayNo)) ;
        for(Map.Entry<String,List<DistAutopayNetPayMember>> entry : paymentNoGroupMap.entrySet()){
            String paymentNo = entry.getKey();
            List<DistAutopayNetPayMember> groupNetPayMembers = entry.getValue();
            Long memberCount = groupNetPayMembers.stream().map(DistAutopayNetPayMember :: getIpBaseNo).distinct().count();
            Map<String,Object> map = this.calcAmount(groupNetPayMembers);
            map.put(IP_TYPE,memberCount);
            map.put(MUST_CURRENT_SUCCESSOR_NAME,paymentNo);
            list.add(map);
        }

        return list;
    }

    public Map<String,Object> distAutopayNetPayMemberToMap(DistAutopayNetPayMember distAutopayNetPayMember){

        Map<String,Object> map = new HashMap<>();
        map.put("distNo",distAutopayNetPayMember.getDistNo());
        map.put("autopayNo",distAutopayNetPayMember.getAutopayNo());
        map.put("payDate",distAutopayNetPayMember.getPayDate());
        map.put("paName",distAutopayNetPayMember.getPaName());
        map.put("ipBaseNo",distAutopayNetPayMember.getIpBaseNo());
        map.put("paNameNo",distAutopayNetPayMember.getPaNameNo());
        map.put("distNo",distAutopayNetPayMember.getDistNo());
        map.put("distNo",distAutopayNetPayMember.getDistNo());
        map.put("distNo",distAutopayNetPayMember.getDistNo());

        return map;

    }

    public Map<String,Object> initMap(){
        Map<String,Object> map = new HashMap<>() ;
        for(String key : this.values2){
            map.put(key,0);
        }
        return map;
    }

    public Map<String,Object> initMap2(){
        Map<String,Object> map = new HashMap<>() ;
        for(int i =0 ; i < values2.size(); i++){
            map.put(values2.get(i), titles2.get(i));
        }
        return map;
    }

    public Map<String,Object> calcAmount(List<DistAutopayNetPayMember> distAutopayNetPayMembers){
        Map<String,Object> distMap = initMap();

        BigDecimal roy_sub_total_sum = BigDecimal.ZERO,adj_royalty_amount_sum = BigDecimal.ZERO,taxable_amount_sum = BigDecimal.ZERO;
        BigDecimal commission_amount_sum = BigDecimal.ZERO,adj_commission_amount_sum = BigDecimal.ZERO,pay_amount_sum = BigDecimal.ZERO;
        for(DistAutopayNetPayMember npm : distAutopayNetPayMembers){
            roy_sub_total_sum = roy_sub_total_sum.add(npm.getRoyaltyAmount());
            adj_royalty_amount_sum = adj_royalty_amount_sum.add(npm.getAdjRoyaltyAmount());
            taxable_amount_sum = taxable_amount_sum.add(npm.getTaxableAmount());
            commission_amount_sum = commission_amount_sum.add(npm.getCommissionAmount());
            adj_commission_amount_sum = adj_commission_amount_sum.add(npm.getAdjCommissionAmount()) ;
            pay_amount_sum = pay_amount_sum.add(npm.getPayAmount());
        }

        distMap.put(ROY_SUB_TOTAL,roy_sub_total_sum);
        distMap.put(ADJ_SUB_TOTAL,adj_royalty_amount_sum);
        distMap.put(TRANSACTION_TAXABLE_INCOME,taxable_amount_sum) ;
        distMap.put(TOTAL_COMMISSION_AMT,commission_amount_sum);
        distMap.put(ADDITIONAL_COMMISSION_AMT,adj_commission_amount_sum) ;
        distMap.put(NET_PAYMENT,pay_amount_sum);
        return distMap;

    }

    public void setPaymentMethod(Map<String,Object> map, String payment){
        if(StringUtils.isNotEmpty(payment)){
            int i = -1 ;
            switch (payment){
                case "A":
                    i = 0;
                    break;
                case "C":
                    i = 1 ;
                    break;
                case "T":
                    i = 2;
                    break;
                case "D":
                    i = 3 ;
                    break;
                case "O":
                    i= 4;
                    break;
                case "X":
                    i =5 ;
                    break;
            }

            if(i > -1 ){
                map.put(MUST_CURRENT_SUCCESSOR_NAME,paymentMethodC[i]);
                map.put(MUST_CURRENT_SUCCESSOR_TO_WITHDRAWAL_PUBLISHER,paymentMethodE[i]);
            }

        }

    }


    List<String> titles1 = Arrays.asList("Dist No","Status","Type","Payment No","Payment Date","Chinese Name","Romanization Name", "Ip Base No",
            "Pa Name No","Ref Ip Base No","Must Current Successor Name","Must Current Successor to Withdrawal Publisher", "Ip Type");

    List<String> titles2 = Arrays.asList("Tv Royalties","Concert Royalties","Radio Royalties","Karaoke And Film Royalties","Airline And General Royalties","Others And Public Performance Royalties","Roy Sub Total",
            "Upa Amt","Tv Adjustment","Karaoke And Film Adjustment","Airline And General Adjustment","Radio Adjustment","Others And Public Performance Adjustment","Adj Sub Total","Royalties Adjustment Total",
            "Transaction Taxable Income","Tax Rate","Withheld Tax","Revised Withheld Tax","Basic Commission Rate","Basic Commission Amt","Additional Commission Rate","Additional Commission Amt","Total Commission Amt",
            "Revised Commission Amt","Sub Total","Sales Tax Rate","Sales Tax","Deduction","Reciprocal Rate","Reciprocal Amt","Net Payment");

    List<String> titles3 = Arrays.asList( "Payment Desc","Payable Payment Method","Payable Currency Code","Must Dist Payment Type","Account Name","Must Dist Br No",
            "Must Dist Member No","Must Current Member No","Inv No");

    List<String> values1 = Arrays.asList("distNo","Status","Type","autopayNo","payDate","paName","Romanization", "ipBaseNo",
            "refIpBaseNo","paNameNo","Must Current Successor Name","Must Current Successor to Withdrawal Publisher","ipType");


    List<String> values2 = Arrays.asList("Tv Royalties","Concert Royalties","Radio Royalties","Karaoke And Film Royalties","Airline And General Royalties","Others And Public Performance Royalties","royalty_amount",
            "Upa Amt","Tv Adjustment","Karaoke And Film Adjustment","Airline And General Adjustment","Radio Adjustment","Others And Public Performance Adjustment","adjRoyaltyAmount","Royalties Adjustment Total",
            "taxableAmount","Tax Rate","Withheld Tax","Revised Withheld Tax","Basic Commission Rate","Basic Commission Amt","Additional Commission Rate","adjCommissionAmount","commissionAmount",
            "Revised Commission Amt","Sub Total","Sales Tax Rate","Sales Tax","Deduction","Reciprocal Rate","Reciprocal Amt","payAmount");

    List<String> values3 = Arrays.asList("Payment Desc","paymentMethod","payCurrency","Must Dist Payment Type","bankAccountName","Must Dist Br No",
            "Must Dist Member No","Must Current Member No","invNo");

    String[] paymentMethodE = new String[]{"By Auto transfer","By Cheque","By Telegraph","By Draft","By Overseas Draft","By Not provided"};
    String[] paymentMethodC = new String[]{"自動轉帳","支票","電報","匯票","海外匯票","不提供"};


}