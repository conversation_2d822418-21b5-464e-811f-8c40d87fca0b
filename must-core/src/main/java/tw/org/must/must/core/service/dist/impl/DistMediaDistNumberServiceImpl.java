package tw.org.must.must.core.service.dist.impl;

import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.model.dist.DistMediaDistNumber;
import tw.org.must.must.mapper.dist.DistMediaDistNumberMapper;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.dist.DistMediaDistNumberService;

import java.util.List;

@Service
public class DistMediaDistNumberServiceImpl extends BaseServiceImpl<DistMediaDistNumber>
		implements DistMediaDistNumberService {

	private final DistMediaDistNumberMapper distMediaDistNumberMapper;

	@Autowired
	public DistMediaDistNumberServiceImpl(DistMediaDistNumberMapper distMediaDistNumberMapper) {
		super(distMediaDistNumberMapper);
		this.distMediaDistNumberMapper = distMediaDistNumberMapper;
	}

	@Override
	public List<DistMediaDistNumber> listDistMediaDistNumberWithPage(Integer pageNum, Integer pageSize) {
		PageHelper.startPage(pageNum, pageSize);
		Example example = new Example(DistMediaDistNumber.class);
//        Example.Criteria criteria = example.createCriteria();
		example.orderBy("createTime").desc();
		return distMediaDistNumberMapper.selectByExample(example);
	}

	@Override
	public DistMediaDistNumber saveDistMediaVersion(DistMediaDistNumber distMediaVersion) {
		if (StringUtils.isBlank(distMediaVersion.getDistNo())) {
			throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),
					String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "DistNo"));
		}
		distMediaVersion.init();
		if (distMediaVersion.getId() == null) {
			try {
				distMediaDistNumberMapper.insertUseGeneratedKeys(distMediaVersion);
			} catch (DuplicateKeyException e) {
				e.printStackTrace();
				throw new MustException(ResultCode.DATASOURC_REPORT);
			}
		} else {
			distMediaDistNumberMapper.updateByPrimaryKeySelective(distMediaVersion);
		}
		return distMediaVersion;
	}

	@Override
	public List<DistMediaDistNumber> getDistMediaDistNumber(String distNo) {
		Example example = new Example(DistMediaDistNumber.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		return distMediaDistNumberMapper.selectByExample(example);
	}
}