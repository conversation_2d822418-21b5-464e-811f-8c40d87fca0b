package tw.org.must.must.core.service.dist.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.core.service.calcIpShare.CalcIpShareService;
import tw.org.must.must.core.service.dist.*;
import tw.org.must.must.core.service.distdata.DistDataCalcWorkIpRoyService;
import tw.org.must.must.core.service.distdata.DistDataCalcWorkPointService;
import tw.org.must.must.core.service.distdata.DistListCopyFilterService;
import tw.org.must.must.core.service.listoverseas.ListMatchDataOverseasMappingService;
import tw.org.must.must.core.service.listoverseas.ListMatchDataOverseasService;
import tw.org.must.must.core.service.listoverseas.ListOverseasFileBaseService;
import tw.org.must.must.core.service.mbr.MbrIpNameService;
import tw.org.must.must.core.service.mbr.MbrIpService;
import tw.org.must.must.core.service.wrk.*;
import tw.org.must.must.model.dist.DistOverseasIpShare;
import tw.org.must.must.model.dist.DistParamInfo;
import tw.org.must.must.model.dist.DistParamOverseas;
import tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy;
import tw.org.must.must.model.distdata.DistDataCalcWorkPoint;
import tw.org.must.must.model.distdata.DistListCopyFilter;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseas;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseasMapping;
import tw.org.must.must.model.listoverseas.ListOverseasFileBase;
import tw.org.must.must.model.mbr.MbrIp;
import tw.org.must.must.model.mbr.MbrIpName;
import tw.org.must.must.model.mbr.vo.IpNameVO;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkIpShare;
import tw.org.must.must.model.wrk.WrkWorkRight;
import tw.org.must.must.model.wrk.WrkWorkTitle;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
public class DistParamInfoCal2ServiceImpl implements DistParamInfoCal2Service {

    private static final Logger logger = LoggerFactory.getLogger(DistParamInfoCal2ServiceImpl.class);

    @Autowired
    private DistDataCalcWorkPointService distDataCalcWorkPointService;

    @Autowired
    private DistDataCalcWorkIpRoyService distDataCalcWorkIpRoyService;

    @Autowired
    private WrkWorkService wrkWorkService;

    @Autowired
    private WrkWorkTitleService wrkWorkTitleService;

    @Autowired
    private WrkWorkTransferService wrkWorkTransferService;

    @Autowired
    private CalcIpShareService calcIpShareService;

    @Autowired
    private MbrIpNameService mbrIpNameService;

    @Autowired
    private MbrIpService mbrIpService;

    @Autowired
    private WrkWorkRightService wrkWorkRightService;

    @Autowired
    private DistListCopyFilterService distListCopyFilterService;

    @Autowired
    private DistParamOverseasService distParamOverseasService;

    @Autowired
    private ListOverseasFileBaseService listOverseasFileBaseService ;

    @Autowired
    private ListMatchDataOverseasService listMatchDataOverseasService;

    @Autowired
    private ListMatchDataOverseasMappingService listMatchDataOverseasMappingService;

    @Autowired
    private WrkWorkIpShareService wrkWorkIpShareService;

    @Autowired
    private DistOverseasIpShareService distOverseasIpShareService;

    private DistCalcRoyHandler distCalcRoyHandler;

    private static ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("distOCalcThread-pool-%d").build();

    private static ExecutorService threadPool = new ThreadPoolExecutor(10, 28,0L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(), threadFactory);

    public void initHandler(DistParamInfo distParamInfo){

        DistCalcRoyHandler distCalcRoyHandler = new DistCalcRoyHandler();

        distCalcRoyHandler.setDistParamInfo(distParamInfo);

        String distNo = distParamInfo.getDistNo();

        String year = "20" + distNo.substring(1,3);

        distCalcRoyHandler.setYear(year);

        ConcurrentHashMap<String, DistWorkHandler> workUniqueKeyMap = new ConcurrentHashMap<>();

        distCalcRoyHandler.setWorkUniqueKeyMap(workUniqueKeyMap);

       /* ConcurrentHashMap<String, MbrIpName> ipNameMap = new ConcurrentHashMap<>();

        distCalcRoyHandler.setIpNameMap(ipNameMap);*/

        ConcurrentHashMap<String, MbrIpName> paNameMap = new ConcurrentHashMap<>();

        distCalcRoyHandler.setPaNameMap(paNameMap);

        ConcurrentHashMap ipTypeMap = new ConcurrentHashMap();

        distCalcRoyHandler.setIpTypeMap(ipTypeMap);

        ConcurrentHashMap<String, IpNameVO> ipNameVOMap = new ConcurrentHashMap<>();

        distCalcRoyHandler.setIpNameVOMap(ipNameVOMap);

        this.distCalcRoyHandler = distCalcRoyHandler;
    }


    @Override
    public void calDistParamInfoO(DistParamInfo distParamInfo) {

        try{
            initHandler(distParamInfo);

            List<DistParamOverseas> distParamOverseasList = distParamOverseasService.getDistParamOverseasList(distParamInfo.getDistNo());

            // SourceDistYear = FIE 不计算
            distParamOverseasList = distParamOverseasList.stream().filter(x -> !StringUtils.equalsIgnoreCase("FIE", x.getSourceDistYear())).collect(Collectors.toList());
            if (distParamOverseasList == null || distParamOverseasList.isEmpty()) {
                logger.info("distParamOverseasList为空或无有效数据，distNo: {}", distParamInfo.getDistNo());
                return;
            }
            //categoryCode为空
            //dist_param_overseas数据如果category code为空，获取 receipt_details_id字段，到list_overseas_file_base查找receipt_details_id相等数据
            categoryCodeNull(distParamOverseasList);
            // 不是空的
            //如果dist_param_overseas.category code不为空，则取对应分配编号下的数据参与计算，
            categoryCodeNotNull(distParamOverseasList);

            this.distCalcRoyHandler = null;
            logger.info("完成执行calDistParamInfoO方法，distNo: {}", distParamInfo.getDistNo());
        } catch (Exception e) {
            XxlJobLogger.log("calDistParamInfoO执行异常: {}", e.getMessage(), e);
            logger.error("calDistParamInfoO执行异常，distNo: " + (distParamInfo != null ? distParamInfo.getDistNo() : "null"), e);
            throw new RuntimeException("calDistParamInfoO执行异常", e);
        }
    }


    private void  categoryCodeNull(List<DistParamOverseas> distParamOverseasList) {
//		1.dist_param_overseas数据如果category code为空，获取 receipt_details_id字段，到list_overseas_file_base查找receipt_details_id相等数据，

        try  {
            if (distParamOverseasList == null) {
                logger.warn("categoryCodeNull方法参数distParamOverseasList为空");
                return;
            }
            
            for (DistParamOverseas distParamOverseas : distParamOverseasList) {

                if (StringUtils.isNotBlank(distParamOverseas.getCategoryCode())) {
                    continue;
                }

                XxlJobLogger.log("开始计算DistParamOverseas,id = {}",distParamOverseas.getId());

                if(distParamOverseas.getSourceDataAmount() == null || distParamOverseas.getSourceDataAmount().compareTo(BigDecimal.ZERO) == 0){
                    continue;
                }

                if(StringUtils.isBlank(distParamOverseas.getRightType())){
                    distParamOverseas.setRightType("PER"); // 默认PER
                }
                Long receiptId = distParamOverseas.getReceiptId();
                
                if (receiptId == null) {
                    logger.warn("DistParamOverseas id={} 的receiptId为空", distParamOverseas.getId());
                    continue;
                }

                List<ListOverseasFileBase> listOverseasFileBaseList =  listOverseasFileBaseService.getListOverseasFileBaseByReceiptId(receiptId) ;

                List<Long> fileBaseIds = listOverseasFileBaseList.stream().map(ListOverseasFileBase::getId).collect(Collectors.toList());

                if(CollectionUtils.isEmpty(fileBaseIds)){
                    XxlJobLogger.log("DistParamOverseas,id = {}下无FID数据",distParamOverseas.getId());
                    continue;
                }


                Long startId = 0L;

                while(true){

                    List<ListMatchDataOverseas> listMatchDataOverseasList = listMatchDataOverseasService.getByFileBaseIdsAndId(fileBaseIds,startId);

                    if (CollectionUtils.isEmpty(listMatchDataOverseasList)) {
                        break;
                    }

                    startId  = listMatchDataOverseasList.get(listMatchDataOverseasList.size() -1).getId();

                    // 先按matchWorkUniqueKey分组
                    Map<String, List<ListMatchDataOverseas>> groupedByWorkUniqueKey = listMatchDataOverseasList.stream().filter(listMatchDataOverseas -> StringUtils.isNotBlank(listMatchDataOverseas.getMatchWorkUniqueKey()))
                            .collect(Collectors.groupingBy(ListMatchDataOverseas::getMatchWorkUniqueKey));
                    
                    // 再将这些分组分配到20个线程组中
                    int threadGroupCount = 28;
                    if(groupedByWorkUniqueKey.size() < threadGroupCount){
                        threadGroupCount = groupedByWorkUniqueKey.size();
                    }
                    List<List<ListMatchDataOverseas>> threadGroups = new ArrayList<>();
                    
                    // 初始化20个空的线程组
                    for (int i = 0; i < threadGroupCount; i++) {
                        threadGroups.add(new ArrayList<>());
                    }
                    
                    // 将workUniqueKey分组循环分配到20个线程组中
                    int groupIndex = 0;
                    for (Map.Entry<String, List<ListMatchDataOverseas>> entry : groupedByWorkUniqueKey.entrySet()) {
                        threadGroups.get(groupIndex % threadGroupCount).addAll(entry.getValue());
                        groupIndex++;
                    }

                    int threadNum = threadGroups.size();

                    XxlJobLogger.log("数据量：" + listMatchDataOverseasList.size() + " 开启线程数：" + threadNum);

                    CountDownLatch countDownLatch = new CountDownLatch(threadNum);

                    List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingUpdateList = Collections.synchronizedList(new ArrayList<>());

                    List<DistDataCalcWorkPoint> distDataCalcWorkPointList = Collections.synchronizedList(new ArrayList<>());

                    List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = Collections.synchronizedList(new ArrayList<>());

                    for (int i = 0; i < threadGroups.size(); i++) {
                        List<ListMatchDataOverseas> group = threadGroups.get(i);
                        
                        if (CollectionUtils.isEmpty(group)) {
                            countDownLatch.countDown();
                            continue;
                        }
                        
                        final int threadIndex = i;
                        threadPool.execute(() -> {
                            try {
                                XxlJobLogger.log("当前数量为：====" + countDownLatch.getCount());

                                long start = System.currentTimeMillis();

                                handleSingleData(group, listMatchDataOverseasMappingUpdateList,distDataCalcWorkPointList,distDataCalcWorkIpRoyList, distParamOverseas);

                                XxlJobLogger.log(Thread.currentThread().getName() + " 线程启动；当前线程组索引为：" + threadIndex +
                                        "；总共处理了 ： " + group.size() + " ； 所消耗的时间为 ：" + (System.currentTimeMillis() - start) + "毫秒");
                            } catch (Exception e){
                                XxlJobLogger.log("handleSingleData执行异常: {}", e.getMessage(), e);
                                logger.error("handleSingleData执行异常，线程索引：" + threadIndex + "，组大小：" + group.size(), e);
                            } finally {
                                countDownLatch.countDown();
                            }

                        });

                    }

                    XxlJobLogger.log("等待所有线程完毕...................：====");
                    //等待线程全部
                    try {
                        countDownLatch.await();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        logger.error("线程等待被中断", e);
                        throw new RuntimeException("线程被中断", e);
                    }
                    XxlJobLogger.log("所有线程已执行完毕，开始进行数据插入" + distDataCalcWorkIpRoyList.size());

                    List<List<ListMatchDataOverseasMapping>> mappingPartitions = Lists.partition(listMatchDataOverseasMappingUpdateList,1000) ;
                    for(List<ListMatchDataOverseasMapping> mappingPartition : mappingPartitions){
                        try {
                            listMatchDataOverseasMappingService.updateBatchByPrimaryKey(mappingPartition);
                        } catch (Exception e) {
                            XxlJobLogger.log("更新ListMatchDataOverseasMapping异常: {}", e.getMessage(), e);
                            logger.error("更新ListMatchDataOverseasMapping异常，批次大小：" + mappingPartition.size(), e);
                            throw e;
                        }
                    }
                    List<List<DistDataCalcWorkPoint>> pointPartitions = Lists.partition(distDataCalcWorkPointList,1000) ;
                    for(List<DistDataCalcWorkPoint> pointPartition : pointPartitions){
                        try {
                            distDataCalcWorkPointService.addList(pointPartition);
                        } catch (Exception e) {
                            XxlJobLogger.log("插入DistDataCalcWorkPoint异常: {}", e.getMessage(), e);
                            logger.error("插入DistDataCalcWorkPoint异常，批次大小：" + pointPartition.size(), e);
                            throw e;
                        }
                    }

                    Map<Long,Long> poingIdsMap = distDataCalcWorkPointList.stream().collect(Collectors.toMap(DistDataCalcWorkPoint::getFileMappingId,DistDataCalcWorkPoint::getId));
                    distDataCalcWorkIpRoyList.forEach(roy -> {
                        if (roy != null && roy.getPointMappingId() != null) {
                            roy.setPointId(poingIdsMap.get(roy.getPointMappingId()));
                        }
                    });
                    List<List<DistDataCalcWorkIpRoy>> royPartitions = Lists.partition(distDataCalcWorkIpRoyList,1000) ;
                    for(List<DistDataCalcWorkIpRoy> royPartition : royPartitions){
                        try {
                            distDataCalcWorkIpRoyService.addList(royPartition);
                        } catch (Exception e) {
                            XxlJobLogger.log("插入DistDataCalcWorkIpRoy异常: {}", e.getMessage(), e);
                            logger.error("插入DistDataCalcWorkIpRoy异常，批次大小：" + royPartition.size(), e);
                            throw e;
                        }
                    }
                }

            }
        } catch (Exception e) {
            XxlJobLogger.log("categoryCodeNull执行异常: {}", e.getMessage(), e);
            logger.error("categoryCodeNull执行异常", e);
            throw new RuntimeException("categoryCodeNull执行异常", e);
        }

    }

    public void categoryCodeNotNull(List<DistParamOverseas> distParamOverseasList) {
//		2.如果dist_param_overseas.category code不为空，则取对应分配编号下的数据参与计算，
//		   （1）根据category code到list_match_data_basic_done表查询相关分配数据
//		    (2)需要判断dist_list_copy_filter表，看category是否从别的单拉取，判断条件同一般分配

        List<DistParamOverseas> categoryCodeDistParamOverseasList = distParamOverseasList.stream().filter(it ->  StringUtils.isNotBlank(it.getCategoryCode())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(categoryCodeDistParamOverseasList)){
            return;
        }

        List<DistListCopyFilter>  distListCopyFilterDists = distListCopyFilterService.selectDistListCopyFilterListByDistNo(distParamOverseasList.get(0).getDistNo());
        Map<String, List<DistListCopyFilter>> distListCopyFilterMap = distListCopyFilterDists.stream().collect(Collectors.groupingBy(DistListCopyFilter::getToListCategoryCode));
        for(DistParamOverseas distParamOverseas : categoryCodeDistParamOverseasList){
            if(!distListCopyFilterMap.containsKey(distParamOverseas.getCategoryCode())){
                continue;
            }

            List<DistListCopyFilter> distListCopyFilters = distListCopyFilterMap.get(distParamOverseas.getCategoryCode());
            for(DistListCopyFilter distListCopyFilter : distListCopyFilters){
                calDistParamCategoryOList(distListCopyFilter);
            }
        }
    }

    public void handleSingleData(List<ListMatchDataOverseas> listMatchDataOverseasList,
                                 List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingUpdateList,
                                 List<DistDataCalcWorkPoint> distDataCalcWorkPointList,
                                 List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList,
                                 DistParamOverseas distParamOverseas) {
        try {
            if (listMatchDataOverseasList == null || listMatchDataOverseasMappingUpdateList == null ||
                distDataCalcWorkPointList == null || distDataCalcWorkIpRoyList == null || distParamOverseas == null) {
                logger.warn("handleSingleData方法参数存在空值: listMatchDataOverseasList={}, listMatchDataOverseasMappingUpdateList={}, distDataCalcWorkPointList={}, distDataCalcWorkIpRoyList={}, distParamOverseas={}",
                    listMatchDataOverseasList, listMatchDataOverseasMappingUpdateList, distDataCalcWorkPointList, distDataCalcWorkIpRoyList, distParamOverseas);
                return;
            }
            
            List<Long> overseasIds = listMatchDataOverseasList.stream().map(ListMatchDataOverseas::getId).collect(Collectors.toList());
            List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList = listMatchDataOverseasMappingService.getListMatchDataOverseasMappingListByOverseasIds(overseasIds);
            if(CollectionUtils.isEmpty(listMatchDataOverseasMappingList)){
                return;
            }


            Map<Long, List<ListMatchDataOverseasMapping>>  listMatchDataOverseasMappingListMap = listMatchDataOverseasMappingList.stream().collect(Collectors.groupingBy(ListMatchDataOverseasMapping::getOverseasId));
            List<DistOverseasIpShare> distOverseasIpShareList = new ArrayList<>();
            try {
                for(ListMatchDataOverseas listMatchDataOverseas : listMatchDataOverseasList){
                    if(listMatchDataOverseas.getStatus() == 0){
                        continue;
                    }

                    String workUniqueKey = listMatchDataOverseas.getMatchWorkUniqueKey();
                    Long workId = listMatchDataOverseas.getMatchWorkId();
                    Integer workSoicety = listMatchDataOverseas.getMatchWorkSocietyCode();
                    ConcurrentHashMap<String, DistWorkHandler> workUniqueKeyMap = distCalcRoyHandler.getWorkUniqueKeyMap();
                    DistWorkHandler distWorkHandler = workUniqueKeyMap.get(workUniqueKey);

                    if(distWorkHandler == null){
                        distWorkHandler = new DistWorkHandler();
                        workUniqueKeyMap.put(workUniqueKey,distWorkHandler);
                        WrkWork wrkWork = wrkWorkService.getWrkWorkByWorkId(workId, workSoicety);
                        if(wrkWork == null){
                            wrkWork = wrkWorkTransferService.getWrkWorkBySorce(workId,workSoicety,0L,"T");
                            if(wrkWork != null){
                                distWorkHandler.setIsTRansfer(true);
                            }
                        }
                        if(wrkWork == null){
                            continue;
                        }

                        WrkWorkTitle wrkWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWrkId(wrkWork.getWorkId(),wrkWork.getWorkSocietyCode(),0L);

                        if(wrkWorkTitle != null){
                            distWorkHandler.setWorkTitle(wrkWorkTitle);
                        }

                        WrkWorkRight wrkWorkRight = wrkWorkRightService.getWrkWorkRightByWorkUniqueAndRightType(workUniqueKey, "PER");
                        if(wrkWorkRight != null){
                            distWorkHandler.setPerSd(wrkWorkRight.getWorkSd());
                            String sd = StringUtils.equalsIgnoreCase(wrkWorkRight.getWorkSd(), Constants.STRING_DIST_YES) ? Constants.STRING_DIST_NO : Constants.STRING_DIST_YES;
                            distWorkHandler.setPerSd(sd);
                            distWorkHandler.setIsDist( sd.equals(Constants.STRING_DIST_YES) ? Constants.STRING_DIST_NO : Constants.STRING_DIST_YES);
                        }

                        List<WrkWorkIpShare> wrkWorkIpShareByYear = calcIpShareService.calcWorkIpShare(wrkWork, distCalcRoyHandler.getYear(), Arrays.asList("PER"));
                        if(!CollectionUtils.isEmpty(wrkWorkIpShareByYear)){
                            distWorkHandler.setWorkIpShareList(wrkWorkIpShareByYear);
                            Map<String,String> ipsocMap = wrkWorkIpShareByYear.stream().filter(w -> StringUtils.isNotBlank(w.getIpNameNo())).collect(Collectors.toMap(WrkWorkIpShare::getIpNameNo,WrkWorkIpShare::getIpSocietyCode,(a, b) -> a));
                            List<WrkWorkIpShare> wrkWorkIpShareList = wrkWorkIpShareService.getWrkWorkIpShareByWorkUniqueKey(wrkWork.getWorkUniqueKey(),"PER");
                            Map<String,List<WrkWorkIpShare>> wrkWorkIpShareMap = wrkWorkIpShareList.stream().filter(w -> StringUtils.isNotBlank(w.getIpNameNo())).collect(Collectors.groupingBy(WrkWorkIpShare::getIpNameNo));
                            for(String ipNameNo : wrkWorkIpShareMap.keySet()){
                                List<WrkWorkIpShare> ipNameShare = wrkWorkIpShareMap.get(ipNameNo);
                                BigDecimal ipShare = ipNameShare.stream().map(w -> w.getIpShare()).reduce(BigDecimal.ZERO,BigDecimal::add);
                                WrkWorkIpShare wrkWorkIpShare = ipNameShare.get(0);
                                DistOverseasIpShare distOverseasIpShare = new DistOverseasIpShare();
                                distOverseasIpShare.setIpNameNo(ipNameNo);
                                distOverseasIpShare.setDistNo(distCalcRoyHandler.getDistParamInfo().getDistNo());
                                distOverseasIpShare.setWorkUniqueKey(wrkWorkIpShare.getWorkUniqueKey());
                                distOverseasIpShare.setIpSoc(ipsocMap.get(ipNameNo));
                                distOverseasIpShare.setWorkIpShare(ipShare);
                                distOverseasIpShareList.add(distOverseasIpShare);
                            }
                        }
                    }

                    DistDataCalcWorkPoint distDataCalcWorkPoint = getDistDataCalcWorkPoint(listMatchDataOverseas);
                    if (distDataCalcWorkPoint != null) {
                        distDataCalcWorkPointList.add(distDataCalcWorkPoint);
                    }

                    List<WrkWorkIpShare> wrkWorkIpShareByYear = distWorkHandler.getWorkIpShareList();
                    if(CollectionUtils.isEmpty(wrkWorkIpShareByYear)){
                        continue;
                    }

                    Map<String,List<WrkWorkIpShare>> wrkWorkIpShareByIPNameNoMap = wrkWorkIpShareByYear.stream().filter(w -> StringUtils.isNotBlank(w.getIpNameNo())).collect(Collectors.groupingBy(WrkWorkIpShare::getIpNameNo));
                    List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList1 = listMatchDataOverseasMappingListMap.get(listMatchDataOverseas.getId());
                    if(CollectionUtils.isEmpty(listMatchDataOverseasMappingList1)){
                        continue;
                    }
                    for(ListMatchDataOverseasMapping listMatchDataOverseasMapping : listMatchDataOverseasMappingList1){
                        String sd = Constants.STRING_DIST_NO;
                        if(listMatchDataOverseasMapping.getStatus() == 0){
                            if(wrkWorkIpShareByIPNameNoMap.containsKey(listMatchDataOverseasMapping.getIpNameNo())){
                                List<WrkWorkIpShare> wrkWorkIpShareList = wrkWorkIpShareByIPNameNoMap.get(listMatchDataOverseasMapping.getIpNameNo());
                                // 添加空指针检查
                                if (CollectionUtils.isEmpty(wrkWorkIpShareList)) {
                                    continue;
                                }
                                BigDecimal ipshare = wrkWorkIpShareList.stream().filter(i->i.getIpShare()!=null && i.getIpShare().compareTo(BigDecimal.ZERO) == 1).map(WrkWorkIpShare::getIpShare).reduce(BigDecimal.ZERO, BigDecimal::add);
                                WrkWorkIpShare wrkWorkIpShare = wrkWorkIpShareList.get(0);
                                if(StringUtils.equals(wrkWorkIpShare.getSd(),Constants.STRING_DIST_YES)){
                                    sd = Constants.STRING_DIST_YES;
                                }
                                listMatchDataOverseasMapping.setStatus(1);
                                listMatchDataOverseasMapping.setMatchIpNameNo(wrkWorkIpShare.getIpNameNo());
                                listMatchDataOverseasMapping.setMatchIpName(StringUtils.isBlank(wrkWorkIpShare.getChineseName()) ? wrkWorkIpShare.getName() : wrkWorkIpShare.getChineseName() );
                                listMatchDataOverseasMapping.setMatchIpSoc(Integer.valueOf(wrkWorkIpShare.getIpSocietyCode()));
                                listMatchDataOverseasMapping.setMatchIpShare(ipshare);
                                listMatchDataOverseasMapping.setMatchIpRole(wrkWorkIpShare.getWorkIpRole());
                                listMatchDataOverseasMapping.setMatchIpType(wrkWorkIpShare.getIpType());
                                listMatchDataOverseasMapping.setAmendTime(new Date());
                                listMatchDataOverseasMapping.setMatchFlag(true);
                                listMatchDataOverseasMapping.setManual(false);
                                if(!StringUtils.equals(wrkWorkIpShare.getIpSocietyCode(),"161")){
                                    listMatchDataOverseasMapping.setRejectCode("F02");
                                    listMatchDataOverseasMapping.setRejectMessage("MUST MEMBER");
                                }
                            } else {
                                // ip not in work
                                if(!wrkWorkIpShareByIPNameNoMap.containsKey(listMatchDataOverseasMapping.getIpNameNo())){
                                    listMatchDataOverseasMapping.setNotInWork(1);
                                    listMatchDataOverseasMapping.setAmendTime(new Date());

                                    MbrIpName mbrIpName = distCalcRoyHandler.getPaNameMap().get(listMatchDataOverseasMapping.getIpNameNo());
                                    if(mbrIpName == null){
                                        mbrIpName = mbrIpNameService.getPANameByIpNameNo(listMatchDataOverseasMapping.getIpNameNo());
                                    }

                                    if(mbrIpName != null){
                                        distCalcRoyHandler.getPaNameMap().put(listMatchDataOverseasMapping.getIpNameNo(), mbrIpName);
                                        listMatchDataOverseasMapping.setMatchIpName(StringUtils.isBlank(mbrIpName.getChineseName()) ? mbrIpName.getName() : mbrIpName.getChineseName() );
                                        listMatchDataOverseasMapping.setMatchPaNameNo(mbrIpName.getIpNameNo());
                                    }
                                }
                                continue;
                            }
                            listMatchDataOverseasMappingUpdateList.add(listMatchDataOverseasMapping);

                        } else {
                            // ip not in work
                            if(!wrkWorkIpShareByIPNameNoMap.containsKey(listMatchDataOverseasMapping.getMatchIpNameNo())){
                                listMatchDataOverseasMapping.setNotInWork(1);
                                listMatchDataOverseasMapping.setAmendTime(new Date());


                                MbrIpName mbrIpName = distCalcRoyHandler.getPaNameMap().get(listMatchDataOverseasMapping.getMatchIpNameNo());
                                if(mbrIpName == null){
                                    mbrIpName = mbrIpNameService.getPANameByIpNameNo(listMatchDataOverseasMapping.getMatchIpNameNo());
                                }

                                if(mbrIpName != null){
                                    distCalcRoyHandler.getPaNameMap().put(listMatchDataOverseasMapping.getMatchIpNameNo(), mbrIpName);
                                    listMatchDataOverseasMapping.setMatchIpName(StringUtils.isBlank(mbrIpName.getChineseName()) ? mbrIpName.getName() : mbrIpName.getChineseName() );
                                    listMatchDataOverseasMapping.setMatchPaNameNo(mbrIpName.getIpNameNo());
                                }
//                            listMatchDataOverseasMappingService.update(listMatchDataOverseasMapping);
                                listMatchDataOverseasMappingUpdateList.add(listMatchDataOverseasMapping);
                            } else {
                                List<WrkWorkIpShare> wrkWorkIpShareList = wrkWorkIpShareByIPNameNoMap.get(listMatchDataOverseasMapping.getMatchIpNameNo());
                                // 添加空指针检查
                                if (CollectionUtils.isEmpty(wrkWorkIpShareList)) {
                                    continue;
                                }
                                BigDecimal ipshare = wrkWorkIpShareList.stream().filter(i->i.getIpShare()!=null && i.getIpShare().compareTo(BigDecimal.ZERO) == 1).map(WrkWorkIpShare::getIpShare).reduce(BigDecimal.ZERO, BigDecimal::add);
                                
                                // 添加空指针检查
                                WrkWorkIpShare wrkWorkIpShare = wrkWorkIpShareList.get(0);
                                if (wrkWorkIpShare == null) {
                                    continue;
                                }
                                
                                if(StringUtils.equals(wrkWorkIpShare.getSd(),Constants.STRING_DIST_YES)){
                                    sd = Constants.STRING_DIST_YES;
                                }
                                if(!StringUtils.equals(wrkWorkIpShare.getIpSocietyCode(),"161")){
                                    if(!StringUtils.equals(listMatchDataOverseasMapping.getRejectCode(),"F02")){
                                        listMatchDataOverseasMapping.setRejectCode("F02");
                                        listMatchDataOverseasMapping.setRejectMessage("NOT MUST MEMBER");
                                        listMatchDataOverseasMapping.setMatchIpSoc(Integer.valueOf(wrkWorkIpShare.getIpSocietyCode()));
                                        listMatchDataOverseasMapping.setMatchIpShare(ipshare);
                                        listMatchDataOverseasMapping.setAmendTime(new Date());
                                        listMatchDataOverseasMappingUpdateList.add(listMatchDataOverseasMapping);
                                    }

//                            continue;
                                }

                                if(listMatchDataOverseasMapping.getMatchIpSoc() == null){
                                    listMatchDataOverseasMapping.setMatchIpShare(ipshare);
                                    listMatchDataOverseasMapping.setMatchIpSoc(161);
                                    listMatchDataOverseasMapping.setMatchIpRole(wrkWorkIpShare.getWorkIpRole());
                                    listMatchDataOverseasMapping.setMatchIpType(wrkWorkIpShare.getIpType());
                                    listMatchDataOverseasMapping.setAmendTime(new Date());
                                    listMatchDataOverseasMappingUpdateList.add(listMatchDataOverseasMapping);
                                }
                            }
                        }

                        DistDataCalcWorkIpRoy distDataCalcWorkIpRoy = getDistDataCalcWorkIpRoy(listMatchDataOverseasMapping, listMatchDataOverseas, distParamOverseas);
                        if (distDataCalcWorkIpRoy != null) {
                            distDataCalcWorkIpRoy.setSd( sd);
                            distDataCalcWorkIpRoy.setPointMappingId(listMatchDataOverseas.getFileMappingId());
                            if(StringUtils.isEmpty(distDataCalcWorkIpRoy.getIsDist())){
                                XxlJobLogger.log("distDataCalcWorkIpRoy.isDIst = null,listMatchDataOverseasMapping.id = {}", listMatchDataOverseasMapping.getId());
                                logger.info("distDataCalcWorkIpRoy.isDIst = null,listMatchDataOverseasMapping.id = {}", listMatchDataOverseasMapping.getId());
                            }
                            distDataCalcWorkIpRoyList.add(distDataCalcWorkIpRoy);
                        }
                    }
                }

                if(!CollectionUtils.isEmpty(distOverseasIpShareList)){
                    distOverseasIpShareService.addList(distOverseasIpShareList);
                }
            } catch (Exception e) {
                logger.error("handleSingleData内部处理异常", e);
                XxlJobLogger.log("handleSingleData内部处理异常: {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            logger.error("handleSingleData执行异常", e);
            XxlJobLogger.log("handleSingleData执行异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    public void calDistParamCategoryOList(DistListCopyFilter distListCopyFilter){

        String fromDistNo = distListCopyFilter.getFromDistNo();
        String fromListCategoryCode = distListCopyFilter.getFromListCategoryCode();
        Long startId = 0l;
        while ( true){
            List<DistDataCalcWorkPoint> distDataCalcWorkPointList = distDataCalcWorkPointService.getSpecialContidion(fromDistNo, fromListCategoryCode, startId, distListCopyFilter.getStartPerfDate(), distListCopyFilter.getEndPerfDate());
            if(CollectionUtils.isEmpty(distDataCalcWorkPointList)){
                break;
            }

            startId = distDataCalcWorkPointList.get(distDataCalcWorkPointList.size() - 1).getId();


            Map<String,List<DistDataCalcWorkPoint>> distDataCalcWorkPointMap = distDataCalcWorkPointList.stream().collect(Collectors.groupingBy(DistDataCalcWorkPoint :: getWorkUniqueKey));
            for(Map.Entry<String,List<DistDataCalcWorkPoint>> entry : distDataCalcWorkPointMap.entrySet()){
                String workUniqueKey = entry.getKey();

                List<DistDataCalcWorkPoint> distDataCalcWorkPointList1 = entry.getValue();

                Long workId = distDataCalcWorkPointList1.get(0).getWorkId();
                Integer workSocietyCode = distDataCalcWorkPointList1.get(0).getWorkSocietyCode();

                ConcurrentHashMap<String, DistWorkHandler> workUniqueKeyMap = distCalcRoyHandler.getWorkUniqueKeyMap();

                DistWorkHandler distWorkHandler = workUniqueKeyMap.get(workUniqueKey);

                if(distWorkHandler == null){

                    distWorkHandler = new DistWorkHandler();

                    workUniqueKeyMap.put(workUniqueKey,distWorkHandler);

                    WrkWork wrkWork = wrkWorkService.getWrkWorkByWorkUniqueKey(workUniqueKey);

                    if(null == wrkWork){
                        wrkWork = wrkWorkTransferService.getWrkWorkBySorce( workId,workSocietyCode,0L,"T") ;
                        if(wrkWork != null){
                            distWorkHandler.setIsTRansfer(true);
                        }
                    }

                    if(wrkWork == null){
                        continue;
                    }

                    distWorkHandler.setWrkWrok(wrkWork);
                    distWorkHandler.setIsTRansfer(true);

                    WrkWorkTitle wrkWorkTitle = wrkWorkTitleService.getWrkWorkTitleByWrkId(wrkWork.getWorkId(),wrkWork.getWorkSocietyCode(),0L);

                    if(wrkWorkTitle != null){
                        distWorkHandler.setWorkTitle(wrkWorkTitle);
                    }

                    WrkWorkRight wrkWorkRight = wrkWorkRightService.getWrkWorkRightByWorkUniqueAndRightType(workUniqueKey, "PER");
                    if(wrkWorkRight != null){
                        distWorkHandler.setPerSd(wrkWorkRight.getWorkSd());
                        String sd = StringUtils.equalsIgnoreCase(wrkWorkRight.getWorkSd(), Constants.STRING_DIST_YES) ? Constants.STRING_DIST_NO : Constants.STRING_DIST_YES;
                        distWorkHandler.setPerSd(sd);
                        distWorkHandler.setIsDist( sd.equals(Constants.STRING_DIST_YES) ? Constants.STRING_DIST_NO : Constants.STRING_DIST_YES);
                    }

                    List<WrkWorkIpShare> workIpShareList = calcIpShareService.calcWorkIpShare(wrkWork, distCalcRoyHandler.getYear(), distCalcRoyHandler.getRightTypeList());
                    if(CollectionUtils.isEmpty(workIpShareList)){
                        continue;
                    }
                   /* workIpShareList = workIpShareList.stream().filter(w -> w.getIpShare()!= null
                            && StringUtils.equals(w.getIpSocietyCode(),"161")
                            && StringUtils.isNotBlank(w.getIpNameNo())).collect(Collectors.toList());*/

                    distWorkHandler.setWorkIpShareList(workIpShareList);

                }

                WrkWork wrkWork = distWorkHandler.getWrkWrok();
                if(wrkWork == null){
                    continue;
                }

                List<WrkWorkIpShare> workIpShareList = distWorkHandler.getWorkIpShareList();

                if(CollectionUtils.isEmpty(workIpShareList)){
                    continue;
                }

                String isDist  = distWorkHandler.getIsDist();
                distDataCalcWorkPointList1.forEach(d -> {
                    d.setDistNo(distCalcRoyHandler.getDistParamInfo().getDistNo());
                    d.setId(null);
                    d.setCreateTime(new Date());
                    d.setAmendTime(new Date());
                    d.setCategoryCode(distListCopyFilter.getToListCategoryCode());
                    d.setPoolRight("DB");
                    d.setPoolCode("D");
                    d.setIsDist(isDist);
                });

                distDataCalcWorkPointService.addList(distDataCalcWorkPointList1);
                List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoy(wrkWork, distDataCalcWorkPointList1,workIpShareList,"PER",distListCopyFilter.getToListSourceCode());
                distDataCalcWorkIpRoyService.addList(distDataCalcWorkIpRoyList);

            }
        }
    }

    private List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoy(WrkWork wrkWork,
                                                              List<DistDataCalcWorkPoint> distDataCalcWorkPointList, List<WrkWorkIpShare> wrkWorkIpShareList,
                                                              String rightType, String sourceName) {

        wrkWorkIpShareList = wrkWorkIpShareList.stream().filter(w -> w.getIpShare()!= null && StringUtils.equals(w.getIpSocietyCode(),"161")
                && StringUtils.isNotBlank(w.getIpNameNo())).collect(Collectors.toList());
        List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = new ArrayList<>();
        logger.info("WrkWork.work_id:{},WrkWork.worksoc：{}",wrkWork.getWorkId(),wrkWork.getWorkSocietyCode());
        for(DistDataCalcWorkPoint distDataCalcWorkPoint : distDataCalcWorkPointList){
            BigDecimal big_100 = new BigDecimal(100 );
            BigDecimal availableIpshare = wrkWorkIpShareList.stream().map(ip -> ip.getIpShare()).reduce(BigDecimal.ZERO,BigDecimal :: add);

            BigDecimal netPoint = distDataCalcWorkPoint.getGrossPoint().multiply(availableIpshare).divide(big_100,6,RoundingMode.HALF_UP);
            distDataCalcWorkPoint.setNetPoint(netPoint);

            for (WrkWorkIpShare wrkWorkIpShare : wrkWorkIpShareList) {
                String ipBaseNo = wrkWorkIpShare.getIpBaseNo();
                String ipNameNo = wrkWorkIpShare.getIpNameNo();


                DistDataCalcWorkIpRoy distDataCalcWorkIpRoy = new DistDataCalcWorkIpRoy();
                distDataCalcWorkIpRoy.init();
                distDataCalcWorkIpRoy.setPointId(distDataCalcWorkPoint.getId());
                distDataCalcWorkIpRoy.setDistNo(distCalcRoyHandler.getDistParamInfo().getDistNo());
                distDataCalcWorkIpRoy.setCategoryCode(distDataCalcWorkPoint.getCategoryCode());

                distDataCalcWorkIpRoy.setPoolCode(distDataCalcWorkPoint.getPoolCode());
                distDataCalcWorkIpRoy.setPoolRight(distDataCalcWorkPoint.getPoolRight());

                distDataCalcWorkIpRoy.setWorkId(distDataCalcWorkPoint.getWorkId());
                distDataCalcWorkIpRoy.setWorkSocietyCode(distDataCalcWorkPoint.getWorkSocietyCode());
                distDataCalcWorkIpRoy.setWorkUniqueKey(distDataCalcWorkPoint.getWorkUniqueKey());
                distDataCalcWorkIpRoy.setTitleId(distDataCalcWorkPoint.getWorkTitleId());
                distDataCalcWorkIpRoy.setIpBaseNo(wrkWorkIpShare.getIpBaseNo());
                distDataCalcWorkIpRoy.setIpNameNo(ipNameNo);
                distDataCalcWorkIpRoy.setIpSocietyCode(wrkWorkIpShare.getIpSocietyCode());

                Map<String,MbrIpName> paNameMap = distCalcRoyHandler.getPaNameMap();
                MbrIpName paName = paNameMap.get(ipNameNo);
                if(paName == null){
                    paName = mbrIpNameService.getPANameByIpBaseNo(ipBaseNo);
                    if(paName != null){
                        paNameMap.put(ipNameNo,paName);
                    }
                }

                if(paName != null){
                    distDataCalcWorkIpRoy.setPaNameNo(paName.getIpNameNo());
                }

                Map<String,String> ipTypeMap = distCalcRoyHandler.getIpTypeMap();
                String ipType = ipTypeMap.get(ipNameNo);
                if(ipType == null){
                    MbrIp mbrIp = mbrIpService.get(ipBaseNo);
                    if (mbrIp != null) {
                        ipTypeMap.put(ipBaseNo,mbrIp.getIpType());
                    }
                }


                distDataCalcWorkIpRoy.setRightType(rightType);
                distDataCalcWorkIpRoy.setWorkIpRole(wrkWorkIpShare.getWorkIpRole());
                distDataCalcWorkIpRoy.setIpShare(wrkWorkIpShare.getIpShare());
                distDataCalcWorkIpRoy.setUsage(distDataCalcWorkPoint.getUsage());
                distDataCalcWorkIpRoy.setWorkType(distDataCalcWorkPoint.getWorkType());

                if (wrkWork != null) {
                    distDataCalcWorkIpRoy.setRefWorkId(wrkWork.getRefWorkId());
                    distDataCalcWorkIpRoy.setRefWorkSociety(wrkWork.getRefWorkSociety());
                }

                distDataCalcWorkIpRoy.setAvWorkId(distDataCalcWorkPoint.getAvWorkId());
                distDataCalcWorkIpRoy.setAvWorkSociety(distDataCalcWorkPoint.getAvWorksSocietyCode());
                distDataCalcWorkIpRoy.setAvTitleId(distDataCalcWorkPoint.getAvTitleId());

                distDataCalcWorkIpRoy.setGrossPoint(distDataCalcWorkPoint.getGrossPoint());
                if(wrkWorkIpShare.getIpShare().compareTo(BigDecimal.ZERO) > 0){
                    BigDecimal ipNetPoint = distDataCalcWorkPoint.getGrossPoint().multiply(wrkWorkIpShare.getIpShare()).divide(big_100,6, RoundingMode.HALF_UP);
                    distDataCalcWorkIpRoy.setNetPoint(ipNetPoint);
                } else {
                    distDataCalcWorkPoint.setNetPoint(BigDecimal.ZERO);
                }
//
//				  `dist_roy` decimal(16,6) DEFAULT NULL COMMENT '分配金额，根据积点计算',
                distDataCalcWorkIpRoy.setPerformDate(distDataCalcWorkPoint.getPerformTime());
                distDataCalcWorkIpRoy.setGroupIndicator(wrkWorkIpShare.getGroupIndicator());
                if(distDataCalcWorkPoint.getIsDist().equals(Constants.STRING_DIST_NO)) {
                    distDataCalcWorkIpRoy.setSd(Constants.STRING_DIST_YES);
                    distDataCalcWorkIpRoy.setIsDist(Constants.STRING_DIST_NO);
                }else {
                    distDataCalcWorkIpRoy.setSd(wrkWorkIpShare.getSd());
                    distDataCalcWorkIpRoy.setIsDist("N");
                }
                distDataCalcWorkIpRoy.setSourceType(distCalcRoyHandler.getDistParamInfo().getDistNo().substring(0,1));
                distDataCalcWorkIpRoy.setFileMappingId(distDataCalcWorkPoint.getFileMapppingId());
                distDataCalcWorkIpRoy.setFileBaseId(distDataCalcWorkPoint.getFileBaseId());

                JSONObject extJson = new JSONObject();
                if (StringUtils.isNotBlank(wrkWorkIpShare.getAssociatedAgrNo())) {
                    extJson.put("agrNo", wrkWorkIpShare.getAssociatedAgrNo());
                }else if(StringUtils.isNotBlank(wrkWorkIpShare.getAgrNo())){
                    extJson.put("agrNo", wrkWorkIpShare.getAgrNo());
                }
                distDataCalcWorkIpRoy.setExtJson(extJson.toString());
                distDataCalcWorkIpRoy.setSourceName(sourceName);

                distDataCalcWorkIpRoyList.add(distDataCalcWorkIpRoy);

            }

        }

        return distDataCalcWorkIpRoyList;

    }

    private DistDataCalcWorkPoint getDistDataCalcWorkPoint(ListMatchDataOverseas listMatchDataOverseas) {
        if(listMatchDataOverseas.getMatchWorkId() == null){
            listMatchDataOverseas.setMatchWorkId(0L);
        }
        if(listMatchDataOverseas.getMatchWorkSocietyCode() == null){
            listMatchDataOverseas.setMatchWorkSocietyCode(0);
        }
        DistDataCalcWorkPoint distDataCalcWorkPoint = new DistDataCalcWorkPoint();
        BeanUtils.copyProperties(listMatchDataOverseas, distDataCalcWorkPoint);
        distDataCalcWorkPoint.setCreateTime(new Date());
        distDataCalcWorkPoint.setId(null);
        distDataCalcWorkPoint.setDistNo(distCalcRoyHandler.getDistParamInfo().getDistNo());
        String workUniqueKey = Constants.getWorkUniqueKey(listMatchDataOverseas.getMatchWorkSocietyCode(),
                listMatchDataOverseas.getMatchWorkId());
//        WrkWorkRight wrkWorkRight = wrkWorkRightMap.get(workUniqueKey);
        /*if (wrkWorkRight != null && wrkWorkRight.getWorkSd().equalsIgnoreCase(Constants.STRING_DIST_YES)) {
            distDataCalcWorkPoint.setIsDist(Constants.STRING_DIST_NO);
        }else {
            distDataCalcWorkPoint.setIsDist(Constants.STRING_DIST_YES);
        }*/
        // MUST-2997 待审核、审核失败的 is_dist = N
//        if(listMatchDataOverseas.getStatus().equals(Constants.OVERSEA__STATUS_0) || listMatchDataOverseas.getStatus().equals(Constants.OVERSEA__STATUS_2)) {
//            distDataCalcWorkPoint.setIsDist(Constants.STRING_DIST_NO);
//        }
//        distDataCalcWorkPoint.setSourceDistNo(listMatchDataOverseas.getRemitDistNo()); // TODO

        // 添加空指针检查
        DistWorkHandler distWorkHandler = distCalcRoyHandler.getWorkUniqueKeyMap().get(workUniqueKey);
        if (distWorkHandler != null) {
            distDataCalcWorkPoint.setIsDist(distWorkHandler.getIsDist());
        } else {
            distDataCalcWorkPoint.setIsDist(Constants.STRING_DIST_YES);
        }
        distDataCalcWorkPoint.setSourceWorkCode(listMatchDataOverseas.getSourceWorkCode());
        distDataCalcWorkPoint.setWorkId(listMatchDataOverseas.getMatchWorkId());
        distDataCalcWorkPoint.setWorkSocietyCode(listMatchDataOverseas.getMatchWorkSocietyCode());
        distDataCalcWorkPoint.setWorkUniqueKey(workUniqueKey);
        distDataCalcWorkPoint.setWorkTitleId(listMatchDataOverseas.getMatchWorkTitleId());
        distDataCalcWorkPoint.setWorkType(listMatchDataOverseas.getMatchWorkType());
        distDataCalcWorkPoint.setGrossPoint(new BigDecimal(0));
        distDataCalcWorkPoint.setCategoryCode("OSD");
        return distDataCalcWorkPoint;
    }

    private DistDataCalcWorkIpRoy getDistDataCalcWorkIpRoy(ListMatchDataOverseasMapping listMatchDataOverseasMapping,ListMatchDataOverseas listMatchDataOverseas,
                                                           DistParamOverseas distParamOverseas) {

        if(listMatchDataOverseasMapping.getMatchWorkId() == null){
            listMatchDataOverseasMapping.setMatchWorkId(0L);
        }
        if(listMatchDataOverseasMapping.getMatchWorkSocietyCode() == null){
            listMatchDataOverseasMapping.setMatchWorkSocietyCode(0);
        }
        DistDataCalcWorkIpRoy distDataCalcWorkIpRoy = new DistDataCalcWorkIpRoy();
        BeanUtils.copyProperties(listMatchDataOverseasMapping, distDataCalcWorkIpRoy);
        distDataCalcWorkIpRoy.setCreateTime(new Date());
        distDataCalcWorkIpRoy.setSourceType("O");
        distDataCalcWorkIpRoy.setPoolCode("O");
        distDataCalcWorkIpRoy.setDistNo(distCalcRoyHandler.getDistParamInfo().getDistNo());
        
        // 添加空指针检查
        if (listMatchDataOverseasMapping.getAmount() != null && distParamOverseas != null && distParamOverseas.getExchangeRate() != null) {
            BigDecimal amount = listMatchDataOverseasMapping.getAmount().multiply(distParamOverseas.getExchangeRate()).setScale(0,  RoundingMode.HALF_UP);
            distDataCalcWorkIpRoy.setNetPoint(amount);
            distDataCalcWorkIpRoy.setDistRoy(amount);
        }
        if (listMatchDataOverseasMapping.getShareRatio() != null) {
            distDataCalcWorkIpRoy.setIpShare(listMatchDataOverseasMapping.getShareRatio());
        }

        distDataCalcWorkIpRoy.setWorkId(listMatchDataOverseasMapping.getMatchWorkId());
        distDataCalcWorkIpRoy.setWorkSocietyCode(listMatchDataOverseasMapping.getMatchWorkSocietyCode());
        String workUniqueKey = Constants.getWorkUniqueKey(listMatchDataOverseasMapping.getMatchWorkSocietyCode(),
                listMatchDataOverseasMapping.getMatchWorkId());
        distDataCalcWorkIpRoy.setWorkUniqueKey(workUniqueKey);
        distDataCalcWorkIpRoy.setTitleId(listMatchDataOverseasMapping.getMatchWorkTitleId());
        if (listMatchDataOverseasMapping.getAmount() != null) {
            distDataCalcWorkIpRoy.setGrossPoint(listMatchDataOverseasMapping.getAmount());  // TODO 绩点 = 金额？
        }
        distDataCalcWorkIpRoy.setSd("N");
        distDataCalcWorkIpRoy.setIpSocietyCode(listMatchDataOverseasMapping.getMatchIpSoc() + "");
        distDataCalcWorkIpRoy.setWorkType(listMatchDataOverseas.getMatchWorkType());
        distDataCalcWorkIpRoy.setTitleId(listMatchDataOverseas.getMatchWorkTitleId());
        distDataCalcWorkIpRoy.setCategoryCode("OSD");
        distDataCalcWorkIpRoy.setSourceName("OVERSEAS DISTRIBUTION");

        if(listMatchDataOverseasMapping.getStatus().equals(Constants.OVERSEA__STATUS_0) || listMatchDataOverseasMapping.getStatus().equals(Constants.OVERSEA__STATUS_2)) {
            distDataCalcWorkIpRoy.setIsDist(Constants.STRING_DIST_NO);
        } else {
            distDataCalcWorkIpRoy.setIsDist(Constants.STRING_DIST_YES);
        }

        distDataCalcWorkIpRoy.setPaNameNo(listMatchDataOverseasMapping.getMatchPaNameNo()); // TODO PaNameNo

        String ipNameNo = listMatchDataOverseasMapping.getMatchIpNameNo();
        // 添加空指针检查
        if (ipNameNo != null && distCalcRoyHandler != null && distCalcRoyHandler.getIpNameVOMap() != null) {
            IpNameVO ipNameVO = distCalcRoyHandler.getIpNameVOMap().get(ipNameNo);
            if(ipNameVO == null){
                List<IpNameVO> ipNameVOList = mbrIpNameService.getIpNameVOByIpNameNo(ipNameNo);
                if(ipNameVOList != null && !ipNameVOList.isEmpty() && !distCalcRoyHandler.getIpNameVOMap().containsKey(ipNameNo)){
                    ipNameVO = ipNameVOList.get(0);
                    distCalcRoyHandler.getIpNameVOMap().put(ipNameNo, ipNameVO);
                }

            }

            if(ipNameVO != null){
                distDataCalcWorkIpRoy.setNameType(ipNameVO.getNameType());
                distDataCalcWorkIpRoy.setIpType(ipNameVO.getIpType());
                distDataCalcWorkIpRoy.setIpBaseNo(ipNameVO.getIpBaseNo());
            }
        }
        return distDataCalcWorkIpRoy;
    }

}
