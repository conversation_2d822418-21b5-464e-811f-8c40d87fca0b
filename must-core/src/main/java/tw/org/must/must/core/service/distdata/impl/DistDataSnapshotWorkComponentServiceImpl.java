package tw.org.must.must.core.service.distdata.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.distdata.DistDataSnapshotWorkComponentService;
import tw.org.must.must.mapper.distdata.DistDataSnapshotWorkComponentMapper;
import tw.org.must.must.model.distdata.DistDataSnapshotWorkComponent;

@Service
public class DistDataSnapshotWorkComponentServiceImpl extends BaseServiceImpl<DistDataSnapshotWorkComponent> implements DistDataSnapshotWorkComponentService {

	private final DistDataSnapshotWorkComponentMapper distDataSnapshotWorkComponentMapper;

    @Autowired
    public DistDataSnapshotWorkComponentServiceImpl(DistDataSnapshotWorkComponentMapper distDataSnapshotWorkComponentMapper) {
        super(distDataSnapshotWorkComponentMapper);
        this.distDataSnapshotWorkComponentMapper = distDataSnapshotWorkComponentMapper;
    }
	@Override
	public Integer clearDistDataSnapshot(String distNo) {
		if(StringUtils.isBlank(distNo))
			return -1;
		Example example = new Example(DistDataSnapshotWorkComponent.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo("distNo", distNo);
		return distDataSnapshotWorkComponentMapper.deleteByExample(example);
	}
}