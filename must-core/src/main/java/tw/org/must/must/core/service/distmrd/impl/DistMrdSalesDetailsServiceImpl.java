package tw.org.must.must.core.service.distmrd.impl;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.core.redis.NoGener;
import tw.org.must.must.core.service.distmrd.DistMrdSalesDetailsService;
import tw.org.must.must.mapper.dist.DistMrdScMapper;
import tw.org.must.must.mapper.distmrd.DistMrdSalesDetailsMapper;
import tw.org.must.must.mapper.distmrd.DistMrdSalesWorkIpMapper;
import tw.org.must.must.mapper.distmrd.DistMrdSalesWorkMapper;
import tw.org.must.must.model.dist.DistMrdSc;
import tw.org.must.must.model.distmrd.DistMrdSalesDetails;
import tw.org.must.must.model.distmrd.DistMrdSalesWork;
import tw.org.must.must.model.distmrd.DistMrdSalesWorkIp;
import tw.org.must.must.model.distmrd.vo.DistMrdSalesDetailsVo;
import tw.org.must.must.model.distmrd.vo.DistMrdSalesWorkVo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class DistMrdSalesDetailsServiceImpl extends BaseServiceImpl<DistMrdSalesDetails> implements DistMrdSalesDetailsService {

    private final DistMrdSalesDetailsMapper distMrdSalesDetailsMapper;
    @Autowired
    private DistMrdSalesWorkMapper distMrdSalesWorkMapper;
    @Autowired
    private DistMrdSalesWorkIpMapper distMrdSalesWorkIpMapper;
    @Autowired
    private DistMrdScMapper distMrdScMapper;

    @Autowired
    public DistMrdSalesDetailsServiceImpl(DistMrdSalesDetailsMapper distMrdSalesDetailsMapper) {
        super(distMrdSalesDetailsMapper);
        this.distMrdSalesDetailsMapper = distMrdSalesDetailsMapper;
    }

    @Override
    public List<DistMrdSalesDetails> listDistMrdSalesDetails(Long salesId) {
        Example example = new Example(DistMrdSalesDetails.class);
        example.orderBy("createTime").desc();
        return distMrdSalesDetailsMapper.selectByExample(example);
    }

    @Override
    @Transactional
    public DistMrdSalesDetailsVo saveDistMrdSalesDetails(DistMrdSalesDetailsVo mrdSalesDetailsVo) {
        DistMrdSalesDetailsVo returnDistMrdSalesDetailsVo = new DistMrdSalesDetailsVo();//返回数据
        DistMrdSalesDetails mrdSalesDetails = mrdSalesDetailsVo.getDistMrdSalesDetails();
        if (mrdSalesDetails.getFromPeriod() == null) {
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "FromPeriod"));
        }
        if (mrdSalesDetails.getToPeriod() == null) {
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "ToPeriod"));
        }
        if (mrdSalesDetails.getUnitPrice() == null) {
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "UnitPrice"));
        }
        if (mrdSalesDetails.getUnitSold() == null) {
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "UnitSold"));
        }
        if (mrdSalesDetails.getRoyRate() == null) {
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "RoyRate"));
        }
        mrdSalesDetails.init();
        if (mrdSalesDetails.getBatchSeqNo() == null){
            mrdSalesDetails.setBatchSeqNo(Integer.valueOf(String.valueOf(NoGener.getIncrement(mrdSalesDetails.getBatchNo().toString()))));
        }
        if (mrdSalesDetails.getId() == null) {
            distMrdSalesDetailsMapper.insertUseGeneratedKeys(mrdSalesDetails);
        } else {
            distMrdSalesDetailsMapper.updateByPrimaryKeySelective(mrdSalesDetails);
        }
        returnDistMrdSalesDetailsVo.setDistMrdSalesDetails(mrdSalesDetails);

        //批量更新，删除，新增DistMrdSalesWork
        List<DistMrdSalesWorkVo> distMrdSalesWorkVoList = mrdSalesDetailsVo.getDistMrdSalesWorkVoList();
        if (CollectionUtils.isEmpty(distMrdSalesWorkVoList)) {
            Example distMrdSalesWorkExample = new Example(DistMrdSalesWork.class);
            Example.Criteria distMrdSalesWorkCriteria = distMrdSalesWorkExample.createCriteria();
            distMrdSalesWorkCriteria.andEqualTo("salesDetailId", mrdSalesDetails.getId());
            List<DistMrdSalesWork> distMrdSalesWorkList = distMrdSalesWorkMapper.selectByExample(distMrdSalesWorkExample);
            List<String> delDistMrdSalesWorkIds = distMrdSalesWorkList.stream().map(distMrdSalesWork -> distMrdSalesWork.getId().toString()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(delDistMrdSalesWorkIds)) {
                distMrdSalesWorkMapper.deleteByIds(String.join(",", delDistMrdSalesWorkIds));
                Example distMrdSalesWorkExampleIp = new Example(DistMrdSalesWorkIp.class);
                Example.Criteria criteria = distMrdSalesWorkExampleIp.createCriteria();
                criteria.andIn("salesWorkId", delDistMrdSalesWorkIds);
                distMrdSalesWorkIpMapper.deleteByExample(distMrdSalesWorkExampleIp);//删除
            }
            BigDecimal totalWorkRoy = distMrdSalesWorkMapper.sumWorkRole(mrdSalesDetails.getId());
            DistMrdSalesDetails distMrdSalesDetails = new DistMrdSalesDetails();
            distMrdSalesDetails.setId(mrdSalesDetails.getId());
            distMrdSalesDetails.setTotalWorkRoy(totalWorkRoy);
            if (totalWorkRoy != null) {
                distMrdSalesDetailsMapper.updateByPrimaryKeySelective(distMrdSalesDetails);
            }
            returnDistMrdSalesDetailsVo.setTotalWorkRoy(totalWorkRoy);
            returnDistMrdSalesDetailsVo.setDistMrdSalesWorkVoList(new ArrayList<>());
            return returnDistMrdSalesDetailsVo;
        }
        List<DistMrdSalesWorkVo> newDistMrdSalesWorkVoList = distMrdSalesWorkVoList.stream().
                map(distMrdSalesWorkVo -> {
                    DistMrdSalesWork newDistMrdSalesWor = distMrdSalesWorkVo.getDistMrdSalesWork();
                    if (newDistMrdSalesWor.getWorkSeqNo() == null) {
                        throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "seq"));
                    }
                    if (StringUtils.isBlank(newDistMrdSalesWor.getWorkTitle())) {
                        throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "WorkTitle"));
                    }
                    if (newDistMrdSalesWor.getWorkSocietyCode() == null) {
                        throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "WorkSocietyCode"));
                    }
                    if (StringUtils.isBlank(newDistMrdSalesWor.getGenreCode())) {
                        throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "Genre"));
                    }
                    if (StringUtils.isBlank(newDistMrdSalesWor.getIpShare())) {
                        throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "Share"));
                    }
                    if (null == newDistMrdSalesWor.getGrossWorkRoy()) {
                        throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "WorkRoy"));
                    }
                    if (newDistMrdSalesWor.getWorkId() == null) {
                        throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(), String.format(ResultCode.PARAMTER_IS_NULL.getMsg(), "WorkId"));
                    }
                    newDistMrdSalesWor.setWorkUniqueKey(StringUtils.leftPad(newDistMrdSalesWor.getWorkSocietyCode().toString(), 3, "0").concat("-").concat(newDistMrdSalesWor.getWorkId().toString()));
                    newDistMrdSalesWor.init();//初始化
                    newDistMrdSalesWor.setSalesDetailId(mrdSalesDetails.getId());
                    return distMrdSalesWorkVo;
                }).collect(Collectors.toList());
        List<DistMrdSalesWorkVo> updateDistMrdSalesWorkVoList = newDistMrdSalesWorkVoList.stream().filter(newDistMrdSalesWorkVo -> newDistMrdSalesWorkVo.getDistMrdSalesWork().getId() != null).collect(Collectors.toList());
        List<Long> updateDistMrdSalesWorkIds = updateDistMrdSalesWorkVoList.stream().map(distMrdSalesWorkVo -> distMrdSalesWorkVo.getDistMrdSalesWork().getId()).collect(Collectors.toList());
        List<DistMrdSalesWorkVo> insertDistMrdSalesWorkVoList = newDistMrdSalesWorkVoList.stream().filter(newDistMrdSalesWorkVo -> newDistMrdSalesWorkVo.getDistMrdSalesWork().getId() == null).collect(Collectors.toList());
        DistMrdSalesWork exmapleDistMrdSalesWork = new DistMrdSalesWork();
        exmapleDistMrdSalesWork.setSalesDetailId(mrdSalesDetails.getId());
        List<DistMrdSalesWork> oldDistMrdSalesWorkList = distMrdSalesWorkMapper.select(exmapleDistMrdSalesWork);
        List<String> deleteDistMrdSalesWorkIds = oldDistMrdSalesWorkList.stream().
                filter(oldDistMrdSalesWork -> !updateDistMrdSalesWorkIds.contains(oldDistMrdSalesWork.getId())).
                map(oldDistMrdSalesWork -> oldDistMrdSalesWork.getId().toString()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteDistMrdSalesWorkIds)) {
            distMrdSalesWorkMapper.deleteByIds(String.join(",", deleteDistMrdSalesWorkIds));//删除
            Example distMrdSalesWorkIpExample = new Example(DistMrdSalesWorkIp.class);
            Example.Criteria criteria = distMrdSalesWorkIpExample.createCriteria();
            criteria.andIn("salesWorkId", deleteDistMrdSalesWorkIds);
            distMrdSalesWorkIpMapper.deleteByExample(distMrdSalesWorkIpExample);//删除
        }
        List<DistMrdSalesWorkVo> returnDistMrdSalesWorkVoList = new ArrayList<>();
        insertDistMrdSalesWorkVoList.forEach(insertDistMrdSalesWorkVo -> {//新增
            DistMrdSalesWork distMrdSalesWork = insertDistMrdSalesWorkVo.getDistMrdSalesWork();
            distMrdSalesWorkMapper.insertUseGeneratedKeys(distMrdSalesWork);
            List<DistMrdSalesWorkIp> distMrdSalesWorkIpList = insertDistMrdSalesWorkVo.getDistMrdSalesWorkIpList();
            distMrdSalesWorkIpList.forEach(distMrdSalesWorkIp -> {
                distMrdSalesWorkIp.setSalesWorkId(distMrdSalesWork.getId());
                distMrdSalesWorkIp.init();
            });
            if (!CollectionUtils.isEmpty(distMrdSalesWorkIpList)) {
                distMrdSalesWorkIpMapper.insertList(distMrdSalesWorkIpList);
                insertDistMrdSalesWorkVo.setDistMrdSalesWorkIpList(distMrdSalesWorkIpList);
            }
            returnDistMrdSalesWorkVoList.add(insertDistMrdSalesWorkVo);
        });
        updateDistMrdSalesWorkVoList.forEach(updateDistMrdSalesWorkVo -> {//更新
            DistMrdSalesWork distMrdSalesWork = updateDistMrdSalesWorkVo.getDistMrdSalesWork();
            distMrdSalesWorkMapper.updateByPrimaryKeySelective(distMrdSalesWork);
            List<DistMrdSalesWorkIp> distMrdSalesWorkIpList = updateDistMrdSalesWorkVo.getDistMrdSalesWorkIpList();
            DistMrdSalesWorkIp distMrdSalesWorkIp = new DistMrdSalesWorkIp();
            distMrdSalesWorkIp.setSalesWorkId(distMrdSalesWork.getId());
            List<DistMrdSalesWorkIp> oldDistMrdSalesWorkIpList = distMrdSalesWorkIpMapper.select(distMrdSalesWorkIp);
            List<Long> newList = distMrdSalesWorkIpList.stream().filter(newdistMrdSalesWorkIp -> newdistMrdSalesWorkIp.getId() != null).map(DistMrdSalesWorkIp::getId).collect(Collectors.toList());
            List<Long> oldList = oldDistMrdSalesWorkIpList.stream().map(DistMrdSalesWorkIp::getId).collect(Collectors.toList());
            List<String> deleteList = oldList.stream().filter(oldId -> !newList.contains(oldId)).map(oldId -> oldId.toString()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteList)) {
                distMrdSalesWorkIpMapper.deleteByIds(String.join(",", deleteList));
            }
            List<DistMrdSalesWorkIp> returnDistMrdSalesWorkIpList = new ArrayList<>();
            distMrdSalesWorkIpList.stream().filter(distMrdSalesWorkIp1 -> {
                boolean isTrue = true;
                if (distMrdSalesWorkIp1.getId() != null) {
                    isTrue = !deleteList.contains(distMrdSalesWorkIp1.getId().toString());
                }
                return isTrue;
            }).forEach(distMrdSalesWorkIp2 -> {
                if (distMrdSalesWorkIp2.getId() == null) {
                    distMrdSalesWorkIpMapper.insertUseGeneratedKeys(distMrdSalesWorkIp2);
                } else {
                    distMrdSalesWorkIpMapper.updateByPrimaryKeySelective(distMrdSalesWorkIp2);
                }
                returnDistMrdSalesWorkIpList.add(distMrdSalesWorkIp2);
            });
            updateDistMrdSalesWorkVo.setDistMrdSalesWorkIpList(returnDistMrdSalesWorkIpList);
            returnDistMrdSalesWorkVoList.add(updateDistMrdSalesWorkVo);
        });
        BigDecimal totalWorkRoy = distMrdSalesWorkMapper.sumWorkRole(mrdSalesDetails.getId());
        DistMrdSalesDetails distMrdSalesDetails = new DistMrdSalesDetails();
        distMrdSalesDetails.setId(mrdSalesDetails.getId());
        distMrdSalesDetails.setTotalWorkRoy(totalWorkRoy);
        if (totalWorkRoy != null) {
            distMrdSalesDetailsMapper.updateByPrimaryKeySelective(distMrdSalesDetails);
        }
        returnDistMrdSalesDetailsVo.setTotalWorkRoy(totalWorkRoy);
        returnDistMrdSalesDetailsVo.setDistMrdSalesWorkVoList(returnDistMrdSalesWorkVoList);
        return returnDistMrdSalesDetailsVo;
    }

    @Override
    public DistMrdSalesDetailsVo getDistMrdSalesDetailsById(Long id) {
        DistMrdSalesDetailsVo distMrdSalesDetailsVo = new DistMrdSalesDetailsVo();
        DistMrdSalesDetails distMrdSalesDetails = getDistMrdSalesDetails(id);
        DistMrdSalesWork exampleDistMrdSalesWork = new DistMrdSalesWork();
        exampleDistMrdSalesWork.setSalesDetailId(id);
        List<DistMrdSalesWork> distMrdSalesWorkList = distMrdSalesWorkMapper.select(exampleDistMrdSalesWork);
        List<DistMrdSalesWorkVo> distMrdSalesWorkVoList = new ArrayList<>();
        distMrdSalesWorkList.forEach(distMrdSalesWork -> {
            DistMrdSalesWorkVo distMrdSalesWorkVo = new DistMrdSalesWorkVo();
            distMrdSalesWorkVo.setDistMrdSalesWork(distMrdSalesWork);
            DistMrdSalesWorkIp exampleDistMrdSalesWorkIp = new DistMrdSalesWorkIp();
            exampleDistMrdSalesWorkIp.setSalesWorkId(distMrdSalesWork.getId());
            List<DistMrdSalesWorkIp> distMrdSalesWorkIpList = distMrdSalesWorkIpMapper.select(exampleDistMrdSalesWorkIp);
            distMrdSalesWorkVo.setDistMrdSalesWorkIpList(distMrdSalesWorkIpList);
            distMrdSalesWorkVoList.add(distMrdSalesWorkVo);
        });
        distMrdSalesDetailsVo.setDistMrdSalesDetails(distMrdSalesDetails);
        distMrdSalesDetailsVo.setDistMrdSalesWorkVoList(distMrdSalesWorkVoList);
        BigDecimal sumOfWorkRole = distMrdSalesWorkMapper.sumWorkRole(id);
        distMrdSalesDetailsVo.setTotalWorkRoy(sumOfWorkRole);
        return distMrdSalesDetailsVo;
    }

    private DistMrdSalesDetails getDistMrdSalesDetails(Long id) {
        DistMrdSalesDetails distMrdSalesDetails = distMrdSalesDetailsMapper.selectByPrimaryKey(id);
        if (distMrdSalesDetails.getScId() != null) {
            DistMrdSc distMrdSc = distMrdScMapper.selectByPrimaryKey(distMrdSalesDetails.getScId());
            if (Objects.nonNull(distMrdSc)) {
                distMrdSalesDetails.setScNo(distMrdSc.getScNo());
                distMrdSalesDetails.setScTitleCh(distMrdSc.getScTitleCh());
                distMrdSalesDetails.setScTitleEn(distMrdSc.getScTitleEn());
                distMrdSalesDetails.setTypeCode(distMrdSc.getScTypeCode());
                distMrdSalesDetails.setCountryCode(distMrdSc.getCountryCode());
            }
        }
        return distMrdSalesDetails;
    }

    @Override
    public Integer delDistMrdSalesDetails(Long id) {
        Integer count = distMrdSalesDetailsMapper.deleteByIds(id.toString());
        DistMrdSalesWork tem = new DistMrdSalesWork();
        tem.setSalesDetailId(id);
        List<DistMrdSalesWork> distMrdSalesWorkList = distMrdSalesWorkMapper.select(tem);
        List<Long> distMrdSalesWorkIds = distMrdSalesWorkList.stream().map(DistMrdSalesWork::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(distMrdSalesWorkIds)) {
            distMrdSalesWorkMapper.deleteByIds(StringUtils.join(distMrdSalesWorkIds, ","));
            Example delTem = new Example(DistMrdSalesWorkIp.class);
            Example.Criteria criteria = delTem.createCriteria();
            criteria.andIn("salesWorkId", distMrdSalesWorkIds);
            distMrdSalesWorkIpMapper.deleteByExample(delTem);
        }
        return count;
    }
}