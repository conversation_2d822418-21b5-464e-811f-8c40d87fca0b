package tw.org.must.must.core.service.distmrd.impl;

import com.github.pagehelper.PageHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.util.ExampleUtil;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.core.service.ref.RefCountryService;
import tw.org.must.must.core.shiro.LoginUtil;
import tw.org.must.must.mapper.dist.DistParamNumberMapper;
import tw.org.must.must.mapper.distmrd.DistMrdProducerMapper;
import tw.org.must.must.mapper.distmrd.DistMrdSalesDetailsMapper;
import tw.org.must.must.mapper.distmrd.DistMrdScTypeMapper;
import tw.org.must.must.mapper.ref.RefCountryMapper;
import tw.org.must.must.model.dist.DistParamNumber;
import tw.org.must.must.model.distmrd.DistMrdProducer;
import tw.org.must.must.model.distmrd.DistMrdSales;
import tw.org.must.must.mapper.distmrd.DistMrdSalesMapper;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.distmrd.DistMrdSalesService;
import tw.org.must.must.model.distmrd.DistMrdSalesDetails;
import tw.org.must.must.model.distmrd.DistMrdScType;
import tw.org.must.must.model.distmrd.vo.DistMrdSalesVo;
import tw.org.must.must.model.ref.RefCountry;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

@Service
public class DistMrdSalesServiceImpl extends BaseServiceImpl<DistMrdSales> implements DistMrdSalesService {

	private final DistMrdSalesMapper distMrdSalesMapper;
	@Autowired
	private DistMrdSalesDetailsMapper distMrdSalesDetailsMapper;

	@Autowired
	private DistParamNumberMapper distParamNumberMapper;
	@Autowired
	private DistMrdProducerMapper distMrdProducerMapper;
	@Autowired
	private RefCountryMapper refCountryMapper;
	@Autowired
	private DistMrdScTypeMapper distMrdScTypeMapper;

    @Autowired
    public DistMrdSalesServiceImpl(DistMrdSalesMapper distMrdSalesMapper) {
        super(distMrdSalesMapper);
        this.distMrdSalesMapper = distMrdSalesMapper;
    }

    @Override
    public List<DistMrdSales> listDistMrdSalesWithPage(Integer pageNum, Integer pageSize, String distNo, String producerCode,String producerName) {
        PageHelper.startPage(pageNum,pageSize);
        Example example = new Example(DistMrdSales.class);
        Example.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotBlank(distNo)){
            criteria.andLike("distNo", ExampleUtil.exampleLikeAll(distNo));
        }
        if(StringUtils.isNotBlank(producerCode)){
            criteria.andEqualTo("producerCode",producerCode);
        }
        if(StringUtils.isNotBlank(producerName)){
            criteria.andLike("producerName",producerName);
        }
        example.orderBy("createTime").desc();
        return distMrdSalesMapper.selectByExample(example);
    }

    @Override
    public DistMrdSales saveDistMrdSales(DistMrdSales distMrdSales) {
        if(distMrdSales.getProducerId() ==null){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),String.format(ResultCode.PARAMTER_IS_NULL.getMsg(),"ProducerCode"));
        } if(StringUtils.isBlank(distMrdSales.getProducerName())){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),String.format(ResultCode.PARAMTER_IS_NULL.getMsg(),"ProducerName"));
        } if(StringUtils.isBlank(distMrdSales.getLocalEquivlent())){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),String.format(ResultCode.PARAMTER_IS_NULL.getMsg(),"LocalEquivlent"));
        } if(null == distMrdSales.getExchangeRate()){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),String.format(ResultCode.PARAMTER_IS_NULL.getMsg(),"ExchangeRate"));
        } if(StringUtils.isBlank(distMrdSales.getCurrency())){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),String.format(ResultCode.PARAMTER_IS_NULL.getMsg(),"Currency"));
        } if(StringUtils.isBlank(distMrdSales.getCurrencyName())){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),String.format(ResultCode.PARAMTER_IS_NULL.getMsg(),"CurrencyName"));
        } if(distMrdSales.getBatchNo() == null){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),String.format(ResultCode.PARAMTER_IS_NULL.getMsg(),"BatchNo"));
        } else if( !Pattern.compile("[1-9][0-9]{0,2}").matcher(distMrdSales.getBatchNo().toString()).matches()){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),"BatchNo：請輸入1-999的數值！");
        } if(StringUtils.isBlank(distMrdSales.getDistNo())){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),String.format(ResultCode.PARAMTER_IS_NULL.getMsg(),"DistNo"));
        }



        //distNo
        DistParamNumber distParamNumber = new DistParamNumber();
        distParamNumber.setDistNo(distMrdSales.getDistNo());
        if(distParamNumberMapper.selectCount(distParamNumber) == 0){
            throw new MustException(ResultCode.NOT_EXIST.getCode(),String.format(ResultCode.NOT_EXIST.getMsg(),"DistNo"));
        }
        //ProducerCode
        DistMrdProducer distMrdProducer = new DistMrdProducer();
        distMrdProducer.setId(distMrdSales.getProducerId());
        distMrdProducer.setProducerName(distMrdSales.getProducerName());
        if(distMrdProducerMapper.selectCount(distMrdProducer) == 0){
            throw new MustException(ResultCode.NOT_EXIST.getCode(),String.format(ResultCode.NOT_EXIST.getMsg(),"ProducerCode,ProducerName"));
        }
        //country
        RefCountry refCountry = new RefCountry();
//        refCountry.setCountryCode(distMrdSales.getCountryCode());//其他表中的countryCode对应refCountry表的tisn字段
        if(refCountryMapper.selectCount(refCountry) == 0){
            throw new MustException(ResultCode.NOT_EXIST.getCode(),String.format(ResultCode.NOT_EXIST.getMsg(),"Country"));
        }
        //FIXME currency字段未校验
        //typeCode
        DistMrdScType distMrdScType = new DistMrdScType();
//        distMrdScType.setTypeCode(distMrdSales.getTypeCode());
        if(distMrdScTypeMapper.selectCount(distMrdScType) == 0){
            throw new MustException(ResultCode.NOT_EXIST.getCode(),String.format(ResultCode.NOT_EXIST.getMsg(),"sc Type"));
        }

        distMrdSales.setAmendUser(LoginUtil.getUserName());
        distMrdSales.init();
        if(distMrdSales.getAmount() == null){
            distMrdSales.setAmount(new BigDecimal(0));
        }
        //*sc_total：amount）
        if(distMrdSales.getScTotal() == null){
            distMrdSales.setScTotal(distMrdSales.getAmount());
        }
        if(distMrdSales.getId() == null){
            distMrdSales.setCreateUser(LoginUtil.getUserName());
            distMrdSalesMapper.insertUseGeneratedKeys(distMrdSales);
            return distMrdSales;
        }
        distMrdSalesMapper.updateByPrimaryKeySelective(distMrdSales);
        return distMrdSales;
    }

    @Override
    public DistMrdSalesVo getDetailOfDistMrdSales(Long id) {
        DistMrdSalesVo distMrdSalesVo = new DistMrdSalesVo();
        DistMrdSales distMrdSales = distMrdSalesMapper.selectByPrimaryKey(id);
        DistMrdSalesDetails distMrdSalesDetails = new DistMrdSalesDetails();
        distMrdSalesDetails.setSalesId(id);
        List<DistMrdSalesDetails> distMrdSalesDetailsList = distMrdSalesDetailsMapper.select(distMrdSalesDetails);
        if (CollectionUtils.isNotEmpty(distMrdSalesDetailsList)){
            distMrdSales.setSumScBatchTotal(distMrdSalesDetailsList.stream().map(DistMrdSalesDetails::getTotalWorkRoy).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add));
        }
        distMrdSalesVo.setDistMrdSales(distMrdSales);
        distMrdSalesVo.setDistMrdSalesDetailsList(distMrdSalesDetailsList);
        return distMrdSalesVo;
    }

    @Override
    public List<DistMrdSales> getByDistNo(String distNo) {
        Example example = new Example(DistMrdSales.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("distNo", distNo);
        return distMrdSalesMapper.selectByExample(example);
    }
}