package tw.org.must.must.core.service.distmrd.impl;

import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.util.ExampleUtil;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.model.distmrd.DistMrdScType;
import tw.org.must.must.mapper.distmrd.DistMrdScTypeMapper;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.distmrd.DistMrdScTypeService;

import java.util.List;

@Service
public class DistMrdScTypeServiceImpl extends BaseServiceImpl<DistMrdScType> implements DistMrdScTypeService {

	private final DistMrdScTypeMapper distMrdScTypeMapper;

    @Autowired
    public DistMrdScTypeServiceImpl(DistMrdScTypeMapper distMrdScTypeMapper) {
        super(distMrdScTypeMapper);
        this.distMrdScTypeMapper = distMrdScTypeMapper;
    }

    @Override
    public List<DistMrdScType> listDistMrdScTypeWithPage(Integer pageNum, Integer pageSize, String typeCode,String typeName) {
        PageHelper.startPage(pageNum,pageSize);
        Example example = new Example(DistMrdScType.class);
        Example.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotBlank(typeCode)){
            criteria.andEqualTo("typeCode",typeCode);
        }
        if(StringUtils.isNotBlank(typeName)){
            criteria.andEqualTo("typeName", ExampleUtil.exampleLikeAll(typeCode));
        }
        example.orderBy("createTime").desc();
        return distMrdScTypeMapper.selectByExample(example);
    }

    @Override
    public DistMrdScType saveDistMrdScType(DistMrdScType distMrdScType) {
        if(StringUtils.isBlank(distMrdScType.getTypeCode())){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),String.format(ResultCode.PARAMTER_IS_NULL.getMsg(),"TypeCode"));
        } if(StringUtils.isBlank(distMrdScType.getTypeName())){
            throw new MustException(ResultCode.PARAMTER_IS_NULL.getCode(),String.format(ResultCode.PARAMTER_IS_NULL.getMsg(),"TypeName"));
        }
        distMrdScType.init();
        try{
            if(distMrdScType.getId() == null){
                distMrdScTypeMapper.insertUseGeneratedKeys(distMrdScType);
            }else {
                distMrdScTypeMapper.updateByPrimaryKeySelective(distMrdScType);
            }

        }catch (DuplicateKeyException e){
            e.printStackTrace();
            throw new MustException(ResultCode.DATASOURC_REPORT);
        }
        return distMrdScType;
    }
}