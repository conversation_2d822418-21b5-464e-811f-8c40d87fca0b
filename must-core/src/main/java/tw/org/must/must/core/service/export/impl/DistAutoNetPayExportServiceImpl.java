package tw.org.must.must.core.service.export.impl;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.core.service.dist.*;
import tw.org.must.must.core.service.export.DistAutoNetPayExportService;
import tw.org.must.must.core.service.mbr.MbrMemberBankAccService;
import tw.org.must.must.core.service.mbr.MbrMemberInfoService;
import tw.org.must.must.core.service.ref.RefSocietyRightService;
import tw.org.must.must.core.service.ref.RefSocietyService;
import tw.org.must.must.model.dist.*;
import tw.org.must.must.model.mbr.MbrMemberBankAcc;
import tw.org.must.must.model.mbr.MbrMemberInfo;
import tw.org.must.must.model.ref.RefSociety;
import tw.org.must.must.model.ref.RefSocietyRight;
import tw.org.must.must.model.report.*;
import tw.org.must.must.model.report.distAutoPay.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.text.DecimalFormat;

@Service
public class DistAutoNetPayExportServiceImpl implements DistAutoNetPayExportService {

//    private Logger logger = LoggerFactory.getLogger(DistAutoNetPayExportServiceImpl.class) ;

    @Autowired
    private DistAutopayNetPayMemberService memberService;

    @Autowired
    private DistAutopayNetPaySocietyService distAutopayNetPaySocietyService;

    @Autowired
    private RefSocietyRightService refSocietyRightService;

    @Autowired
    private DistAutopayMemberRetainService retainService;

    @Autowired
    private MbrMemberInfoService mbrMemberInfoService;

    @Autowired
    private MbrMemberBankAccService mbrMemberBankAccService;

    @Autowired
    private RefSocietyService refSocietyService;

    /**
     * 格式化货币值
     * @param amount 金额
     * @param currencySymbol 货币符号
     * @return 格式化后的货币字符串
     */
    private String formatCurrency(BigDecimal amount, String currencySymbol) {
        if (amount == null) {
            return currencySymbol + "0.00";
        }

        DecimalFormat df = new DecimalFormat("#,##0.00");
        boolean isNegative = amount.compareTo(BigDecimal.ZERO) < 0;
        String formattedAmount = df.format(amount.abs());

        return isNegative ? "-" + currencySymbol + formattedAmount : currencySymbol + formattedAmount;
    }

    @Autowired
    private DistAutopayBaseService distAutopayBaseService;

    @Autowired
    private DistNameSplitTransferService distNameSplitTransferService ;

    //780
    @Override
    public List<DistAutoPayOverseasReceipt> exportOverseasReceipt(String autopayNo, Boolean bankInfo,
                                                                  Integer societyCode, String distNo) {
        List<DistAutoPayOverseasReceipt> receipts = new ArrayList<>();
        List<DistAutopayNetPaySociety> societies = distAutopayNetPaySocietyService.getSocietyByExample(autopayNo, societyCode, distNo);
        if (CollectionUtils.isEmpty(societies)) {
            throw new MustException(ResultCode.Unknown_Exception.getCode(), "無可導出數據!");
        }

        Map<Integer, List<DistAutopayNetPaySociety>> societyMap = societies.stream().
                collect(Collectors.groupingBy(DistAutopayNetPaySociety::getSocietyCode));

        societyMap.keySet().forEach(key -> {
            DistAutoPayOverseasReceipt receipt = new DistAutoPayOverseasReceipt();
            receipt = parseSocieties(receipt, societyMap.get(key), bankInfo, 1);
            //代管
            List<RefSocietyRight> refSocietyRights = refSocietyRightService.getSocietyRightByAffiliatedSocCode(key);
            List<Integer> codes = refSocietyRights.stream().map(RefSocietyRight::getSocietyCode).collect(Collectors.toList());
            List<DistAutopayNetPaySociety> affiliatedSocieties = distAutopayNetPaySocietyService.listDistAutopayNetPaySocietyForaffiliated(codes);
            if (CollectionUtils.isNotEmpty(affiliatedSocieties)) {
                receipt = parseSocieties(receipt, affiliatedSocieties, bankInfo, 2);
            }
            receipts.add(receipt);
        });


        return receipts;
    }

    @Override
    public List<SalesTaxReport> exportSaleTaxReport(List<String> paNameNoList) {
        List<SalesTaxReport> list = new ArrayList<>();
        List<DistAutopayMemberRetain> retains = retainService.selectByPaNameNoList(paNameNoList);
        if (CollectionUtils.isEmpty(retains)) {
            return new ArrayList<>();
        }
        Map<String, List<DistAutopayMemberRetain>> societyMap = retains.stream().
                collect(Collectors.groupingBy(DistAutopayMemberRetain::getIpBaseNo));

        societyMap.keySet().forEach(key -> {
            list.add(parseRetain(societyMap.get(key)));
        });
        for (SalesTaxReport report : list) {
            String ipBaseNo = report.getIpBaseNo();

            // 根据ipBaseNo从mbr_member_info表中获取br_no
            MbrMemberInfo memberInfo = getMemberInfo(ipBaseNo);
            if (memberInfo != null && StringUtils.isNotBlank(memberInfo.getBrNo())) {
                report.setBrNo(memberInfo.getBrNo());
            } else {
                report.setBrNo(""); // 如果没有找到或br_no为空，设置为空字符串
            }
        }
        return list;
    }

    //880 750
    @Override
    public List<DistPayList> exportPayList(String autopayNo) {
        List<DistPayList> list = new ArrayList<>();
        List<DistAutopayNetPayMember> members = memberService.selectByAutoPay(autopayNo, null, null, null);
        if (CollectionUtils.isEmpty(members)) {
            return new ArrayList<>();
        }
        Map<String, List<DistAutopayNetPayMember>> memberMap = members.stream().
                collect(Collectors.groupingBy(DistAutopayNetPayMember::getAutopayNo));
        memberMap.keySet().forEach(key -> {
            list.add(parsePayList(memberMap.get(key)));
        });
        return list;
    }

    //880 750
    private DistPayList parsePayList(List<DistAutopayNetPayMember> members) {
        DistPayList distPayList = new DistPayList();

        List<DistPayDetails> details = parsePayListDetail(members);

        BigDecimal totalBalance = new BigDecimal(0);
        details.stream().forEach(detail -> {
            List<DistNoDetail> detailList = detail.getDetailList();
            BigDecimal sum = detailList.stream().map(DistNoDetail::getBalance).reduce(BigDecimal::add).get();
            totalBalance.add(sum);
        });
        DistAutopayNetPayMember member = members.get(0);
        DistAutoPay distAutoPay = new DistAutoPay();
        distAutoPay.setDetails(details);
        distAutoPay.setSummaryTotal(parseSummaryTotal(members));
        //750
        distPayList.setDate(DateParse.format(new Date(), "yyyyMMdd  HH:mm:ss"));
        distPayList.setAutoPayNo(member.getAutopayNo());
        distPayList.setDistribution("");
        distPayList.setReportId("");
        distPayList.setCount(String.valueOf(details.size()));
        distPayList.setBases(distAutoPay);
        distPayList.setTotalBalance(String.valueOf(totalBalance));
        return distPayList;
    }

    //880 750
    private List<DistPayDetails> parsePayListDetail(List<DistAutopayNetPayMember> members) {
        List<DistPayDetails> details = new ArrayList<>();
        Map<String, List<DistAutopayNetPayMember>> memberMap = members.stream().
                collect(Collectors.groupingBy(DistAutopayNetPayMember::getPaNameNo));
        memberMap.keySet().forEach(key -> {
            List<DistAutopayNetPayMember> memberList = memberMap.get(key);
            List<DistNoDetail> detailList = parseDetailList(memberList);
            DistAutopayNetPayMember member = memberList.get(0);
            DistPayDetails payDetails = new DistPayDetails();
            payDetails.setPaNameNo(member.getPaNameNo());
            payDetails.setPaName(member.getPaName());
            MbrMemberBankAcc bankAcc = getBankInfo(member.getIpBaseNo());
            payDetails.setBankName(bankAcc.getBankName());
            payDetails.setBankCode(String.valueOf(bankAcc.getBankNo()));
            payDetails.setBranchNo(String.valueOf(bankAcc.getBankBranchNo()));
            payDetails.setAccountNumber(member.getBankAccountNo());
            //880
            payDetails.setPayName(member.getPaName());
            payDetails.setPayMethod(member.getPaymentMethod());
            payDetails.setPayDescribe(bankAcc.getPaymentDesc());
            payDetails.setDetailList(detailList);
            details.add(payDetails);
        });
        return details;
    }

    //880 750
    private List<DistNoDetail> parseDetailList(List<DistAutopayNetPayMember> members) {
        List<DistNoDetail> details = new ArrayList<>();
        members.stream().forEach(member -> {
            DistNoDetail detail = new DistNoDetail();
            detail.setDistNo(member.getDistNo());
            detail.setBalance(member.getRoyaltyAmount().add(member.getAdjRoyaltyAmount()));
            detail.setBankAccountName(member.getBankAccountName());
        });
        return details;
    }

    //800协会 会员
    @Override
    public List<DistTranferList> exportTranferList(String autopayNo, Integer type) {
        List<DistTranferList> list = new ArrayList<>();
        if (type == 1) { //协会
            List<DistAutopayNetPaySociety> societies = distAutopayNetPaySocietyService.getSocietyByExample(autopayNo, null, null);
            if (CollectionUtils.isEmpty(societies)) {
                return new ArrayList<>();
            }
            Map<String, List<DistAutopayNetPaySociety>> societyMap = societies.stream().
                    collect(Collectors.groupingBy(DistAutopayNetPaySociety::getAutopayNo));
            societyMap.keySet().forEach(key -> {
                List<DistAutopayNetPaySociety> societyList = societyMap.get(key);
                list.add(parseTranferSocietyList(societyList));
            });
        } else if (type == 2) {//会员
            List<DistAutopayNetPayMember> members = memberService.selectByAutoPay(autopayNo, null, null, null);
            if (CollectionUtils.isEmpty(members)) {
                return new ArrayList<>();
            }
            Map<String, List<DistAutopayNetPayMember>> memberMap = members.stream().
                    collect(Collectors.groupingBy(DistAutopayNetPayMember::getAutopayNo));
            memberMap.keySet().forEach(key -> {
                list.add(parseTranferMemberList(memberMap.get(key)));
            });
        }
        return list;
    }

    //800协会
    private DistTranferList parseTranferSocietyList(List<DistAutopayNetPaySociety> societies) {
        DistTranferList distTranferList = new DistTranferList();
        List<DistTranferDetail> detailList = parseTranferSocietyDetail(societies);
        List<SummaryTotal> summaryTotals = parseSocietySummaryTotal(societies);
        BigDecimal total = detailList.stream().map(DistTranferDetail::getRoyalties).reduce(BigDecimal::add).get();
        BigDecimal taxAmount = new BigDecimal(0);
        BigDecimal commissionAmount = new BigDecimal(0);
        BigDecimal reciprocalAmount = new BigDecimal(0);
        detailList.stream().forEach(detail -> {
            List<DistTranferDetailList> distNoLists = detail.getDistNoLists();
            taxAmount.add(distNoLists.stream().map(DistTranferDetailList::getTaxAmount).reduce(BigDecimal::add).get());
            commissionAmount.add(distNoLists.stream().map(DistTranferDetailList::getAdmissionAmount).reduce(BigDecimal::add).get());
            reciprocalAmount.add(distNoLists.stream().map(DistTranferDetailList::getReciprocalAmount).reduce(BigDecimal::add).get());
        });
        DistAutopayNetPaySociety society = societies.get(0);
        distTranferList.setDate(DateParse.format(new Date(), "yyyyMMdd  HH:mm:ss"));
        distTranferList.setReportId("");
        distTranferList.setAutoPayNo(society.getAutopayNo());
        DistAutoPay distAutoPay = new DistAutoPay();
        distAutoPay.setDetailList(detailList);
        distAutoPay.setSummaryTotal(summaryTotals);

        distTranferList.setCount(detailList.size());
        distTranferList.setTotal(total);
        distTranferList.setPaymentTotal(total.subtract(taxAmount).subtract(commissionAmount).subtract(reciprocalAmount));
        distTranferList.setTaxAmount(taxAmount);
        distTranferList.setCommissionAmount(commissionAmount);
        distTranferList.setReciprocalAmount(reciprocalAmount);
        distTranferList.setBases(distAutoPay);
        return distTranferList;
    }

    //800协会
    private List<DistTranferDetail> parseTranferSocietyDetail(List<DistAutopayNetPaySociety> societies) {
        List<DistTranferDetail> details = new ArrayList<>();
        Map<Integer, List<DistAutopayNetPaySociety>> societyMap = societies.stream().
                collect(Collectors.groupingBy(DistAutopayNetPaySociety::getSocietyCode));
        societyMap.keySet().forEach(key -> {
            List<DistAutopayNetPaySociety> societyList = societyMap.get(key);
            List<DistTranferDetailList> distNoLists = parseTranferDistNoList(societyList);
            BigDecimal royalties = distNoLists.stream().map(DistTranferDetailList::getRoyalties).reduce(BigDecimal::add).get();
            BigDecimal payAbleAmount = distNoLists.stream().map(DistTranferDetailList::getPayAbleAmount).reduce(BigDecimal::add).get();
            DistAutopayNetPaySociety society = societyList.get(0);
            DistTranferDetail detail = new DistTranferDetail();
            detail.setPayMethod(society.getPaymentMethod());
            detail.setSocietyName(society.getSocietyName());
            detail.setRoyalties(royalties);
            detail.setPayAbleAmount(payAbleAmount);
            detail.setNetConvert("");
            detail.setDistNoLists(distNoLists);
            RefSociety refSociety = getRefSociety(society.getSocietyCode());
            detail.setBankAccounName(refSociety.getBankAccountName());
            detail.setBankAccountNo(refSociety.getBankAccountNo());
            detail.setBankName(refSociety.getBankName());
            detail.setCountryCode(refSociety.getCountryCode());
            detail.setDranOnCountryCode(refSociety.getDrawnOnCountryCode());
            detail.setBankAddress(refSociety.getBankAddress1());
            detail.setSwiftCode(refSociety.getBankSwiftCode());
            detail.setIbanCode(refSociety.getBankIbanCode());
            details.add(detail);
        });
        return details;
    }

    //800协会
    private List<DistTranferDetailList> parseTranferDistNoList(List<DistAutopayNetPaySociety> societyList) {
        List<DistTranferDetailList> distNoLists = new ArrayList<>();
        societyList.stream().forEach(society -> {
            DistTranferDetailList distNoList = new DistTranferDetailList();
            distNoList.setSocietyName(society.getSocietyName());
            distNoList.setDistNo(society.getDistNo());
//            distNoList.setPayAbleTo();
//            distNoList.setRoyalties();
//            distNoList.setPayAbleAmount();
            distNoList.setTaxRate(society.getTaxableRate());
            distNoList.setTaxAmount(society.getTaxableAmount());
            distNoList.setAdmissionRate(society.getCommissionRate());
            distNoList.setAdmissionAmount(society.getCommissionAmount());
            distNoList.setReciprocalRate(society.getReciprocalRate());
            distNoList.setReciprocalAmount(society.getReciprocalAmount());
            distNoLists.add(distNoList);
        });
        return distNoLists;
    }

    //800会员
    private DistTranferList parseTranferMemberList(List<DistAutopayNetPayMember> members) {
        DistTranferList distTranferList = new DistTranferList();
        DistAutopayNetPayMember member = members.get(0);
        List<DistTranferDetail> detailList = parseTranferMemberDetail(members);
        List<SummaryTotal> summaryTotals = parseSummaryTotal(members);
        BigDecimal total = detailList.stream().map(DistTranferDetail::getRoyalties).reduce(BigDecimal::add).get();
        BigDecimal taxAmount = new BigDecimal(0);
        BigDecimal commissionAmount = new BigDecimal(0);
        BigDecimal reciprocalAmount = new BigDecimal(0);
        detailList.stream().forEach(detail -> {
            List<DistTranferDetailList> distNoLists = detail.getDistNoLists();
            taxAmount.add(distNoLists.stream().map(DistTranferDetailList::getTaxAmount).reduce(BigDecimal::add).get());
            commissionAmount.add(distNoLists.stream().map(DistTranferDetailList::getAdmissionAmount).reduce(BigDecimal::add).get());
            reciprocalAmount.add(distNoLists.stream().map(DistTranferDetailList::getReciprocalAmount).reduce(BigDecimal::add).get());
        });
        distTranferList.setDate(DateParse.format(new Date(), "yyyyMMdd  HH:mm:ss"));
        distTranferList.setReportId("");
        distTranferList.setAutoPayNo(member.getAutopayNo());
        DistAutoPay distAutoPay = new DistAutoPay();
        distAutoPay.setDetailList(detailList);
        distAutoPay.setSummaryTotal(summaryTotals);
        distTranferList.setCount(detailList.size());
        distTranferList.setTotal(total);
        distTranferList.setPaymentTotal(total.subtract(taxAmount).subtract(commissionAmount).subtract(reciprocalAmount));
        distTranferList.setTaxAmount(taxAmount);
        distTranferList.setCommissionAmount(commissionAmount);
        distTranferList.setReciprocalAmount(reciprocalAmount);
        distTranferList.setBases(distAutoPay);
        return distTranferList;

    }

    //800会员
    private List<DistTranferDetail> parseTranferMemberDetail(List<DistAutopayNetPayMember> members) {
        List<DistTranferDetail> details = new ArrayList<>();
        Map<String, List<DistAutopayNetPayMember>> memberMap = members.stream().
                collect(Collectors.groupingBy(DistAutopayNetPayMember::getPaNameNo));
        memberMap.keySet().forEach(key -> {
            List<DistAutopayNetPayMember> memberList = memberMap.get(key);
            List<DistTranferDetailList> distNoLists = parseDistAutoPayDistNoList(memberList);
            BigDecimal royalties = distNoLists.stream().map(DistTranferDetailList::getRoyalties).reduce(BigDecimal::add).get();
            BigDecimal payAbleAmount = distNoLists.stream().map(DistTranferDetailList::getPayAbleAmount).reduce(BigDecimal::add).get();
            DistAutopayNetPayMember member = memberList.get(0);
            DistTranferDetail detail = new DistTranferDetail();
            detail.setPayMethod(member.getPaymentMethod());
            detail.setPaName(member.getPaName());
            detail.setPaNameNo(member.getPaNameNo());
            detail.setRoyalties(royalties);
            detail.setHandlingCharge(new BigDecimal(0));
            detail.setPayAbleAmount(payAbleAmount);
            detail.setNetConvert("");
            detail.setDistNoLists(distNoLists);
            MbrMemberBankAcc bankAcc = getBankInfo(member.getIpBaseNo());
            detail.setAccountName(bankAcc.getAccountName());
            detail.setPaymentDesc(bankAcc.getPaymentDesc());
            details.add(detail);
        });
        return details;
    }

    //800会员
    private List<DistTranferDetailList> parseDistAutoPayDistNoList(List<DistAutopayNetPayMember> members) {
        List<DistTranferDetailList> distNoLists = new ArrayList<>();
        members.stream().forEach(member -> {
            DistTranferDetailList distNoList = new DistTranferDetailList();
            distNoList.setDistNo(member.getDistNo());
//            distNoList.setRoyalties();
//            distNoList.setPayAbleAmount();
            distNoList.setAdmissionRate(member.getCommissionRate());
            distNoList.setAdmissionAmount(member.getCommissionAmount());
            distNoLists.add(distNoList);
        });
        return distNoLists;
    }

    //730支票 汇款
    @Override
    public List<DistDistributionAccount> exportDistribution(String autopayNo, List<String> list, String paNameNo, String distNo) {
        List<DistDistributionAccount> distDistributionAccounts = new ArrayList<>();
        List<DistAutopayNetPayMember> members = memberService.selectByAutoPay(autopayNo, paNameNo, distNo, list);
        if (CollectionUtils.isEmpty(members)) {
           throw new MustException(ResultCode.Unknown_Exception.getCode(), "無可導出數據!");
        }
        Map<String, List<DistAutopayNetPayMember>> memberMap = members.stream().
                collect(Collectors.groupingBy(DistAutopayNetPayMember::getAutopayNo));
        memberMap.keySet().forEach(key -> {
            distDistributionAccounts.add(parseDistribution(memberMap.get(key)));
        });
        return distDistributionAccounts;
    }


    @Override
    public List<DistDistributionAccount> exportDistribution(String autopayNo, List<String> list, List<String> paNameNoList, String distNo,Date payDate) {
        List<DistDistributionAccount> distDistributionAccounts = new ArrayList<>();
        List<DistAutopayNetPayMember> members = memberService.selectByAutoPay(autopayNo, paNameNoList, distNo, list,payDate);
        if (CollectionUtils.isEmpty(members)) {
            throw new MustException(ResultCode.Unknown_Exception.getCode(), "無可導出數據!");
        }
        Map<String, List<DistAutopayNetPayMember>> memberMap = members.stream().
                collect(Collectors.groupingBy(DistAutopayNetPayMember::getAutopayNo));
        memberMap.keySet().forEach(key -> {
            distDistributionAccounts.add(parseDistribution(memberMap.get(key)));
        });
        return distDistributionAccounts;
    }

    /**
     * 根据 paNameNo分档
     * @param autopayNo
     * @param list
     * @param paNameNoList
     * @param distNo
     * @param payDate
     * @return
     */
    @Override
    public Map<String,DistDistributionAccount> exportDistributionGroup(String autopayNo, List<String> list, List<String> paNameNoList, String distNo,Date payDate) {
        Map<String,DistDistributionAccount> distDistributionAccountMap = new HashMap<>();
        List<DistAutopayNetPayMember> members = memberService.selectByAutoPay(autopayNo, paNameNoList, distNo, list,payDate);
        if (CollectionUtils.isEmpty(members)) {
            throw new MustException(ResultCode.Unknown_Exception.getCode(), "無可導出數據!");
        }
        Map<String, List<DistAutopayNetPayMember>> memberMap = members.stream().
                collect(Collectors.groupingBy(m -> m.getPaNameNo() + "_" + m.getPaName()));
        memberMap.keySet().forEach(key -> {
            distDistributionAccountMap.put(key,parseDistribution(memberMap.get(key)));
        });
        return distDistributionAccountMap;
    }

    //730支票 汇款
    private DistDistributionAccount parseDistribution(List<DistAutopayNetPayMember> members) {
        DistDistributionAccount account = new DistDistributionAccount();
        List<DistPaBaseNoList> baseNoLists = parseDistirbutionDetail(members);
        List<SummaryTotal> summaryTotals = parseSummaryTotal(members);
        DistAutopayNetPayMember member = members.get(0);
        account.setDate(DateParse.format(new Date(), "yyyyMMdd  HH:mm"));
        account.setAutoPayNo(member.getAutopayNo());
        DistAutoPay distAutoPay = new DistAutoPay();
        distAutoPay.setSummaryTotal(summaryTotals);
        distAutoPay.setBaseNoLists(baseNoLists);
        account.setBases(distAutoPay);
        BigDecimal totalRoyalties = BigDecimal.ZERO,totalCommission = BigDecimal.ZERO,totalTax = BigDecimal.ZERO,netPayment = BigDecimal.ZERO;
        for(DistAutopayNetPayMember m : members){
            if(m.getRoyaltyAmount() != null && m.getRoyaltyAmount().compareTo(BigDecimal.ZERO) != 0){
                totalRoyalties = totalRoyalties.add(m.getRoyaltyAmount());
            }

            if(m.getCommissionAmount() != null && m.getCommissionAmount().compareTo(BigDecimal.ZERO) != 0){
                totalCommission = totalCommission.add(m.getCommissionAmount());
            }

            if(m.getTaxableAmount() != null && m.getTaxableAmount().compareTo(BigDecimal.ZERO) != 0){
                totalTax = totalTax.add(m.getTaxableAmount());
            }

            if(m.getPayAmount() != null && m.getPayAmount().compareTo(BigDecimal.ZERO) != 0){
                netPayment = netPayment.add(m.getPayAmount());
            }
        }
        account.setTotalCommission(totalCommission);
        account.setTotalTax(totalTax);
        account.setDistributionPayment(BigDecimal.ZERO);
        account.setNetPayment(netPayment);
        account.setTotalRoyalties(totalRoyalties);
        return account;
    }

    //730支票 汇款
    private List<DistPaBaseNoList> parseDistirbutionDetail(List<DistAutopayNetPayMember> members) {
        List<DistPaBaseNoList> baseNoLists = new ArrayList<>();
        Map<String, List<DistAutopayNetPayMember>> memberMap = members.stream().
                collect(Collectors.groupingBy(DistAutopayNetPayMember::getIpBaseNo));
        memberMap.keySet().forEach(key -> {
            //单个 ipBaseNo 对应的集合
            List<DistAutopayNetPayMember> memberList = memberMap.get(key);
            DistAutopayNetPayMember member = memberList.get(0);
            DistPaBaseNoList baseNoList = new DistPaBaseNoList();
            baseNoList.setMemberName(member.getPaName());
            baseNoList.setIpBaseNo(member.getIpBaseNo());
            baseNoList.setPaNameNo(member.getPaNameNo());
            MbrMemberInfo memberInfo = getMemberInfo(member.getIpBaseNo());
            baseNoList.setBrNo(memberInfo.getBrNo());
            baseNoList.setBrName(memberInfo.getBrName());
            baseNoList.setDetailReceipt(parseDistributionDetails(memberList));
            baseNoLists.add(baseNoList);
        });
        return baseNoLists;
    }

    //730支票 汇款
    private List<DistAutoPayReceipt> parseDistributionDetails(List<DistAutopayNetPayMember> memberList) {
        List<DistAutoPayReceipt> receipts = new ArrayList<>();

        //非海外
        List<DistAutopayNetPayMember> payMembers = memberList.stream().filter(m -> !m.getDistNo().contains("O")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(payMembers)) {
            List<DistAutoPayReceiptDetail> details = parseDistributionDetailList(payMembers);
            memberList.stream().forEach(member -> {
                DistAutoPayReceipt receipt = new DistAutoPayReceipt();
//                String distNo = member.getDistNo();
                receipt.setRoyaltiesType("MUST ROYALTIES MUST 權利金");
                receipt.setDistAutoPayReceiptDetail(details);
                receipts.add(receipt);
            });
        }

        //海外
        List<DistAutopayNetPayMember> overseas = memberList.stream().filter(m -> m.getDistNo().contains("O")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(overseas)) {
            List<DistAutoPayReceiptDetail> details = parseDistributionDetailList(overseas);
            memberList.stream().forEach(member -> {
                DistAutoPayReceipt receipt = new DistAutoPayReceipt();
//                String distNo = member.getDistNo();
                receipt.setRoyaltiesType("OVERSEAS ROYALTIES 海外權利金");
                receipt.setDistAutoPayReceiptDetail(details);
                receipts.add(receipt);
            });
        }
        return receipts;
    }

    //730支票 汇款
    private List<DistAutoPayReceiptDetail> parseDistributionDetailList(List<DistAutopayNetPayMember> members) {
        List<DistAutoPayReceiptDetail> details = new ArrayList<>();
        members.stream().forEach(member -> {
            DistAutoPayReceiptDetail detail = new DistAutoPayReceiptDetail();
            detail.setDistNo(member.getDistNo());
            //根据distNo判断  TOTAL ROYALTIES 網上公傳權利金小計  或  TOTAL ROYALTIES 權利金小計
            detail.setRoyalDescribe("");
            BigDecimal royaltyAmount = member.getLocalTaxableIncome().add(member.getOverseasTaxableIncome())
                    .subtract(member.getCommissionAmount()).subtract(member.getTaxableAmount());
            detail.setRoyaltyAmount(royaltyAmount);
            detail.setDistAutoPayDetail(parseDistributionDistNo(member));
            details.add(detail);
        });
        return details;
    }

    private List<SummaryTotal> parseSummaryTotal(List<DistAutopayNetPayMember> members) {
        List<SummaryTotal> summaryTotals = new ArrayList<>();
        Map<String, List<DistAutopayNetPayMember>> memberMap = members.stream().
                collect(Collectors.groupingBy(DistAutopayNetPayMember::getDistNo));
        memberMap.keySet().forEach(key -> {
            List<DistAutopayNetPayMember> memberList = memberMap.get(key);
            BigDecimal royalAmount = memberList.stream().map(DistAutopayNetPayMember::getRoyaltyAmount).reduce(BigDecimal::add).get();
            BigDecimal adjRoyalAmount = memberList.stream().map(DistAutopayNetPayMember::getAdjRoyaltyAmount).reduce(BigDecimal::add).get();
            SummaryTotal summaryTotal = new SummaryTotal();
            summaryTotal.setDistNo(key);
            summaryTotal.setTotalAmount(royalAmount.add(adjRoyalAmount));
        });
        return summaryTotals;
    }

    private List<SummaryTotal> parseSocietySummaryTotal(List<DistAutopayNetPaySociety> societies) {
        List<SummaryTotal> summaryTotals = new ArrayList<>();
        Map<String, List<DistAutopayNetPaySociety>> societyMap = societies.stream().
                collect(Collectors.groupingBy(DistAutopayNetPaySociety::getDistNo));
        societyMap.keySet().forEach(key -> {
            List<DistAutopayNetPaySociety> societyList = societyMap.get(key);
            BigDecimal royalAmount = societyList.stream().map(DistAutopayNetPaySociety::getRoyaltyAmount).reduce(BigDecimal::add).get();
            SummaryTotal summaryTotal = new SummaryTotal();
            summaryTotal.setDistNo(key);
            summaryTotal.setTotalAmount(royalAmount);
        });
        return summaryTotals;
    }

    private SalesTaxReport parseRetain(List<DistAutopayMemberRetain> retains) {
        SalesTaxReport salesTaxReport = new SalesTaxReport();
        List<DistAutoPayReceiptDetail> details = parseDetail1(retains);

        BigDecimal totalRoyalties = new BigDecimal(0);
        BigDecimal totalSale = new BigDecimal(0);
        BigDecimal totalAmount = new BigDecimal(0);

        details.stream().forEach(detail -> {
            totalAmount.add(detail.getRoyaltyAmount());
            DistAutoPayDetail payDetail = detail.getDistAutoPayDetail();
            totalSale.add(payDetail.getSalesTaxAmount() == null ? BigDecimal.ZERO : payDetail.getSalesTaxAmount());
            totalRoyalties.add(payDetail.getRoyaltyAmount());
        });

        DistAutopayMemberRetain retain = retains.get(0);
        salesTaxReport.setDate(DateParse.format(new Date(), "yyyyMMdd HH:mm:ss"));
        salesTaxReport.setMemberName(retain.getPaName());
        salesTaxReport.setIpBaseNo(retain.getIpBaseNo());
        salesTaxReport.setPaNameNo(retain.getPaNameNo());
        String brNo = "" ;
        if(retain.getBankAccountNo() != null){
            brNo = retain.getBankAccountNo() + "/";
        }

        if(retain.getBankAccountName() != null){
            brNo = brNo + retain.getBankAccountName();
        }
        salesTaxReport.setBrNo(brNo);
        salesTaxReport.setPayCurrency("NT$ 台幣");

        DistAutoPay distAutoPay = new DistAutoPay();
        List<DistAutopayNetPaySociety> societies = new ArrayList<>();
        BeanUtils.copyProperties(retains, societies);
        List<DistAutoPayReceipt> payReceipts = parseReceipt(societies);
        distAutoPay.setDistAutoPayReceipts(payReceipts);
        salesTaxReport.setBases(distAutoPay);

        salesTaxReport.setTotalRoyalties(totalRoyalties);
        salesTaxReport.setTotalSale(totalSale);
        salesTaxReport.setTotalAmount(totalAmount);
        salesTaxReport.setDistAutoPayReceiptDetail(details);
        return salesTaxReport;
    }

    private List<DistAutoPayReceiptDetail> parseDetail1(List<DistAutopayMemberRetain> retains) {
        List<DistAutoPayReceiptDetail> details = new ArrayList<>();
        retains.stream().forEach(retain -> {
            DistAutoPayReceiptDetail detail = new DistAutoPayReceiptDetail();
            detail.setDistNo(retain.getDistNo());
            detail.setDistAutoPayDetail(parsePayDetail1(retain));
            BigDecimal royaltyAmount = retain.getRoyaltyAmount().add(retain.getSalesTaxAmount() == null ? BigDecimal.ZERO : retain.getSalesTaxAmount());
            detail.setRoyaltyAmount(royaltyAmount);
            details.add(detail);
        });
        return details;
    }

    private DistAutoPayDetail parsePayDetail1(DistAutopayMemberRetain retain) {
        DistAutoPayDetail payDetail = new DistAutoPayDetail();
        payDetail.setRoyaltyAmount(retain.getRoyaltyAmount());
        payDetail.setSalesTaxRate(retain.getSalesTaxRate());
        payDetail.setSalesTaxAmount(retain.getSalesTaxAmount());
        return payDetail;
    }

    private DistAutoPayOverseasReceipt parseSocieties(DistAutoPayOverseasReceipt receipt,
                                                      List<DistAutopayNetPaySociety> societies,
                                                      Boolean bankInfo,
                                                      Integer type) {

        List<DistAutoPayReceipt> receipts = new ArrayList<>();
        List<DistAutoPayReceiptDetail> details = parseDetail(societies);
        if (receipt.getBases() != null) {
            receipts = receipt.getBases().getDistAutoPayReceipts();
        }
        receipts.stream().forEach(re -> {
            details.addAll(re.getDistAutoPayReceiptDetail());
        });
        List<DistAutoPayReceipt> payReceipts = parseReceipt(societies);

        List<PrsAffiliated> affiliateds = parseAffiliated(societies, type);
        BigDecimal royaltal = new BigDecimal(0);
        affiliateds.stream().forEach(affiliated -> {
            royaltal.add(affiliated.getRoyalties());
        });

        BigDecimal adjTotalAmount = new BigDecimal(0);
        BigDecimal adjSendAmount = new BigDecimal(0);

        details.stream().forEach(detail -> {
            DistAutoPayDetail payDetail = detail.getDistAutoPayDetail();
            adjTotalAmount.add(payDetail.getPerformRoyalties() == null ? BigDecimal.ZERO : payDetail.getPerformRoyalties().
                    add(payDetail.getAllocation() == null ? BigDecimal.ZERO : payDetail.getAllocation()).add(payDetail.getRoyaltiesAdj() == null ? BigDecimal.ZERO : payDetail.getRoyaltiesAdj()));
            adjSendAmount.add(detail.getRoyaltyAmount());
        });
        DistAutopayNetPaySociety society = societies.get(0);
        BigDecimal bankCharge = bankInfo ? society.getBankChagres() : new BigDecimal(0);
        BigDecimal netPayMent = adjSendAmount.add(royaltal).subtract(bankCharge == null ? BigDecimal.ZERO : bankCharge);
        DistAutoPay base = new DistAutoPay();
        base.setDistAutoPayReceipts(payReceipts);
        base.setPrsAffiliated(affiliateds);
        receipt.setDate(DateParse.format(new Date(), "yyyyMMdd"));
        receipt.setSocietyName(society.getSocietyName());
        receipt.setSocietyCode(society.getSocietyCode());
        receipt.setPayCurrency("NT$ 台幣");
        receipt.setFeeInError(adjTotalAmount);
        receipt.setAdjTotalAmount(adjTotalAmount);
        receipt.setAdjSendAmount(adjSendAmount);
        receipt.setPaymentMethod(society.getPaymentMethod());
        receipt.setAffiliated(royaltal);
        receipt.setBankCharge(bankCharge);
        receipt.setNetPayment(netPayMent);
        receipt.setExchangeRate(society.getExchangeRate());
        if(society.getExchangeRate() == null || society.getExchangeRate().compareTo(BigDecimal.ZERO) == 0){
            receipt.setTotalPayment(netPayMent);
        } else {
            receipt.setTotalPayment(netPayMent.divide(society.getExchangeRate(),6,RoundingMode.HALF_UP));
        }
        receipt.setBases(base);
        return receipt;
    }

    private List<DistAutoPayReceipt> parseReceipt(List<DistAutopayNetPaySociety> societies) {
        List<DistAutoPayReceipt> receipts = new ArrayList<>();
        societies.stream().forEach(society -> {
            List<DistAutoPayReceiptDetail> detail;
            DistAutoPayReceipt receipt = new DistAutoPayReceipt();
            String distNo = society.getDistNo();
            if (distNo.contains("O")) {
                receipt.setRoyaltiesType("OVERSEAS ROYALTIES 海外權利金");
            } else {
                receipt.setRoyaltiesType("MUST ROYALTIES MUST 權利金");
            }
            List<DistAutopayNetPaySociety> list = new ArrayList<>();
            list.add(society);
            detail = parseDetail(list);

            receipt.setDistAutoPayReceiptDetail(detail);
            receipts.add(receipt);
        });
        return receipts;
    }

    private List<DistAutoPayReceiptDetail> parseDetail(List<DistAutopayNetPaySociety> societies) {
        List<DistAutoPayReceiptDetail> details = new ArrayList<>();
        societies.stream().forEach(society -> {
            DistAutoPayReceiptDetail detail = new DistAutoPayReceiptDetail();
            String distNo = society.getDistNo();
            detail.setDistNo(distNo);
            detail.setDistAutoPayDetail(parsePayDetail(society));
            BigDecimal royaltyAmount = getRoyaltyAmount(society);
            detail.setRoyaltyAmount(royaltyAmount);
            if (distNo.contains("I")) {
                detail.setRoyalDescribe("TOTAL ROYALTIES 網上公傳權利金小計");
            } else {
                detail.setRoyalDescribe("TOTAL ROYALTIES 權利金小計");
            }
            details.add(detail);
        });
        return details;
    }

    private BigDecimal getRoyaltyAmount(DistAutopayNetPaySociety society) {
        if (society.getLocalTaxableIncome() == null) {
            society.setLocalTaxableIncome(BigDecimal.ZERO);
        }
        if (society.getOverseasTaxableIncome() == null) {
            society.setOverseasTaxableIncome(BigDecimal.ZERO);
        }
        if (society.getCommissionAmount() == null) {
            society.setCommissionAmount(BigDecimal.ZERO);
        }
        if (society.getTaxableAmount() == null) {
            society.setTaxableAmount(BigDecimal.ZERO);
        }
        if (society.getReciprocalAmount() == null) {
            society.setReciprocalAmount(BigDecimal.ZERO);
        }
        BigDecimal royaltyAmount = society.getLocalTaxableIncome().add(society.getOverseasTaxableIncome())
                .subtract(society.getCommissionAmount()).subtract(society.getTaxableAmount()).subtract(society.getReciprocalAmount());
        return royaltyAmount;
    }

    private DistAutoPayDetail parsePayDetail(DistAutopayNetPaySociety society) {
        DistAutoPayDetail detail = new DistAutoPayDetail();
        String distNo = society.getDistNo();
        BigDecimal amount = society.getLocalTaxableIncome().add(society.getOverseasTaxableIncome());
        String year = "20" + distNo.substring(1, 3);
        detail.setYear(year);
        if (distNo.substring(3, 4).equals("9")) {
            detail.setPerformRoyalties(amount);
            detail.setMemberDescribe(year + "年會員保留款");
            detail.setMemberRetain(amount);
        } else {
            detail.setRoyaltiesAdj2(amount);
        }
        detail.setAllocation(society.getBasicAmount());
        detail.setRoyaltiesAdj(society.getAdjRoyaltyAmount());
        detail.setCommissionRate(society.getCommissionRate());
        detail.setCommissionAmount(society.getCommissionAmount());
        detail.setTaxableRate(society.getTaxableRate());
        detail.setTaxableAmount(society.getTaxableAmount());
        detail.setReciprocalRate(society.getReciprocalRate());
        detail.setReciprocalAmount(society.getReciprocalAmount());
        detail.setDeduction(society.getDeduction());
        if (distNo.contains("I")) {
            detail.setFirstDescribe(year + " PUBLIC TRANSMISSION ROYALTIES " + year + "年度網上公傳權利金");
        } else if (distNo.contains("P")) {
            detail.setFirstDescribe(year + " PERFORMING ROYALTIES " + year + "年度演奏權利金");
            detail.setDistributionDescribe("UNLOGGED PERFORMANCE ALLOCATION 基本分配額");
        } else if (distNo.contains("M")) {
            detail.setFirstDescribe(year + " MECHANICAL REPRODUCTION ROYALTIES " + year + "年度機械灌緣權利金");
        }
        return detail;
    }

    private DistAutoPayDetail parseDistributionDistNo(DistAutopayNetPayMember member) {
        DistAutoPayDetail detail = new DistAutoPayDetail();
        String distNo = member.getDistNo();
        BigDecimal amount = member.getLocalTaxableIncome().add(member.getOverseasTaxableIncome());
        String year = "20" + distNo.substring(1, 3);
        detail.setYear(year);
        if (distNo.substring(3, 4).equals("9")) {
            detail.setPerformRoyalties(amount);
            detail.setMemberDescribe(year + "年會員保留款");
            detail.setMemberRetain(amount);
        } else {
            detail.setRoyaltiesAdj2(amount);
        }
        detail.setAllocation(member.getBasicAmount());
        detail.setRoyaltiesAdj(member.getAdjRoyaltyAmount());//權利金調整
        detail.setCommissionRate(member.getCommissionRate());//管理费
        detail.setCommissionAmount(member.getCommissionAmount());
        detail.setTaxableRate(member.getTaxableRate());//所得税
        detail.setTaxableAmount(member.getTaxableAmount());
        detail.setAdditionalCommissionAmount(member.getAdjCommissionAmount());
        detail.setDeduction(member.getDeduction());
        if (distNo.contains("I")) {
            detail.setFirstDescribe(year + " PUBLIC TRANSMISSION ROYALTIES " + year + "年度網上公傳權利金");
            detail.setRoyalDescribe("TOTAL ROYALTIES " + "網上公傳權利金小計");
        } else if (distNo.contains("P")) {
            detail.setFirstDescribe(year + " PERFORMING ROYALTIES " + year + "年度公開演出權利金");
            detail.setDistributionDescribe("UNLOGGED PERFORMANCE ALLOCATION 基本分配額");
            detail.setRoyalDescribe("TOTAL ROYALTIES " + "公開演出權利金小計");
        } else if (distNo.contains("M")) {
            detail.setFirstDescribe(year + " MECHANICAL REPRODUCTION ROYALTIES " + year + "年度重製權權利金");
            detail.setRoyalDescribe("TOTAL ROYALTIES " + "重製權權利金小計");
        } else if(distNo.contains("O")){
            detail.setFirstDescribe(year + " PERFORMING ROYALTIES " + year + "年度海外聯會匯入款");
            detail.setDistributionDescribe("UNLOGGED PERFORMANCE ALLOCATION 基本分配額");
            detail.setRoyalDescribe("TOTAL ROYALTIES " + "海外聯會匯入款小計");
        }
        return detail;
    }

    public List<String> txtReport(String autopayNo){
        DistAutopayBase query = new DistAutopayBase();
        query.setAutopayNo(autopayNo);
        List<DistAutopayBase> distAutopayBaseList = distAutopayBaseService.list(query);
        List<String> result = new ArrayList<>();
        for(DistAutopayBase distAutopayBase : distAutopayBaseList){
            List<String> txtDetails = new ArrayList<>();
            Map<String,BigDecimal> map = new HashMap<>();
            int count = 0;
            String payType = distAutopayBase.getPayType();
            if(payType.contains("MBR")){
                List<DistAutopayNetPayMember> autopayNetPayMembers = memberService.selectByAutoPay(autopayNo,null,null,null);
                getTxtDetailForMember(autopayNetPayMembers,txtDetails, map);
                count = autopayNetPayMembers.size();
            }

            if(payType.contains("SOC")){
                List<DistAutopayNetPaySociety> autopayNetPaySocieties = distAutopayNetPaySocietyService.getSocietyByExample(autopayNo,null,null);
                getTxtDetailForSociety(autopayNetPaySocieties,txtDetails, map);
                count += autopayNetPaySocieties.size();
            }

            String txtHeader = getTxtHeader(distAutopayBase,map.get("amount"),map.get("fee"),count);
            result.add(txtHeader);
            result.addAll(txtDetails);
        }

        return result;

    }

    private List<PrsAffiliated> parseAffiliated(List<DistAutopayNetPaySociety> societies, Integer type) {
        List<PrsAffiliated> affiliateds = new ArrayList<>();
        if (type == 2) {
            societies.stream().forEach(society -> {
                PrsAffiliated affiliated = new PrsAffiliated();
                affiliated.setDistNo(society.getDistNo());
                affiliated.setSocieties(society.getSocietyName());
                affiliated.setRoyalties(society.getRoyaltyAmount());
                affiliated.setTax(society.getTaxableAmount());
                affiliated.setCommission(society.getCommissionAmount());
                affiliated.setNetRoyalties(society.getPayAmount());
                affiliateds.add(affiliated);
            });
        }
        return affiliateds;
    }

    //根据ipBaseNo获取银行信息
    private MbrMemberBankAcc getBankInfo(String ipBaseNo) {
        List<MbrMemberBankAcc> list = mbrMemberBankAccService.selectByIpNoAndStatus(ipBaseNo, 1);
        return list.get(0);
    }

    //根据协会编号到 ref_society查询
    private RefSociety getRefSociety(Integer societyCode) {
        return refSocietyService.getSocietyBySocietyCode(societyCode);
    }

    //根据ipBaseNo
    private MbrMemberInfo getMemberInfo(String ipBaseNo) {
        return mbrMemberInfoService.getByIpNo(ipBaseNo);
    }

    public String getTxtHeader(DistAutopayBase distAutopayBase,BigDecimal amount, BigDecimal fee, int count){
        StringBuffer txtHeader = new StringBuffer("1");
        txtHeader.append("2068").append(leftPad("**************",14));
//        DateParse.format(distAutopayBase.getAutopayDate(),DateParse.patternDate);
        txtHeader.append(DateParse.transferToTwDate(distAutopayBase.getAutopayDate()));
        txtHeader.append("820").append(leftPad(amount.toString(),13)).append(leftPad(count+"",7));
        txtHeader.append(leftPad("社團法人中華音樂著作權協會",30," ")).append(leftPad(fee+"",6));
        txtHeader.append(rightPad("",110));
        txtHeader.append("0\n");
        return txtHeader.toString();

    }


    public void getTxtDetailForMember(List<DistAutopayNetPayMember> distAutopayNetPayMemberList,List<String> txtDetails,Map<String,BigDecimal> amountAndFee){
        BigDecimal amountTotal = amountAndFee.get("amount");
        BigDecimal feeTotal = amountAndFee.get("fee");
        if(amountTotal == null){
            amountTotal = BigDecimal.ZERO;
        }
        if(feeTotal == null){
            feeTotal = BigDecimal.ZERO;
        }
        for(DistAutopayNetPayMember distAutopayNetPayMember : distAutopayNetPayMemberList){
            StringBuffer txtDetail = new StringBuffer();
            Long bankNo = distAutopayNetPayMember.getBankNo() == null ? 0L : distAutopayNetPayMember.getBankNo();
            if( bankNo > 10000){
                bankNo = bankNo/10;
            }

            txtDetail.append(leftPad(bankNo.toString(),6));
            BigDecimal amount = distAutopayNetPayMember.getPayAmount().multiply(new BigDecimal(100)).setScale(0);
            amountTotal = amountTotal.add(amount);
            BigDecimal fee = getPayFee(distAutopayNetPayMember.getPayAmount());
            feeTotal = feeTotal.add(fee);
            // 修改以下两行，为金额和手续费指定固定宽度
            txtDetail.append(leftPad(amount.toString(),11)); // 金额: 11位左填充
            txtDetail.append(leftPad(fee.toString(),5));     // 手续费: 5位左填充
            txtDetail.append(leftPad(distAutopayNetPayMember.getBankAccountNo(), 14, "0"));
            String name = distAutopayNetPayMember.getPaName();
            if(StringUtils.isNotEmpty(distAutopayNetPayMember.getPaNameNo())){
                DistNameSplitTransfer distNameSplitTransfer = distNameSplitTransferService.getByPaNameNo(distAutopayNetPayMember.getPaNameNo());
                if(distNameSplitTransfer != null){
                    name = distNameSplitTransfer.getSplitName();
                }

            }
            String newName = toSBC(name);
            txtDetail.append(rightPad(newName,76- newName.length()));
            txtDetail.append(rightPad(distAutopayNetPayMember.getAutopayNo(),40));
            txtDetail.append(rightPad(distAutopayNetPayMember.getPaNameNo(),9));
            txtDetail.append(rightPad("",33));
            txtDetail.append("0\n");
            txtDetails.add(txtDetail.toString());
        }

        amountAndFee.put("amount",amountTotal);
        amountAndFee.put("fee",feeTotal);
    }

    public void getTxtDetailForSociety(List<DistAutopayNetPaySociety> distAutopayNetPaySocietyList,List<String> txtDetails,Map<String,BigDecimal> amountAndFee){
        BigDecimal amountTotal = amountAndFee.get("amount");
        BigDecimal feeTotal = amountAndFee.get("fee");
        if(amountTotal == null){
            amountTotal = BigDecimal.ZERO;
        }
        if(feeTotal == null){
            feeTotal = BigDecimal.ZERO;
        }
        for(DistAutopayNetPaySociety distAutopayNetPaySociety : distAutopayNetPaySocietyList){
            if(distAutopayNetPaySociety.getBankNo() == null || StringUtils.isEmpty(distAutopayNetPaySociety.getBankAccountNo())){
                continue;
            }
            StringBuffer txtDetail = new StringBuffer();
            Long bankNo = distAutopayNetPaySociety.getBankNo();
            if(bankNo!= null && bankNo > 10000){
                bankNo = bankNo/10;
            }
            txtDetail.append(leftPad(bankNo.toString(),6));
            txtDetail.append(distAutopayNetPaySociety.getBankNo().toString());
            BigDecimal amount = distAutopayNetPaySociety.getPayAmount().multiply(new BigDecimal(100)).setScale(0);
            amountTotal = amountTotal.add(amount);
            BigDecimal fee = getPayFee(distAutopayNetPaySociety.getPayAmount());
            feeTotal = feeTotal.add(fee);
            txtDetail.append(distAutopayNetPaySociety.getPayAmount().setScale(0, RoundingMode.HALF_UP));
            txtDetail.append(fee);
            txtDetail.append(leftPad(distAutopayNetPaySociety.getBankAccountNo(), 14, "0"));
            txtDetail.append(rightPad(toSBC(distAutopayNetPaySociety.getSocietyName()),76));
            txtDetail.append(rightPad(distAutopayNetPaySociety.getAutopayNo(),40));
            txtDetail.append(rightPad(distAutopayNetPaySociety.getRemark(),9));
            txtDetail.append(rightPad("",33));
            txtDetail.append("0\n");
            txtDetails.add(txtDetail.toString());
        }

        amountAndFee.put("amount",amountTotal);
        amountAndFee.put("fee",feeTotal);
    }

    /**
     * 计算手续费
     * 匯款金額     單筆手續費    匯款金額   單筆手續費
     *   <=200萬       30元        <=1100萬     120元
     *   <=300萬       40          <=1200 萬     130
     *   <=400萬       50          <=1300 萬     140
     *   <=500萬       60          <=1400 萬     150
     *   <=600萬       70          <=1500 萬     160
     *   <=700萬       80          <=1600 萬     170
     *   <=800萬       90          <=1700 萬     180
     *   <=900萬      100          <=1800 萬     190
     *   <=1000萬     110          <=1900 萬     200
     *                             <=2000 萬     210
     * @param amount
     * @return
     */
    public BigDecimal getPayFee(BigDecimal amount){
        BigDecimal fee = new BigDecimal(30);
        BigDecimal stepAmount = new BigDecimal(1000000);
        BigDecimal stepFee = BigDecimal.TEN;
        BigDecimal min = new BigDecimal(2000000);
        BigDecimal max = new BigDecimal(20000000) ;

        if(amount.compareTo(max) == 1){
            throw new MustException("金额不能大于2000万") ;
        }

        while (min.compareTo(max) < 1 ){
            if(amount.compareTo(min) < 1){
                return fee;
            }
            min = min.add(stepAmount);
            fee = fee.add(stepFee);
        }

        return fee;
    }

    public String leftPad(String str, int length){
        if(str == null){
            str = "";
        }
        return StringUtils.leftPad(str,length,"0");
    }

    public String rightPad(String str, int length){
        if(str == null){
            str = "";
        }
        return StringUtils.rightPad(str,length," ");
    }

    public String leftPad(String str, int length,String padStr){
        if(str == null){
            str = "";
        }
        return StringUtils.leftPad(str,length,padStr);
    }

    public String rightPad(String str, int length,String padStr){
        if(str == null){
            str = "";
        }
        return StringUtils.rightPad(str,length,padStr);
    }

    /**
     * @param
     * @return java.lang.String
     * @desc 半角转全角
     */
    public static String toSBC(String val) {
        char chars[] = val.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if (chars[i] == ' ') {
                chars[i] = '\u3000';
            } else if (chars[i] < '\177') {
                chars[i] = (char) (chars[i] + 65248);
            }
        }
        return new String(chars);
    }

    /**
     * @param val
     * @return java.lang.String
     * @desc 全角转半角
     */
    public static String toDBC(String val) {
        char chars[] = val.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if (chars[i] == '\u3000') {
                chars[i] = ' ';
            } else if (chars[i] > '\uFF00' && chars[i] < '\uFF5F') {
                chars[i] = (char) (chars[i] - 65248);
            }
        }
        return new String(chars);
    }



}
