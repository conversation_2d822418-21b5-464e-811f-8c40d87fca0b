package tw.org.must.must.core.service.export.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tw.org.must.must.core.service.dist.DistAutopayNetPayMemberService;
import tw.org.must.must.core.service.export.ExportPdfService;
import tw.org.must.must.core.service.mbr.MbrMemberInfoService;
import tw.org.must.must.core.service.mbr.MbrMemberMembershipService;
import tw.org.must.must.model.dist.DistAutopayNetPayMember;
import tw.org.must.must.model.mbr.MbrMemberAddress;
import tw.org.must.must.model.mbr.MbrMemberContact;
import tw.org.must.must.model.mbr.MbrMemberInfo;
import tw.org.must.must.model.mbr.vo.MemberVO;
import tw.org.must.report.export.model.autoPay.AutoPayMemberPdf;
import tw.org.must.report.export.model.autoPay.MemberInfoSub;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ExportPdfServiceImpl implements ExportPdfService {

    @Autowired
    private DistAutopayNetPayMemberService distAutopayNetPayMemberService;
    @Autowired
    private MbrMemberInfoService mbrMemberInfoService;
    @Autowired
    private MbrMemberMembershipService mbrMemberMembershipService;

    @Override
    public AutoPayMemberPdf getAutoPayMemberPdf(String paymentNo, String memberNoA, String memberNoB, List<String> memberNo,
                                                List<String> memberShip, Integer nameOrder, Integer memberNoOrder, List<String> inputOrder) {
        AutoPayMemberPdf autoPayMemberPdf = new AutoPayMemberPdf();
        List<String> ipBaseNoList = new ArrayList<>();
        boolean checkMemberShip = false; //  查询条件有ipBaseNo，就先筛选下membership；没有就等查出 DistAutopayNetPayMember 数据后再筛选
        if (!CollectionUtils.isEmpty(memberNo)) {
            ipBaseNoList = mbrMemberInfoService.selectByMemberNoList(memberNo, memberShip).stream().map(MbrMemberInfo::getIpBaseNo).distinct().collect(Collectors.toList());
        } else if (StringUtils.isNotBlank(memberNoA) || StringUtils.isNotBlank(memberNoB)) {
            ipBaseNoList = mbrMemberInfoService.selectByBetween(memberNoA, memberNoB, memberShip).stream().map(MbrMemberInfo::getIpBaseNo).distinct().collect(Collectors.toList());
        }
        if(!ipBaseNoList.isEmpty() && !CollectionUtils.isEmpty(memberShip)) {
            checkMemberShip = true;
        }
        List<DistAutopayNetPayMember> distAutopayNetPayMemberList = distAutopayNetPayMemberService.getIpBaseNoList(paymentNo, ipBaseNoList);
        if (CollectionUtils.isEmpty(distAutopayNetPayMemberList)) {
            return autoPayMemberPdf;
        }
        if (!checkMemberShip) {
            List<String> ipBaseNos = distAutopayNetPayMemberList.stream().map(DistAutopayNetPayMember::getIpBaseNo).distinct().collect(Collectors.toList());
            ipBaseNos = mbrMemberMembershipService.filterMemeber(ipBaseNos, memberShip);
            List<String> finalIpBaseNos = ipBaseNos;
            distAutopayNetPayMemberList = distAutopayNetPayMemberList.stream().filter(it -> finalIpBaseNos.contains(it.getIpBaseNo())).collect(Collectors.toList());
        }
        if (distAutopayNetPayMemberList.isEmpty()) {
            return autoPayMemberPdf;
        }
        List<MemberInfoSub> memberInfoSubs = new ArrayList<>(distAutopayNetPayMemberList.size());
        List<MemberInfoSub> memberInfoSubs1 = new ArrayList<>(distAutopayNetPayMemberList.size());
        List<MemberInfoSub> memberInfoSubs2 = new ArrayList<>(distAutopayNetPayMemberList.size());
        for (DistAutopayNetPayMember payMember : distAutopayNetPayMemberList) {
            MemberVO memberVO = mbrMemberInfoService.getMemberVO(payMember.getIpBaseNo());
            if (null != memberVO) {
                MemberInfoSub sub = new MemberInfoSub();
                sub.setName(memberVO.getInfo().getMailName());
                sub.setMemberNo(memberVO.getInfo().getMemberNo());
                List<MbrMemberAddress> addresses = memberVO.getAddresses();
                if (!CollectionUtils.isEmpty(addresses)) {
                    MbrMemberAddress mbrMemberAddress = addresses.stream().filter(x -> StringUtils.equals(x.getType(), memberVO.getInfo().getCorrespondType())).findFirst().orElse(null);
                    if (null != mbrMemberAddress) {
                        sub.setZipCode(mbrMemberAddress.getZipCode());
                        sub.setArea(String.format("%s%S", mbrMemberAddress.getCounty(), mbrMemberAddress.getDistrict()));
                        sub.setAddress(String.format("%s%s%s%s%s%s%s", trim(mbrMemberAddress.getStreet()), trim(mbrMemberAddress.getSection()), trim(mbrMemberAddress.getLane()), trim(mbrMemberAddress.getAlley()),
                                trim(mbrMemberAddress.getNo()), trim(mbrMemberAddress.getFloor()), trim(mbrMemberAddress.getRoom())));
                    }
                }
                List<MbrMemberContact> contacts = memberVO.getContacts();
                if (!CollectionUtils.isEmpty(contacts)) {
                    MbrMemberContact contact = contacts.stream().filter(x -> StringUtils.equals(x.getType(), memberVO.getInfo().getCorrespondType())).findFirst().orElse(null);
                    if (null != contact) {
                        sub.setAttn(contact.getPerson());
                    }
                }
                memberInfoSubs.add(sub);

            }
        }
        // 排序
        if(Integer.valueOf(1).equals(nameOrder)) {
            memberInfoSubs = memberInfoSubs.stream().sorted(Comparator.comparing(MemberInfoSub::getName)).collect(Collectors.toList());
        }
        if(Integer.valueOf(1).equals(memberNoOrder)) {
            memberInfoSubs = memberInfoSubs.stream().sorted(Comparator.comparing(MemberInfoSub::getMemberNo)).collect(Collectors.toList());
        }
        if(!CollectionUtils.isEmpty(inputOrder)) {
            for (String order : inputOrder) {
                if(StringUtils.equalsIgnoreCase(order, "address")) {
                    memberInfoSubs = memberInfoSubs.stream().sorted(Comparator.comparing(it -> String.format("%s%s", it.getArea(), it.getAddress()))).collect(Collectors.toList());
                }else if(StringUtils.equalsIgnoreCase(order, "attn")) {
                    memberInfoSubs = memberInfoSubs.stream().sorted(Comparator.comparing(MemberInfoSub::getAttn)).collect(Collectors.toList());
                }
            }
        }
        for (int i = 0; i < memberInfoSubs.size(); i++) {
            if ((i & 1) == 1) {
                // 奇数
                memberInfoSubs2.add(memberInfoSubs.get(i));
            }else {
                memberInfoSubs1.add(memberInfoSubs.get(i));
            }
        }
        autoPayMemberPdf.setMember1(memberInfoSubs1);
        autoPayMemberPdf.setMember2(memberInfoSubs2);
        return autoPayMemberPdf;
    }

    private String trim(String str) {
        return StringUtils.isBlank(str) ? "" : str;
    }
}
