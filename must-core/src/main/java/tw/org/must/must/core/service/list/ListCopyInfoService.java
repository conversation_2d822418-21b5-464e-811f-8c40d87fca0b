package tw.org.must.must.core.service.list;


import java.util.List;

import tw.org.must.must.common.base.IdBaseService;
import tw.org.must.must.model.list.ListCopyFilter;
import tw.org.must.must.model.list.ListCopyInfo;

/**
 * <AUTHOR>
 * @date 2019/7/15
 */
public interface ListCopyInfoService extends IdBaseService<ListCopyInfo> {

	void saveListCopy(ListCopyInfo listCopyInfo, List<ListCopyFilter> listCopyFilterList);

}