package tw.org.must.must.core.service.list;


import org.springframework.web.multipart.MultipartFile;
import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.model.list.ListBasicFileBase;
import tw.org.must.must.model.list.ListDspFileBase;

import java.util.Date;
import java.util.List;

public interface ListDspFileBaseService extends BaseService<ListDspFileBase> {

    List<ListDspFileBase> getListDspFileBaseByFileStatus(int i);

    List<ListDspFileBase> getListDspByAppleMusicFile();

    List<ListDspFileBase> getListDspFileBase(Long valueOf, String startDateStr, String endDateStr);

    String upload(String categoryCode, Long claimMinimaInfoId, MultipartFile[] files);

    List<ListDspFileBase> listListDspFileBaseWithPage(Integer pageNum, Integer pageSize, Long queueId);

    ListDspFileBase getListDspFileBaseByMinimaIdAndQueueId(Long id, Long listFileQueueId);

    ListDspFileBase getListDspFileBaseByProductAndQueueId(String productKey, Long listFileQueueId);

    List<ListDspFileBase> getListByCategoryCodeNotAll(String categoryCode);

    List<ListDspFileBase> getListDspFileBaseByQueueId(Long queueId);

    List<ListDspFileBase> getListDspFileBaseList(String listCategoryCode, Date distListStartTime, Date distListEndTime);

    int deleteByFileQueue(Long id);

    List<ListDspFileBase> getListDspFileBaseByMinimaInfoIdListAndFileDate(List<Long> claimMinimaInfoIdList, String startDateStr, String endDateStr);

    List<ListDspFileBase> getListDspFileByQueueIdAndIsShow(Long fileQuenceId, Integer isShow);

    List<ListDspFileBase> getListDspFileBaseByMinimaId(Long id);

    List<ListDspFileBase> getListDspFileBaseByQueueIdAndListFileStartTimeAndIsShow(Long fileQuenceId, Date listFileStartTime,Integer isShow);

    List<ListDspFileBase> getListDspFileBaseByQueueIdsAndFileDate(List<Long> fileQueueIdList, String startDateStr, String endDateStr);

    List<ListDspFileBase> getListDspFileBaseByQueueIds(List<Long> queueIds);

    List<ListDspFileBase> getListDspFileBaseByMinimaIds(List<Long> claimMinimaInfoIds);
}