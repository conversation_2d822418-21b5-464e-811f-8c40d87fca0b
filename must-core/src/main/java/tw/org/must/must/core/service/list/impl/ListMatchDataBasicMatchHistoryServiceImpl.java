package tw.org.must.must.core.service.list.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.util.ExampleUtil;
import tw.org.must.must.core.service.list.ListMatchDataBasicMatchHistoryLogService;
import tw.org.must.must.core.service.list.ListMatchDataBasicMatchHistoryService;
import tw.org.must.must.core.service.wrk.WrkWorkService;
import tw.org.must.must.core.service.wrk.WrkWorkTitleService;
import tw.org.must.must.mapper.list.ListMatchDataBasicMatchHistoryMapper;
import tw.org.must.must.model.list.*;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkTitle;

@Service
public class ListMatchDataBasicMatchHistoryServiceImpl extends BaseServiceImpl<ListMatchDataBasicMatchHistory>
        implements ListMatchDataBasicMatchHistoryService {

    private static final Logger logger = LoggerFactory.getLogger(ListMatchDataBasicMatchHistoryServiceImpl.class);

    private final ListMatchDataBasicMatchHistoryMapper listMatchDataBasicMatchHistoryMapper;

    @Lazy
    @Autowired
    private WrkWorkService workService;

    @Lazy
    @Autowired
    private WrkWorkTitleService wrkWorkTitleService;
    @Autowired
    @Lazy
    private ListMatchDataBasicMatchHistoryLogService listMatchDataBasicMatchHistoryLogService;


    @Autowired
    public ListMatchDataBasicMatchHistoryServiceImpl(
            ListMatchDataBasicMatchHistoryMapper listMatchDataBasicMatchHistoryMapper) {
        super(listMatchDataBasicMatchHistoryMapper);
        this.listMatchDataBasicMatchHistoryMapper = listMatchDataBasicMatchHistoryMapper;
    }

    @Override
    public Map<String, ListMatchDataBasicMatchHistory> getMapByMd5List(List<String> uniqueKeyMd5List) {
        if (uniqueKeyMd5List == null || uniqueKeyMd5List.size() < 1) {
            return new HashMap<String, ListMatchDataBasicMatchHistory>();
        }
        Example example = new Example(ListMatchDataBasicMatchHistory.class);
        Criteria criteria = example.createCriteria();
        criteria.andIn("uniqueKeyMd5", uniqueKeyMd5List);
        List<ListMatchDataBasicMatchHistory> list = listMatchDataBasicMatchHistoryMapper.selectByExample(example);
        return list.stream().collect(
                Collectors.toMap(ListMatchDataBasicMatchHistory::getUniqueKeyMd5, Function.identity(), (a, b) -> a));
    }

    @Override
    public int deleteListMatchDataBasicMatchHistoryByUniqueKeyMd5(String uniqueKeyMd5) {
        if (uniqueKeyMd5 == null || uniqueKeyMd5.isEmpty()) {
            return 0;
        }
        Example example = new Example(ListMatchDataBasicMatchHistory.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("uniqueKeyMd5", uniqueKeyMd5);
        return listMatchDataBasicMatchHistoryMapper.deleteByExample(example);
    }

    @Override
    public Integer saveListMatchDataBasicMatchHistory(List<ListMatchDataBasicMatchHistory> listMatchDataBasicMatchHistoryList) {
        if (listMatchDataBasicMatchHistoryList == null || listMatchDataBasicMatchHistoryList.isEmpty()) {
            return 0;
        }
       /* List<String> uniqueKeyMd5List = listMatchDataBasicMatchHistoryList.stream()
                .map(ListMatchDataBasicMatchHistory::getUniqueKeyMd5).collect(Collectors.toList());

        deleteListMatchDataBasicMatchHistoryByUniqueKeyMd5List(uniqueKeyMd5List);*/

        List<List<ListMatchDataBasicMatchHistory>> partitions = Lists.partition(listMatchDataBasicMatchHistoryList, Constants.BATCH_SIZE_1000);

        try {
            for (List<ListMatchDataBasicMatchHistory> partition : partitions) {
                listMatchDataBasicMatchHistoryMapper.insertDuplicate(partition);
            }
        }catch (Exception e){
            logger.error("",e);
        }
        return 1;
    }

    @Override
    public void deleteListMatchDataBasicMatchHistoryByUniqueKeyMd5List(List<String> uniqueKeyMd5List) {
        Example example = new Example(ListMatchDataBasicMatchHistory.class);
        Criteria criteria = example.createCriteria();
        criteria.andIn("uniqueKeyMd5", uniqueKeyMd5List);
        listMatchDataBasicMatchHistoryMapper.deleteByExample(example);
    }

    @Override
    public List<ListMatchDataBasicMatchHistory> getList(ListMatchDataBasicMatchHistory listMatchDataBasicMatchHistory) {
        Example example = new Example(ListMatchDataBasicMatchHistory.class);
        Criteria criteria = example.createCriteria();
        String uniqueKeyMd5 = listMatchDataBasicMatchHistory.getUniqueKeyMd5();
        if(StringUtils.isNotBlank(uniqueKeyMd5)){
            criteria.andEqualTo("uniqueKeyMd5", uniqueKeyMd5);
        }
        String title = listMatchDataBasicMatchHistory.getTitleCh();
        if(StringUtils.isNotBlank(title)) {
            criteria.andLike("titleCh", ExampleUtil.exampleLikeAll(title));
        }
        String workArtist = listMatchDataBasicMatchHistory.getArtists();
        if(StringUtils.isNotBlank(workArtist)) {
            criteria.andLike("artists", ExampleUtil.exampleLikeAll(workArtist));
        }
       /* String resourceId = listMatchDataBasicMatchHistory.get();
        if(StringUtils.isNotBlank(resourceId)) {
            criteria.andLike("resourceId", ExampleUtil.exampleLikeAll(resourceId));
        }*/
        String author = listMatchDataBasicMatchHistory.getAuthors();
        if(StringUtils.isNotBlank(author)) {
            criteria.andLike("authors", ExampleUtil.exampleLikeAll(author));
        }
        String composer = listMatchDataBasicMatchHistory.getComposers();
        if(StringUtils.isNotBlank(composer)) {
            criteria.andLike("composers", ExampleUtil.exampleLikeAll(composer));
        }
        String isrc = listMatchDataBasicMatchHistory.getIsrc();
        if(StringUtils.isNotBlank(isrc)) {
            criteria.andLike("isrc", ExampleUtil.exampleLikeAll(isrc));
        }
        String iswc = listMatchDataBasicMatchHistory.getIswc();
        if(StringUtils.isNotBlank(iswc)) {
            criteria.andLike("iswc", ExampleUtil.exampleLikeAll(iswc));
        }
        Long matchWorkId = listMatchDataBasicMatchHistory.getWorkId();
        if(Objects.nonNull(matchWorkId)) {
            criteria.andEqualTo("workId", matchWorkId);
        }
        Integer matchWorkSocietyCode = listMatchDataBasicMatchHistory.getWorkSocietyCode();
        if(Objects.nonNull(matchWorkSocietyCode)) {
            criteria.andEqualTo("workSocietyCode", matchWorkSocietyCode);
        }
        return listMatchDataBasicMatchHistoryMapper.selectByExample(example);
    }

    @Override
    @Transactional
    public Integer updateAndLog(ListMatchDataBasicMatchHistory exist, ListMatchDataBasicMatchHistoryLog log) {
        this.update(exist);
        listMatchDataBasicMatchHistoryLogService.add(log);
        return 1;
    }

    @Override
    public List<ListMatchDataBasicMatchHistory> getHistoryByIdAndSoc(Long workId, Integer workSociety) {
        Example example = new Example(ListMatchDataBasicMatchHistory.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("workId", workId);
        criteria.andEqualTo("workSocietyCode", workSociety);
        return listMatchDataBasicMatchHistoryMapper.selectByExample(example);
    }

    @Override
    public List<ListMatchDataBasicMatchHistory> getListByStartId(Long startId) {
        Example example = new Example(ListMatchDataBasicMatchHistory.class);
        Criteria criteria = example.createCriteria();
        criteria.andGreaterThan("id", startId);
        example.orderBy("id");
        return listMatchDataBasicMatchHistoryMapper.selectByExampleAndRowBounds(example,new RowBounds(0,20000));
    }

    @Override
    public void insertListMatchDataBasicMatchHistoryForTitleUpdate(List<ListMatchDataBasicMatchHistory> list) {

        listMatchDataBasicMatchHistoryMapper.insertListMatchDataBasicMatchHistoryForTitleUpdate(list);
    }

    @Override
    public void checkAndCompletionWork(ListMatchDataBasicMatchHistory exist, ListMatchDataBasicMatchHistory history, Boolean isBatch) {
        Long workId = history.getWorkId();
        Integer workSocietyCode = history.getWorkSocietyCode();
        boolean isChanged = true;
        if(exist.getWorkId().equals(workId) && exist.getWorkSocietyCode().equals(workSocietyCode)) {
            isChanged = false;
        }

        if(isBatch){
            exist.setWorkId(workId);
            exist.setWorkSocietyCode(workSocietyCode);
        }else {
            BeanUtils.copyProperties(history, exist, "id");
            exist.init();
        }

        if(!isChanged) {
            return;
        }
        WrkWork work = workService.getWrkWorkByWorkId(workId, workSocietyCode);
        if(Objects.isNull(work)) {
            return;
        }
        exist.setMatchWorkType(work.getWorkType());
        if(StringUtils.isBlank(history.getIswc())) {
            exist.setIswc(work.getISWC());
        }
        WrkWorkTitle workTitle = wrkWorkTitleService.getWrkWorkTitleByWrkId(workId, workSocietyCode, 0L);
        if(Objects.isNull(workTitle)) {
            return;
        }
        if(StringUtils.isBlank(history.getTitleCh())) {
            String title = StringUtils.isNotBlank(workTitle.getTitle()) ? String.format("%s/%s", workTitle.getTitle(), workTitle.getTitleEn()) : workTitle.getTitleEn();
            exist.setTitleCh(title);
            exist.setMatchTitleId(workTitle.getId());
        }
    }

}