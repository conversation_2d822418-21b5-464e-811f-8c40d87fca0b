package tw.org.must.must.core.service.listoverseas;


import tw.org.must.must.model.listoverseas.ListMatchDataOverseasMatchWork;

import java.util.List;

import tw.org.must.must.common.base.BaseService;

public interface ListMatchDataOverseasMatchWorkService extends BaseService<ListMatchDataOverseasMatchWork> {

	Integer saveList(List<ListMatchDataOverseasMatchWork> listMatchDataOverseasMatchWorkList);

	List<ListMatchDataOverseasMatchWork> getListMatchDataOverseasMatchWorkList(String dataUniqueKey);

	void insertIgnore(List<ListMatchDataOverseasMatchWork> list);

}