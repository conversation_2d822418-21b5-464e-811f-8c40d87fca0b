package tw.org.must.must.core.service.listoverseas;


import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.common.base.Page;
import tw.org.must.must.dto.listoverseas.OverseasFileBaseWithSocietyDto;
import tw.org.must.must.model.list.vo.ListOverseasFileBaseParamVo;
import tw.org.must.must.model.listoverseas.ListOverseasFileBase;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface ListOverseasFileBaseService extends BaseService<ListOverseasFileBase> {

	List<ListOverseasFileBase> getListOverseasFileBaseList(ListOverseasFileBaseParamVo listOverseasFileBaseParamVo);
	void reportListOverseasFileBaseList(ListOverseasFileBaseParamVo listOverseasFileBaseParamVo,HttpServletResponse response);

	int updateListOverseasFileBaseByStatus(Long id, Integer i);

	String upload(MultipartFile[] files);

	String upload(MultipartFile[] files, Long receiptDetailsId);
	void uploadNew(Long receiptId,MultipartFile[] files);
	void uploadNew(Long receiptId,String filePath);
	void uploadNew(String filePath);

	void downloadFile(File file, HttpServletResponse response, HttpServletRequest request) throws IOException;

	BigDecimal getFieTotalAmount(Long receiptId);
	BigDecimal getTotalStatementAmount(Long receiptId);

	Integer bindReceipt(ListOverseasFileBase listOverseasFileBase);

	BigDecimal selectAmountByReceiptId(Long receiptId);

	Integer updateBatchSelective(List<String> ids);

	Integer logicDelete(Long id);

	Map<Long,BigDecimal> getTotalAmountGroupByReceiptId(List<Long> receiptIds);

	List<ListOverseasFileBase> getListOverseasFileBaseByReceiptIds(List<Long> receiptIds);

	List<ListOverseasFileBase> getListOverseasFileBaseByReceiptId(Long receiptId);

	ListOverseasFileBase getSummaryAmount(Long receiptId) ;

	ListOverseasFileBase getTotalAllAmount( Long id, Long receiptId, Integer status, String overseasDistNo, Integer remitSocietyCode, String remitSocietyName, String fileName);

	List<ListOverseasFileBase> getListOverseasFileBaseByDistNo(String distNo);

	/**
	 * 获取海外清单文件基础信息，包括协会名称
	 * @return 包含协会名称的海外清单文件列表
	 */
	List<OverseasFileBaseWithSocietyDto> getOverseasFileBaseWithSociety();

	/**
	 * 分页获取海外清单文件基础信息，包括协会名称
	 * @param page 分页参数
	 * @param fileType 文件类型
	 * @param remitSocietyCode 来源协会代码
	 * @param fileName 文件名
	 * @return 分页的海外清单文件列表
	 */
	com.github.pagehelper.PageInfo<OverseasFileBaseWithSocietyDto> getOverseasFileBaseWithSocietyPage(
			Page page,
			String fileType,
			Integer remitSocietyCode,
			String fileName);
}