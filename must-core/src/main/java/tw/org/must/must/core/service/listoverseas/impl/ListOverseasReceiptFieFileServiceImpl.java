package tw.org.must.must.core.service.listoverseas.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.common.util.Md5;
import tw.org.must.must.core.service.listoverseas.ListOverseasReceiptFieFileService;
import tw.org.must.must.mapper.listoverseas.ListOverseasReceiptFieFileMapper;
import tw.org.must.must.model.listoverseas.ListMatchDataOverseasMapping;
import tw.org.must.must.model.listoverseas.ListOverseasReceiptFieFile;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class ListOverseasReceiptFieFileServiceImpl extends BaseServiceImpl<ListOverseasReceiptFieFile> implements ListOverseasReceiptFieFileService {

    private final ListOverseasReceiptFieFileMapper listOverseasReceiptFieFileMapper;

    @Autowired
    public ListOverseasReceiptFieFileServiceImpl(ListOverseasReceiptFieFileMapper listOverseasReceiptFieFileMapper) {
        super(listOverseasReceiptFieFileMapper);
        this.listOverseasReceiptFieFileMapper = listOverseasReceiptFieFileMapper;
    }

    @Value("${list.file.overseasPath}")
    private String listFilePath;

    @Override
    public String upload(MultipartFile[] files, Long receiptDetailsId) {
        String filePath = listFilePath + File.separator + "fie" + File.separator + new SimpleDateFormat("yyyy-MM-dd").format(new Date());


        int fileSize = files.length;
        if (fileSize > 0) {
            try {
                for (MultipartFile file : files) {
                    String fileName = file.getOriginalFilename();
                    String targetFileName = Md5.getMd5ByInputStream(file.getInputStream());
                    if (StringUtils.isBlank(targetFileName)) {
                        return "文件读取异常";
                    }
                    filePath = filePath.concat(File.separator).concat(targetFileName);
                    File parentFile = new File(filePath);
                    if (!parentFile.exists()) {
                        parentFile.mkdirs();
                    }
                    targetFileName = fileName;
                    File targetFile = new File(parentFile, Objects.requireNonNull(targetFileName));

                    file.transferTo(targetFile);

                    ListOverseasReceiptFieFile fieFile = new ListOverseasReceiptFieFile();
                    fieFile.setName(targetFileName);
                    fieFile.setFilePath(targetFile.getAbsolutePath());
                    if(receiptDetailsId != null){
                        fieFile.setReceiptDetailsId(receiptDetailsId);
                    }
                    fieFile.init();
                    this.add(fieFile);
                    return fieFile.getId() + "";
                }
            } catch (Exception e) {
                e.printStackTrace();
                return "文件传输异常！";
            }
        } else {
            return "文件不能为空！";
        }
        return null;
    }

    @Override
    public List<ListOverseasReceiptFieFile> getByReceiptDetailId(Long receiptDetailId) {

        Example example = new Example(ListOverseasReceiptFieFile.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("receiptDetailsId", receiptDetailId);
        return listOverseasReceiptFieFileMapper.selectByExample(example);
    }

}
