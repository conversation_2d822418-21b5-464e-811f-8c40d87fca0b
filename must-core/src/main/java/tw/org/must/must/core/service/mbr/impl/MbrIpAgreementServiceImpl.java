package tw.org.must.must.core.service.mbr.impl;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.common.util.DateUtils;
import tw.org.must.must.core.service.mbr.MbrIpAgreementService;
import tw.org.must.must.core.service.ref.RefRightService;
import tw.org.must.must.mapper.mbr.MbrIpAgreementMapper;
import tw.org.must.must.model.mbr.MbrIpAgreement;
import tw.org.must.must.model.mbr.vo.IpAgreementGroupVO;
import tw.org.must.must.model.mbr.vo.MbrIpAgreementVO;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MbrIpAgreementServiceImpl extends BaseServiceImpl<MbrIpAgreement> implements MbrIpAgreementService {

	private final MbrIpAgreementMapper mbrIpAgreementMapper;

	private final RefRightService refRightService;

	@Autowired
	public MbrIpAgreementServiceImpl(MbrIpAgreementMapper mbrIpAgreementMapper, RefRightService refRightService) {
		super(mbrIpAgreementMapper);
		this.mbrIpAgreementMapper = mbrIpAgreementMapper;
		this.refRightService = refRightService;
	}

	@Override
	public Integer saveSelectiveList(List<MbrIpAgreement> mbrIpAgreementList) {
		if (mbrIpAgreementList.size() < 1) {
			return 0;
		}
		return mbrIpAgreementMapper.saveSelectiveList(mbrIpAgreementList);
	}

	@Override
	public List<IpAgreementGroupVO> listGroupVOByIpNo(String ipNo) {
		List<MbrIpAgreement> agreements = mbrIpAgreementMapper.selectByIpNo(ipNo);
		if (agreements == null || agreements.isEmpty()) {
			return null;
		}
		Map<String, String> rightCashMap = refRightService.rightCashMap();
		rightCashMap.put("DB","NOD");
		Map<String, List<MbrIpAgreement>> map = new HashMap<>(16);
		agreements.forEach(agreement -> {
//			String right = converRight(agreement.getRightCode());
			String right = agreement.getRightCode();
			if (rightCashMap.containsKey(right)) {
				String cashRight = rightCashMap.get(right);
				if (!map.containsKey(cashRight)) {
					map.put(cashRight, new ArrayList<>());
				}
				map.get(cashRight).add(agreement);
			} else {
				if (!map.containsKey("OR")) {
					map.put("OR", new ArrayList<>());
				}
				map.get("OR").add(agreement);
			}
		});
		List<IpAgreementGroupVO> agreementGroupVOList = new ArrayList<>();
		map.forEach((k, v) -> {
			IpAgreementGroupVO agreementGroupVO = IpAgreementGroupVO.build(k, v);
			agreementGroupVOList.add(agreementGroupVO);
		});
		return agreementGroupVOList;
	}

	@Override
	public List<IpAgreementGroupVO> getMbrIpAgreements(String ipBaseNo) {
		Example example = new Example(MbrIpAgreement.class);
		Criteria createCriteria = example.createCriteria();
		createCriteria.andEqualTo("ipBaseNo", ipBaseNo);
		List<MbrIpAgreement> agreements = mbrIpAgreementMapper.selectByExample(example);
		if (agreements == null || agreements.isEmpty()) {
			return null;
		}
		Map<String, String> rightCashMap = refRightService.rightCashMap();
		rightCashMap.put("DB","NOD");
		Map<String, List<MbrIpAgreement>> map = new HashMap<>(16);
		agreements.forEach(agreement -> {
//			String right = converRight(agreement.getRightCode());
			String right = agreement.getRightCode();
			if (rightCashMap.containsKey(right)) {
				String cashRight = rightCashMap.get(right);
				if (!map.containsKey(cashRight)) {
					map.put(cashRight, new ArrayList<>());
				}
				map.get(cashRight).add(agreement);
			} else {
				if (!map.containsKey("OR")) {
					map.put("OR", new ArrayList<>());
				}
				map.get("OR").add(agreement);
			}
		});
		List<IpAgreementGroupVO> agreementGroupVOList = new ArrayList<>();
		map.forEach((k, v) -> {
			IpAgreementGroupVO agreementGroupVO = IpAgreementGroupVO.build(k, v);
			agreementGroupVOList.add(agreementGroupVO);
		});
		return agreementGroupVOList;
	}

	@Override
	public List<MbrIpAgreement> getMbrIpAgreementsByIpBaseNo(String ipBaseNo) {
		Example example = new Example(MbrIpAgreement.class);
		Criteria createCriteria = example.createCriteria();
		createCriteria.andEqualTo("ipBaseNo", ipBaseNo);
		createCriteria.andEqualTo("creationClassCode", "MW");
		return mbrIpAgreementMapper.selectByExample(example);
	}

	@Override
	public Integer deleteByIpNo(String ipNo) {
		return mbrIpAgreementMapper.deleteByIpNo(ipNo);
	}

	private String converRight(String cashRight) {
		if ("NDB".equals(cashRight)) {
			cashRight = "NOD";
		}
		return cashRight;
	}

	@Override
	public Map<String, List<MbrIpAgreementVO>> selectMapByIpBaseNo(List<String> ipBaseNoList) {
		if (ipBaseNoList.size() < 1) {
			return new HashMap<String, List<MbrIpAgreementVO>>();
		}
		List<MbrIpAgreementVO> list;
		try {
			list = mbrIpAgreementMapper.selectListByIpBaseNo(ipBaseNoList);
			return list.stream().collect(Collectors.groupingBy(MbrIpAgreementVO::getIpBaseNo));
		} catch (Exception e) {
			return new HashMap<String, List<MbrIpAgreementVO>>();
		}
	}

	@Override
	public MbrIpAgreement getMbrIpAgreementByIpBaseNoAndRightCode(String ipBaseNo, String rightCode) {
		Example example = new Example(MbrIpAgreement.class);
		Criteria createCriteria = example.createCriteria();
		createCriteria.andEqualTo("ipBaseNo", ipBaseNo);
		createCriteria.andEqualTo("rightCode", rightCode);
		createCriteria.andGreaterThan("validTo", new Date());
		List<MbrIpAgreement> miaList = mbrIpAgreementMapper.selectByExample(example);
		if (null != miaList && !miaList.isEmpty()){
			return miaList.get(0);
		}
		return null;
	}

	@Override
	public List<MbrIpAgreement> getMbrIpAgreementByIdLimit1000(Long id) {
		if(null == id){
			return new ArrayList<>();
		}
		Example example = new Example(MbrIpAgreement.class);
		Criteria createCriteria = example.createCriteria();
		createCriteria.andGreaterThan("id", id);
		return mbrIpAgreementMapper.selectByExampleAndRowBounds(example,new RowBounds(0,100000));
	}


	@Override
	public List<MbrIpAgreement> getMbrIpAgreementByIpBaseNoAndRightCodeList(@NotNull  String ipBaseNo, List<String> rightCodeList,String year) {

		if(StringUtils.isBlank(ipBaseNo)){

			return null;
		}


		Example example = new Example(MbrIpAgreement.class);
		Criteria createCriteria = example.createCriteria();

		createCriteria.andEqualTo("ipBaseNo", ipBaseNo);
		createCriteria.andEqualTo("tisN",158);
		createCriteria.andEqualTo("creationClassCode", "MW");

		if(StringUtils.isNotBlank(year)){
			// 类似于：and (valid_from<='2019-01-01' or valid_from<='2019-12-31') and valid_to >= '2019-12-31'
//			Criteria criteria = example.createCriteria();
//			criteria.andLessThanOrEqualTo("validFrom",year+"-12-31");
//			example.and(criteria);


			//AND (  valid_from<='2022-12-31' AND valid_to > '2022-01-01')
			createCriteria.andLessThanOrEqualTo("validFrom",year+"-12-31");
			createCriteria.andGreaterThanOrEqualTo("validTo",year+"-01-01");
		}else {
			String now = DateUtils.date2Str(DateUtils.date_sdf.get()) ;
			createCriteria.andLessThanOrEqualTo("validFrom",now);
			createCriteria.andGreaterThanOrEqualTo("validTo",now);
		}

		if (rightCodeList != null && rightCodeList.size() > 0){
			createCriteria.andIn("rightCode",rightCodeList);
		}

		return mbrIpAgreementMapper.selectByExample(example);
	}

	@Override
	public List<MbrIpAgreement> getMbrIpAgreementByIpBaseAndRightCodeAndDate(String ipBaseNo, List<String> rightCodeList, Date date) {
		if(StringUtils.isBlank(ipBaseNo)){

			return null;
		}


		Example example = new Example(MbrIpAgreement.class);
		Criteria createCriteria = example.createCriteria();

		createCriteria.andEqualTo("ipBaseNo", ipBaseNo);
		createCriteria.andEqualTo("tisN",158);
		createCriteria.andEqualTo("creationClassCode", "MW");

		createCriteria.andLessThanOrEqualTo("validFrom",date);
		createCriteria.andGreaterThanOrEqualTo("validTo",date);

		if (rightCodeList != null && rightCodeList.size() > 0){
			createCriteria.andIn("rightCode",rightCodeList);
		}

		return mbrIpAgreementMapper.selectByExample(example);
	}

    @Override
    public Integer deleteByIPI(List<MbrIpAgreement> mbrIpAgreementList) {
		Integer result = 0;
		for (MbrIpAgreement mbrIpAgreement : mbrIpAgreementList) {
			Example example = new Example(MbrIpAgreement.class);
			Criteria criteria = example.createCriteria();
			criteria.andEqualTo("ipBaseNo", mbrIpAgreement.getIpBaseNo());
			criteria.andEqualTo("creationClassCode", mbrIpAgreement.getCreationClassCode());
			criteria.andEqualTo("rightCode", mbrIpAgreement.getRightCode());
			criteria.andEqualTo("roleCode", mbrIpAgreement.getRoleCode());
			result += mbrIpAgreementMapper.deleteByExample(example);
		}
		return result;
    }

}