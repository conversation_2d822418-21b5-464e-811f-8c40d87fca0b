package tw.org.must.must.core.service.new_list;

import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.model.new_list.List2BasicFileCombinedFileQueue;

import java.util.Date;
import java.util.List;

public interface List2BasicFileCombinedFileQueueService extends BaseService<List2BasicFileCombinedFileQueue> {
    List<List2BasicFileCombinedFileQueue> getByUploadTime(Date combinedStartDate, Date combinedEndDate);

    List<List2BasicFileCombinedFileQueue> getByBaseIds(List<Long> baseIds);

    List<List2BasicFileCombinedFileQueue> getByBaseId(Long baseId);

    int deleteByBaseId(Long baseId);

    /**
     * 根据file_queue_id查询合并文件队列记录
     * @param fileQueueId 文件队列ID
     * @return 合并文件队列记录列表
     */
    List<List2BasicFileCombinedFileQueue> getByFileQueueId(Long fileQueueId);

    /**
     * 根据file_queue_id删除合并文件队列记录
     * @param fileQueueId 文件队列ID
     * @return 删除的记录数
     */
    int deleteByFileQueueId(Long fileQueueId);
}
