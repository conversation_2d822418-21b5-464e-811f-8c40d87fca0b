package tw.org.must.must.core.service.new_list;


import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;
import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.common.base.Page;
import tw.org.must.must.dto.list.List2FileQueueDeleteImpactDto;
import tw.org.must.must.model.list.ListCategory;

import tw.org.must.must.model.new_list.List2FileQueue;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface List2FileQueueService extends BaseService<List2FileQueue> {

    List<List2FileQueue> getAffectiveListFileQueue(String string, String fileType);

//	List<List2FileQueue> getListFileQueueList(String categoryCode, String status, String uploadType,
//			String uploadUserName, String fileName, String string);

    String upload(String fileType, String sequence, String filePath, Map<String, Integer> msgResult) throws Exception;

    List<List2FileQueue> getAffectiveListFileQueueForYoutube(String string, Integer i);

    Integer updateStatusById(List2FileQueue listqueue);

    List<List2FileQueue> getListFileQueueList(Long id, String categoryCode, String fileName, String fileUploadStartDate,
                                             String fileUploadEndDate, String uploadType, Integer status, String fileType, Integer isNoCategory, String filePath);

    Integer deleteListFileQueue(Long id);

    String upload(List2FileQueue lfq, MultipartFile[] files);

    String uploadCatalogFile(String categoryCode, Long claimMinimaInfoId, String filePath, String matchMark);

    List2FileQueue uploadWithoutStatus(List2FileQueue queue);

    int uploadList(List<List2FileQueue> uploadQueues);

    List2FileQueue getListFileQueueByFilePathMd5(String md5DigestAsHex);

    ListCategory generateSingleSessionCategoryCode();

    List<List2FileQueue> getParsedListFileQueueByFolder(String folder);

    List<List2FileQueue> getParsedListFileQueueByFolder(String folder, Date combinedStartDate, Date combinedEndDate);

    List<List2FileQueue> getListFileQueueListByUploadTime(Integer status, Date startDate, Date endDate);

    /**
     * 单场次的categoryCode 需要从base表对应的categoryCode中查询
     * @param page
     * @param id
     * @param categoryCode
     * @param fileName
     * @param fileUploadStartDate
     * @param fileUploadEndDate
     * @param uploadType
     * @param status
     * @param fileType
     * @param isNoCategory
     * @param filePath
     * @param usageTime
     * @return
     */
    PageInfo<List2FileQueue> getListFileQueueList2(Page page, Long id, String categoryCode, String fileName, String fileUploadStartDate,
                                                  String fileUploadEndDate, String uploadType, Integer status, String fileType,
                                                  Integer isNoCategory, String filePath, String usageTime);

    List<List2FileQueue> getListFileQueueByIds(List<String> ids);

    /**
     * 删除P2类型文件队列及相关数据的新方法
     * @param id 文件队列ID
     * @return 删除结果
     */
    Integer deleteListFileQueueNew(Long id);

    /**
     * 查询删除P2类型文件队列会影响的相关数据记录ID
     * @param id 文件队列ID
     * @return 删除影响数据DTO
     */
    List2FileQueueDeleteImpactDto getDeleteImpact(Long id);

}