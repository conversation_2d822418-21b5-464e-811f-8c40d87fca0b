package tw.org.must.must.core.service.new_list.impl;

import cn.miludeer.jsoncode.JsonCode;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.util.BeanCopyUtil;
import tw.org.must.must.common.util.CommonUtils;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.common.util.excel.ExcelUtil;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.Result;
import tw.org.must.must.common.vcp.CommonUtil;
import tw.org.must.must.core.UsageListParsing;
import tw.org.must.must.core.excel.ExcelReaderUtil;
import tw.org.must.must.core.excel.IRowReader;
import tw.org.must.must.core.handle.redisService.RedisService;
import tw.org.must.must.core.service.list.*;
import tw.org.must.must.core.service.new_list.List2BasicFileConfigService;
import tw.org.must.must.core.service.new_list.List2BasicFileTemplateConfigService;
import tw.org.must.must.core.service.new_list.List2FileQueueService;
import tw.org.must.must.core.shiro.LoginUtil;
import tw.org.must.must.model.list.ListCategory;
import tw.org.must.must.model.list.enums.BasicFileTemplateParsingMode;
import tw.org.must.must.model.list.vo.ListBasicFileTemplateConfigVo;
import tw.org.must.must.model.new_list.List2BasicFileConfig;
import tw.org.must.must.model.new_list.List2BasicFileTemplate;
import tw.org.must.must.mapper.new_list.List2BasicFileTemplateMapper;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.new_list.List2BasicFileTemplateService;
import tw.org.must.must.model.new_list.List2BasicFileTemplateConfig;
import tw.org.must.must.model.new_list.List2FileQueue;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class List2BasicFileTemplateServiceImpl extends BaseServiceImpl<List2BasicFileTemplate> implements List2BasicFileTemplateService {



    private static final Logger logger = LoggerFactory.getLogger(List2BasicFileTemplateServiceImpl.class);

    @Autowired
    @Lazy
    private List2BasicFileTemplateConfigService list2BasicFileTemplateConfigService;

    @Autowired
    @Lazy
    private List2BasicFileConfigService listBasicFileConfigService;

    @Autowired
    private ListFilePathChangeService listFilePathChangeService;

    @Lazy
    @Autowired
    private List2FileQueueService listFileQueueService;

    @Autowired
    private ListCategoryService listCategoryService;

    @Autowired
    private RedisService redisService;

    private String CHECK_AND_CLASSIFICATION_FLAG = "checkAndClassificationFlag_new" ;

    private ConcurrentLinkedQueue<Map<String, List<List2FileQueue>>> concurrentLinkedQueue = new ConcurrentLinkedQueue<>();


	private final List2BasicFileTemplateMapper list2BasicFileTemplateMapper;

    @Autowired
    public List2BasicFileTemplateServiceImpl(List2BasicFileTemplateMapper list2BasicFileTemplateMapper) {
        super(list2BasicFileTemplateMapper);
        this.list2BasicFileTemplateMapper = list2BasicFileTemplateMapper;
    }


    @Override
    public List<List2BasicFileTemplate> getList(List2BasicFileTemplate list2BasicFileTemplate) {
        Example example = new Example(List2BasicFileTemplate.class);
        Example.Criteria createCriteria = example.createCriteria();
        if (StringUtils.isNotBlank(list2BasicFileTemplate.getFolder())) {
            createCriteria.andLike("folder", String.format("%%%s%%", list2BasicFileTemplate.getFolder()));
        }
        if (StringUtils.isNotBlank(list2BasicFileTemplate.getCategoryCode())) {
            createCriteria.andEqualTo("categoryCode", list2BasicFileTemplate.getCategoryCode());
        }
        if (null != list2BasicFileTemplate.getType() && list2BasicFileTemplate.getType() >= 0) {
            createCriteria.andEqualTo("type", list2BasicFileTemplate.getType());
        }
        example.orderBy("amendTime").desc();
        return list2BasicFileTemplateMapper.selectByExample(example);
    }

    @Override
    public boolean checkId(Long templateId) {
        List2BasicFileTemplate template = getById(templateId);
        if (Objects.isNull(template)) {
            return false;
        }
        return true;
    }


    @Override
    public boolean checkByFolder(String folder) {
        Example example = new Example(List2BasicFileTemplate.class);
        Example.Criteria createCriteria = example.createCriteria();
        createCriteria.andEqualTo("folder", folder);
        List2BasicFileTemplate list2BasicFileTemplate = list2BasicFileTemplateMapper.selectOneByExample(example);
        if (Objects.isNull(list2BasicFileTemplate)) {
            return false;
        }
        return true;
    }

    @Override
    public boolean checkByFolder(String folder, String categoryCode) {
        Example example = new Example(List2BasicFileTemplate.class);
        Example.Criteria createCriteria = example.createCriteria();
        createCriteria.andEqualTo("folder", folder);
        createCriteria.andEqualTo("categoryCode", categoryCode);
        List2BasicFileTemplate list2BasicFileTemplate = list2BasicFileTemplateMapper.selectOneByExample(example);
        if (Objects.isNull(list2BasicFileTemplate)) {
            return false;
        }
        return true;
    }


    @Override
    public Map<String, List<List2BasicFileTemplate>> getAllMap() {
        List<List2BasicFileTemplate> templates = list2BasicFileTemplateMapper.selectAll();
        Map<String, List<List2BasicFileTemplate>> result = new HashMap<>();
        if (Objects.nonNull(templates)) {
            result = templates.stream().collect(Collectors.groupingBy(List2BasicFileTemplate :: getFolder));
        }
        return result;
    }

    public static final String NO_CATEGORY = "no-category";
    public static final String DONE = "done";
    public static final String ERROR = "error";


    @Override
    public void recursionDeal(String originalPath, File file, Map<String, List<List2BasicFileTemplate>> headerMap, Map<String, List<List2FileQueue>> result) {
        if (Objects.isNull(file)) {
            return;
        }
        String fileName = file.getName();
        if (file.isDirectory()) {
            if (StringUtils.equalsAny(fileName, DONE, NO_CATEGORY, ERROR)) {
                this.deleteFiles(file);
                return;
            }
            File[] files = file.listFiles();
            if (files != null) {
                for (File f : files) {
                    recursionDeal(originalPath, f, headerMap, result);
                }
            }
        } else {
            if (fileName.startsWith("log_") && fileName.endsWith(".txt")) {
                this.deleteFiles(file);
                return;
            }
            //開始處理
            List2FileQueue listFileQueue = new List2FileQueue();
            listFileQueue.setFilePath(file.getAbsolutePath());
            String absolutePath = file.getParent(); //不匹配包含的文件名，只匹配文件夾路徑
            absolutePath = absolutePath.replace("\\", "/") + "/";
            boolean flag = false;
            for (Map.Entry<String,List<List2BasicFileTemplate>> entry : headerMap.entrySet()) {
                absolutePath = CommonUtil.ChineseNormalizer.doNormalize(absolutePath); //轉簡體 ，簡體匹配
                String s = CommonUtil.ChineseNormalizer.doNormalize(entry.getKey());
                String uploadType = getUploadType(absolutePath) ;
//                listFileQueue.setUploadType(uploadType);
                if (!s.startsWith("/")) {
                    s = "/" + s;
                }
                if (!s.endsWith("/")) {
                    s = s + "/";
                }
                if (absolutePath.indexOf(s) > 0) {
                    List<List2BasicFileTemplate> templates = entry.getValue();
                    Map<String,List2BasicFileTemplate> categoryMap = templates.stream().collect(Collectors.toMap(List2BasicFileTemplate::getCategoryCode, Function.identity(), (a, b) -> a));

                    List2BasicFileTemplate template = templates.get(0);
                    String categoryCode = template.getCategoryCode().replaceAll("-CJ|-PG","");
//                    String defaultCateCode = categoryCode;
                    if(StringUtils.equalsAny(uploadType,"CJ","PG")) {
                        categoryCode = categoryCode + "-" + uploadType;
                    }

                    template = categoryMap.get(categoryCode);
                    if(null == template){
                        if(isCheckCategory(categoryCode)){
                            listFileQueue.setCategoryCode(categoryCode) ;
                        }
                        break;
                    }else {
                        listFileQueue.setCategoryCode(template.getCategoryCode());
                        classifyMap(result, listFileQueue, String.valueOf(template.getId()));
                        flag = true;
                        break;
                    }

                }
            }
            if (!flag) {
                classifyMap(result, listFileQueue, NO_CATEGORY);
            }
        }
    }

    private void deleteFiles(File file) {
        if (!file.exists()) {
            return;
        }
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files.length == 0) {
                file.delete();
            } else {
                for (File file1 : files) {
                    deleteFiles(file1);
                }
            }
        }
        file.delete();

    }

    private static final Integer MATCH_LINES = 10;

    @Override
    public Map<String, List<List2FileQueue>> checkTitle(Map<String, List<List2FileQueue>> checkTitleResult) {
        Map<String, List<List2FileQueue>> resultMap = new HashMap<>();
        for (String key : checkTitleResult.keySet()) {
            if (StringUtils.equals(key, NO_CATEGORY)) {
                checkDefaultTitle(checkTitleResult.get(key), resultMap, key);
            } else {
                checkTemplateTitle(checkTitleResult.get(key), resultMap, key);
            }
        }
        return resultMap;
    }

    private void checkTemplateTitle(List<List2FileQueue> listFileQueueList, Map<String, List<List2FileQueue>> resultMap, String key) {
        //查詢清單配置必須要的字段（包含默認的）
        List2BasicFileTemplate template = this.getById(Long.parseLong(key));
        // 宽松解析模式
        boolean looseParseMode = template.getParsingMode().equals(BasicFileTemplateParsingMode.LOOSE_MODE.code());
        Map<String, Object> extJson = new HashMap<>();
        extJson.put("templateId", template.getId());
        List2BasicFileTemplateConfig config = new List2BasicFileTemplateConfig();
        config.setTemplateId(template.getId());
        //自定義header
        List<ListBasicFileTemplateConfigVo> configs = list2BasicFileTemplateConfigService.getList(config);
        List<String> configHeaders = configs.stream().filter(x -> x.getListBasicFileConfigBehoove() == 1)
                .map(z -> z.getListBasicFileConfigKey()).collect(Collectors.toList());
        //默認header && 必须检测字段（behoove = 1）
        Map<String, String[]> configHeaderMap2 = new HashMap<>();
        List<List2BasicFileConfig> allList = listBasicFileConfigService.getAllList();
        List<String> defaultHeaders = allList.stream().filter(x -> x.getBehoove() == 1).map(List2BasicFileConfig::getKey).collect(Collectors.toList());
        //篩選並剔除自定義header剩餘的必要的默認字段值
        UsageListParsing.UsageListParseOptions usOpts = new UsageListParsing.UsageListParseOptions();
        Class<? extends UsageListParsing.UsageListParseOptions> clz = usOpts.getClass();
        Field[] fields = clz.getDeclaredFields();
        for (Field field : fields) {
            for (String header : defaultHeaders) {
                if (field.getName().equals(header)) {
                    // 设置访问权限
                    field.setAccessible(true);
                    try {
                        if (!configHeaders.contains(header)) {
                            String[] value = (String[]) field.get(usOpts);
                            configHeaderMap2.put(header, value);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        //比对是否包含必要的字段
        for (List2FileQueue listFileQueue : listFileQueueList) {
            //獲取標題
            String filePath = listFileQueue.getFilePath();
            File file = new File(filePath);
            if (file.getName().startsWith("~$")) {
                //临时文件不处理，跳过
                continue;
            }
            logger.info("open file: {}", filePath);
            try {
                Boolean flag = false;
                //从开始行匹配扫描6行，查出表头
                Integer line = template.getLine() == 0 ? 1 : template.getLine();
                Integer endLine = line;
                StringBuilder logBuilder = new StringBuilder(); //記錄日志，用於寫入ERROR類別中，查找原因

                LinkedHashMap<Integer, List<String>> titlesMap = getTitles(file, line, filePath);

                if (Objects.isNull(titlesMap) || titlesMap.isEmpty()) {
                    logBuilder.append("當前沒有掃描到表頭，請確認文件内容是否正確。PS: 處理文件時，請勿打開文件，防止程序讀取不到！").append(CommonUtils.LINE_BREAK);
                    listFileQueue.setDescription(logBuilder.toString());
                    classifyMap(resultMap, listFileQueue, ERROR);
                    continue;
                }
                List<String> customHeaders = new ArrayList<>();
                for (ListBasicFileTemplateConfigVo configVo : configs) {
                    // 当宽松解析模式时，只校验必须的字段
                    if (looseParseMode && configVo.getListBasicFileConfigBehoove() != 1) {
                        continue;
                    }
                    // 這些字段 不需要校驗， 後期可以在 list_basic_file_config 添加可以字段表示不需要校驗的字段
                    if (StringUtils.equalsAny(configVo.getListBasicFileConfigKey(), "ignorableColNames", "remarkColNames")) {
                        continue;
                    }
                    String header = CommonUtil.ChineseNormalizer.doNormalize(configVo.getHeader()).replaceAll(" ", "");
//                    String header = CommonUtil.ChineseNormalizer.doNormalize(configVo.getHeader()).trim();
                    if (configVo.getObtainMethod() == 0) {
                        customHeaders.add(header);
                    } else if (configVo.getObtainMethod() == 1) {
                        List<String> headers = CommonUtils.regexNormalizeMatch(header, "\\$\\{(.*?)\\}", 1);
                        customHeaders.addAll(headers);
                    }
                }
                logger.info("{}模式，自定義表頭 titles : {}", looseParseMode ? "宽松" : "严格", JSON.toJSONString(customHeaders));
                logger.info("默認表頭 titles : {}", JSON.toJSONString(configHeaderMap2));
                Map<String, String[]> missingHeaderMap = BeanCopyUtil.copyImplSerializable(configHeaderMap2);
                boolean checkTypeNames = false; // 校验 音乐类型 - （一般，背景，广告），由于改接口
                for (Integer row : titlesMap.keySet()) {
                    //一行一行开始比较, row
                    List<String> titles = titlesMap.get(row);
                    endLine = row + 1;
                    titles = titles.stream().map(x -> CommonUtil.ChineseNormalizer.doNormalize(x).replaceAll(" ", "")).collect(Collectors.toList());
                    logger.info("第{}行，文件表頭  titles : {}", endLine, JSON.toJSONString(titles));
                    //比對自定義字段
                    if (titles.containsAll(customHeaders)) {
                        logger.info("第{}行，自定義表頭全部匹配成功", endLine);
                        //自定義表頭匹配成功再去匹配默認表頭
                        for (String headerKey : configHeaderMap2.keySet()) {
                            if (!checkTypeNames && titles.contains("音乐类型") ) { // 可能不是合并表头，是单表头，例：音乐类型-一般音乐，这个就不比较，缺失即缺失，不管
                                checkTypeNames = true;
                            }
                            String[] headerValues = configHeaderMap2.get(headerKey);
                            for (String headerValue : headerValues) {
                                if (titles.contains(CommonUtil.ChineseNormalizer.doNormalize(headerValue))) {
                                    missingHeaderMap.remove(headerKey);
                                    break;
                                }
                            }
                        }
                        if (missingHeaderMap.isEmpty()) {
                            //也包含默認header是空的情況
                            logger.info("第{}行，默認表頭全部匹配成功【必须】", endLine);
                            //把templateId寫入queue, 方便後續解析的時候不要再去匹配folder找到對應的List2BasicFileTemplate
                            listFileQueue.setExtJson(JSON.toJSONString(extJson));
                            flag = true;
                            break; //匹配成功跳出
                        } else {
                            logger.info("第{}行，默認表頭與文件表頭 匹配失敗", endLine);
                        }
                    } else {
                        logger.info("第{}行，自定義表頭與文件表頭 匹配失敗", endLine);
                    }
                }
                if (!missingHeaderMap.isEmpty()) {
                    if(checkTypeNames) {
                        // 只有表头有 音乐类型 的时候才去匹配 一般音乐、背景音乐、广告音乐 这三个表头。（但是有错位的情况，暂时不考虑，如把这三个音乐类型放在了音乐来源里，也有音乐来源，也能匹配上，主要匹配是根据这三个表头来对应的，问题不大）
                        // 只要有一种音乐类型的表头即可
                        long typeNamesCount = missingHeaderMap.keySet().stream().filter(strings -> strings.startsWith("typeNames")).count();
                        if(typeNamesCount < 3 && typeNamesCount > 0) {
                            missingHeaderMap.entrySet().removeIf(entry -> entry.getKey().startsWith("typeNames"));
                        }
                    }else {
                        // 没有音乐类型，不去比较 一般音乐、背景音乐、广告音乐 三个表头
                        missingHeaderMap.entrySet().removeIf(entry -> entry.getKey().startsWith("typeNames"));
                    }
                    if (missingHeaderMap.isEmpty()) {
                        logger.info("默認表頭全部匹配成功【必须】({})", checkTypeNames ? "包含音乐类型表头且检测到其中一种音乐类型表头" : "不含音乐类型");
                        flag = true;
                    }
                }

                listFileQueue.setExtJson(JSON.toJSONString(extJson));
                if (flag) {
                    classifyMap(resultMap, listFileQueue, DONE);
                } else {
                    logBuilder.append("第").append(line).append("-").append(endLine).append("行標題匹配失敗;").append(CommonUtils.LINE_BREAK);
                    logBuilder.append("\t\t自定義標題（").append(looseParseMode ? "寬鬆" : "嚴格").append("模式）： ").append(StringUtils.join(",", customHeaders)).append(CommonUtils.LINE_BREAK);
                    logBuilder.append("\t\t默認標題： ").append(StringUtils.join(JSON.toJSONString(configHeaderMap2))).append(CommonUtils.LINE_BREAK);
                    listFileQueue.setDescription(logBuilder.toString());
                    classifyMap(resultMap, listFileQueue, ERROR);
                }

            } catch (Exception e) {
                logger.error("處理失敗！ {}", e);
                listFileQueue.setDescription(e.getMessage());
                classifyMap(resultMap, listFileQueue, ERROR);
                e.printStackTrace();
            }
        }
    }



    /**
     * 没有匹配到模板的文件，只去校验默认里的必填字段
     *
     * @param listFileQueues
     * @param resultMap
     * @param key
     */
    private void checkDefaultTitle(List<List2FileQueue> listFileQueues, Map<String, List<List2FileQueue>> resultMap, String key) {
        //默認header && 必须检测字段（behoove = 1）
        Map<String, String[]> configHeaderMap2 = new HashMap<>();
        List<List2BasicFileConfig> allList = listBasicFileConfigService.getAllList();
        List<String> defaultHeaders = allList.stream().filter(x -> x.getBehoove() == 1).map(List2BasicFileConfig::getKey).collect(Collectors.toList());
        UsageListParsing.UsageListParseOptions usOpts = new UsageListParsing.UsageListParseOptions();
        Class<? extends UsageListParsing.UsageListParseOptions> clz = usOpts.getClass();
        Field[] fields = clz.getDeclaredFields();
        for (Field field : fields) {
            for (String header : defaultHeaders) {
                if (field.getName().equals(header)) {
                    // 设置访问权限
                    field.setAccessible(true);
                    try {
                        String[] value = (String[]) field.get(usOpts);
                        configHeaderMap2.put(header, value);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        //比对是否包含必要的字段
        for (List2FileQueue listFileQueue : listFileQueues) {
            //獲取標題
            String filePath = listFileQueue.getFilePath();
            File file = new File(filePath);
            if (file.getName().startsWith("~$")) {
                //临时文件不处理，跳过
                continue;
            }
            if (!file.exists()) {
                logger.info("文件【】不存在！", filePath);
                listFileQueue.setDescription("文件不存在！");
                continue;
            }
            logger.info("open file: {}", filePath);
            try {
                Boolean flag = false;
                //从开始行匹配扫描6行，查出表头
                Integer line = 1;
                Integer endLine = line;
                StringBuilder logBuilder = new StringBuilder(); //記錄日志，用於寫入ERROR類別中，查找原因
                LinkedHashMap<Integer, List<String>> titlesMap = getTitles(file, line, filePath);

                if (Objects.isNull(titlesMap) || titlesMap.isEmpty()) {
                    logBuilder.append("當前沒有掃描到表頭，請確認文件内容是否正確。PS: 處理文件時，請勿打開文件，防止程序讀取不到！").append(CommonUtils.LINE_BREAK);
                    listFileQueue.setDescription(logBuilder.toString());
                    classifyMap(resultMap, listFileQueue, ERROR);
                    continue;
                }
                logger.info("默認表頭 titles : {}", JSON.toJSONString(configHeaderMap2));
                //自定義表頭匹配成功再去匹配默認表頭
                Map<String, String[]> missingHeaderMap = BeanCopyUtil.copyImplSerializable(configHeaderMap2);
                boolean checkTypeNames = false; // 校验 音乐类型 - （一般，背景，广告），由于改接口
                for (Integer row : titlesMap.keySet()) {
                    //一行一行开始比较, row
                    List<String> titles = titlesMap.get(row);
                    endLine = row + 1;
                    titles = titles.stream().map(x -> CommonUtil.ChineseNormalizer.doNormalize(x).replaceAll(" ","")).collect(Collectors.toList());
                    logger.info("第{}行，文件表頭  titles : {}", endLine, JSON.toJSONString(titles));
                    for (String headerKey : configHeaderMap2.keySet()) {
                        if (!checkTypeNames && titles.contains("音乐类型") ) { // 可能不是合并表头，是单表头，例：音乐类型-一般音乐，这个就不比较，缺失即缺失，不管
                            checkTypeNames = true;
                        }
                        String[] headerValues = configHeaderMap2.get(headerKey);
                        for (String headerValue : headerValues) {
                            if (titles.contains(CommonUtil.ChineseNormalizer.doNormalize(headerValue))) {
                                missingHeaderMap.remove(headerKey);
                                break;
                            }
                        }
                    }
                    if (missingHeaderMap.isEmpty()) {
                        //也包含默認header是空的情況
                        logger.info("第{}行，默認表頭全部匹配成功【必须】", endLine);
                        //把templateId寫入queue, 方便後續解析的時候不要再去匹配folder找到對應的List2BasicFileTemplate
                        flag = true;
                        break; //匹配成功跳出
                    } else {
                        logger.info("第{}行，默認表頭與文件表頭 匹配失敗", endLine);
                    }
                }
                if (!missingHeaderMap.isEmpty()) {
                    if(checkTypeNames) {
                        // 只有表头有 音乐类型 的时候才去匹配 一般音乐、背景音乐、广告音乐 这三个表头。（但是有错位的情况，暂时不考虑，如把这三个音乐类型放在了音乐来源里，也有音乐来源，也能匹配上，主要匹配是根据这三个表头来对应的，问题不大）
                        // 只要有一种音乐类型的表头即可
                        long typeNamesCount = missingHeaderMap.keySet().stream().filter(strings -> strings.startsWith("typeNames")).count();
                        if(typeNamesCount < 3 && typeNamesCount > 0) {
                            missingHeaderMap.entrySet().removeIf(entry -> entry.getKey().startsWith("typeNames"));
                        }
                    }else {
                        // 没有音乐类型，不去比较 一般音乐、背景音乐、广告音乐 三个表头
                        missingHeaderMap.entrySet().removeIf(entry -> entry.getKey().startsWith("typeNames"));
                    }
                    if (missingHeaderMap.isEmpty()) {
                        logger.info("默認表頭全部匹配成功【必须】({})", checkTypeNames ? "包含音乐类型表头且检测到其中一种音乐类型表头" : "不含音乐类型");
                        flag = true;
                    }
                }
                if (flag) {
                    classifyMap(resultMap, listFileQueue, NO_CATEGORY);
                } else {
                    logBuilder.append("第").append(line).append("-").append(endLine).append("行表頭匹配失敗; ").append(CommonUtils.LINE_BREAK);
                    logBuilder.append("\t\t缺失的默認表頭：").append(StringUtils.join(JSON.toJSONString(missingHeaderMap))).append(CommonUtils.LINE_BREAK);
                    listFileQueue.setDescription(logBuilder.toString());
                    classifyMap(resultMap, listFileQueue, ERROR);
                }
            } catch (Exception e) {
                logger.error("處理失敗！ {}", e);
                listFileQueue.setDescription(e.getMessage());
                classifyMap(resultMap, listFileQueue, ERROR);
                e.printStackTrace();
            }
        }


    }


    @Override
    public List<List2BasicFileTemplate> getByCategoryCode(String categoryCode) {
        Example example = new Example(List2BasicFileTemplate.class);
        Example.Criteria createCriteria = example.createCriteria();
        createCriteria.andEqualTo("categoryCode", categoryCode);
        return list2BasicFileTemplateMapper.selectByExample(example);
    }


    @Override
    public Map<String, Object> getAllMapById(Long templateId) {
        Map<String, Object> result = new HashMap<>();
        //TODO
        List2BasicFileTemplate template = this.getById(templateId);
        if (Objects.nonNull(template)) {
            int startLine = template.getLine() - 1; //代码操作的excel其实行从0开始;
            result.put("startLine", startLine < 0 ? 0 : startLine);
            List2BasicFileTemplateConfig config = new List2BasicFileTemplateConfig();
            config.setTemplateId(template.getId());
            List<ListBasicFileTemplateConfigVo> list = list2BasicFileTemplateConfigService.getList(config);
            list.forEach(x -> {
                result.put(x.getListBasicFileConfigKey(), x.getHeader());
            });
        }
        return result;
    }

    @Override
    public void checkTemplate(List2FileQueue fileQueue) {
        String categoryCode = fileQueue.getCategoryCode();
        String filePath = fileQueue.getFilePath().replace("\\", "/");
        filePath = CommonUtil.ChineseNormalizer.doNormalize(filePath);
        if (StringUtils.isNotBlank(categoryCode) && StringUtils.isNotBlank(filePath)) {
            List<List2BasicFileTemplate> list2BasicFileTemplateList = getByCategoryCode(categoryCode);
            for (List2BasicFileTemplate template : list2BasicFileTemplateList) {
                String folder = template.getFolder().replace("\\", "/");
                if (filePath.indexOf(CommonUtil.ChineseNormalizer.doNormalize(folder)) > 0) {
                    Map<String, Object> extJson = new HashMap<>();
                    extJson.put("templateId", template.getId());
                    fileQueue.setExtJson(JSON.toJSONString(extJson));
                    break;
                }
            }
        }
    }

    @Override
    public boolean checkTitle(List2FileQueue lfq) {
        String templateId = null;
        try {
            templateId = JsonCode.getValue(lfq.getExtJson(), "$.templateId");
        } catch (Exception e) {
            logger.error("extJSON: {}", lfq.getExtJson());
            e.printStackTrace();
        }
        Map<String, List<List2FileQueue>> resultMap = new HashMap<>();
        if (StringUtils.isNotBlank(templateId)) {
            this.checkTemplateTitle(new ArrayList<>(Arrays.asList(lfq)), resultMap, templateId);
        } else {
            this.checkDefaultTitle(new ArrayList<>(Arrays.asList(lfq)), resultMap, NO_CATEGORY);
        }
        if (resultMap.containsKey(ERROR)) {
            return false;
        }
        return true;
    }

    public static void main(String[] args) {
        List<String> st = new ArrayList<>(Arrays.asList("你好", "李焕英", "沈腾"));
        System.out.println(st.toString());
        System.out.println(st.toString().indexOf("沈"));
    }


    @Override
    @Transactional
    public Result<String> checkAndClassification(String filePath) {
        int code = HttpStatus.OK.value();
        String data = null;
        String message = "";
        String serverFilePath;
        logger.info("登陆用户：{}", LoginUtil.getUserName());

        // 根据路径格式判断是否需要转换
        // 如果是Linux格式路径（以/开头），需要通过路径映射转换
        // 如果是Windows格式路径，直接使用
        if (CommonUtils.isWindows()) {
            // Windows格式路径，直接使用
            serverFilePath = filePath;
            logger.info("Windows格式路径直接使用：{}", filePath);
        } else {
            // Linux格式路径，需要转换为服务器路径
            serverFilePath = listFilePathChangeService.getSeverPathByLikeFilePath(filePath);
            logger.info("Linux格式路径转换：{} -> {}", filePath, serverFilePath);
        }
        if (StringUtils.isNotBlank(serverFilePath)) {
            Map<String, List<List2FileQueue>> classificationResult = new HashMap<>(); //兩種key{id, NO_CATEGORY}
            Map<String, List<List2FileQueue>> doneResult = new HashMap<>(); //只允許只有三種key{ NO_CATEGORY, ERROR, DONE}

            Map<String, List<List2BasicFileTemplate>> headerMap = this.getAllMap(); //模板 folder -> it
            Map<String, String> idCategoryCodeMap = new HashMap<>(); // id -> categoryCode
            headerMap.keySet().stream().forEach(x -> {
                List<List2BasicFileTemplate> templates = headerMap.get(x);
                templates.forEach(template ->
                        idCategoryCodeMap.put(String.valueOf(template.getId()), template.getCategoryCode())
                );

            });
            File file = new File(serverFilePath);
            File[] files = file.listFiles();
            String finalServerFilePath = file.getAbsolutePath();
            if (Objects.isNull(files) || files.length == 0) {
                code = HttpStatus.BAD_REQUEST.value();
                message = String.format("該目錄【%s】下無待處理文件！", filePath);
            } else {
                Map<String, List<List2FileQueue>> checkTitleResult = new HashMap<>();  // id -> List
                //1.分類
                this.recursionDeal(finalServerFilePath, file, headerMap, classificationResult);
                //校驗category是否存在
                for (String categoryOrId : classificationResult.keySet()) {
                    List<List2FileQueue> queues = classificationResult.get(categoryOrId);
                    queues.forEach(q -> {
                        q.setFinalServerFilePath(finalServerFilePath);
                        q.setInputFilePath(filePath);
                        q.setUploadUserName(LoginUtil.getUserName());
                        q.setUploadUserId(LoginUtil.getUserId());
                    });
                    //上一個方法返回的classificationResult中的key只有可能是NO_CATEGROY 或者 ID
                    if (StringUtils.equals(categoryOrId, NO_CATEGORY)) {
//                        queues.stream().forEach(x -> x.setCategoryCode(""));
//                        classifyMap(doneResult, queues, NO_CATEGORY);
                        checkTitleResult.put(NO_CATEGORY, queues);
                    } else {
                        //前面限制住了，配置模板的時候category不存在，不給寫入
                    /*String categoryCode = idCategoryCodeMap.get(categoryOrId); //這個不會爲空
                    if (StringUtils.isNotBlank(categoryCode) && isCheckCategory(categoryCode)) {
                        checkTitleResult.put(categoryOrId, queues);
                        continue;
                    } else {
                        queues.stream().forEach(x -> x.setCategoryCode(String.format("未配置【%s】", categoryCode)));
                    }*/
                        checkTitleResult.put(categoryOrId, queues);
                    }

                }

                this.concurrentLinkedQueue.add(checkTitleResult) ;

                String flag = redisService.getValue(CHECK_AND_CLASSIFICATION_FLAG) ;
                if(flag.equals("1")){
                    message = String.format("当前有目錄正在執行，該目錄【%s】已加入排隊，等待執行，處理結果請在當前目錄下查看log日志！", filePath);
                    return new Result<>(code, message, data);
                } else {
                    redisService.setValue(CHECK_AND_CLASSIFICATION_FLAG,"1");
                }

                CompletableFuture.runAsync(() -> {
                    while(true){
                        if(this.concurrentLinkedQueue.isEmpty()){
                            break;
                        }

                        logger.info("开始执行:concurrentLinkedQueue.size():" + concurrentLinkedQueue.size());
                        Map<String, List<List2FileQueue>> listFileQueueMap = this.concurrentLinkedQueue.poll();
                        //2.校驗並再次分類
                        if (!listFileQueueMap.isEmpty()) {
                            doneResult.putAll(this.checkTitle(listFileQueueMap));
                        }

                        //3.複製文件並上傳
                        String logName = String.format("log_%s.txt", DateParse.format(new Date(), DateParse.patternFile));
                        for (String key : doneResult.keySet()) {
                            List<List2FileQueue> queues = doneResult.get(key);
                            File loggerFile = null;
                            FileWriter fileWriter = null;
                            String _finalServerFilePath = null ;
                            for (List2FileQueue queue : queues) {
                                try {
                                    if (StringUtils.startsWith(queue.getFileName(), "~$")) {
                                        //临时文件，统一不上传不处理;
                                        continue;
                                    }
                                    //生成新路徑並複製文件
                                    String path = queue.getFilePath();
                                    _finalServerFilePath = queue.getFinalServerFilePath();
                                    String substring = "";
                                    if (_finalServerFilePath.length() < path.lastIndexOf(File.separator)) {
                                        logger.debug("serverFilePaht : {}", _finalServerFilePath);
                                        logger.debug("path : {}", path);
                                        substring = path.substring(_finalServerFilePath.length(), path.lastIndexOf(File.separator));
                                    }
                                    String destFilePath = _finalServerFilePath + File.separator + key + File.separator + substring;
                                    if (null == loggerFile) {
                                        loggerFile = new File(_finalServerFilePath + File.separator + logName);
                                        logger.info("日志路径： {}", loggerFile.getAbsolutePath());
                                    }
                                    fileWriter = new FileWriter(loggerFile, true);
                                    String originalPath = path.replace(_finalServerFilePath, queue.getInputFilePath());
                                    fileWriter.write(String.format("處理文件【%s】;%s", originalPath, CommonUtils.LINE_BREAK));
                                    String destFile = CommonUtils.copyFile(path, destFilePath);
                                    fileWriter.write(String.format("\t文件複製成功！%s;%s", destFile, CommonUtils.LINE_BREAK));
                                    queue.setFilePath(destFile);
                                    //上傳， ERROR目錄，無需入庫
                                    if (StringUtils.equals(key, ERROR)) {
                                        fileWriter.write(String.format("\t\t错误！%s;%s", queue.getDescription(), CommonUtils.LINE_BREAK));
                                        queue.setStatus(3);
                                        queue.setDescription(String.format("上傳校驗失敗；%s", queue.getDescription()));
                                    } else {
                                        queue.setStatus(0);
                                    }
                                    List2FileQueue existQueue = listFileQueueService.getListFileQueueByFilePathMd5(DigestUtils.md5DigestAsHex(queue.getFilePath().getBytes()));
                                    if (Objects.nonNull(existQueue)) {
                                        fileWriter.write(String.format("\t文件已经入库过！id【%d】，uploadTime【%s】%s", existQueue.getId(),
                                                DateParse.format(existQueue.getCreateTime(), DateParse.patterDateFormat), CommonUtils.LINE_BREAK + CommonUtils.LINE_BREAK));
                                    } else {
                                        queue = listFileQueueService.uploadWithoutStatus(queue);
                                        fileWriter.write(String.format("\t文件入库成功！id【%d】%s", queue.getId(), CommonUtils.LINE_BREAK + CommonUtils.LINE_BREAK));
                                    }
                                } catch (Exception e) {
                                    //e.printStackTrace();
                                    logger.error("處理失敗：{}", e);
                                    try {
                                        fileWriter.write(String.format("\t\t\tERROR: %s, %s, %s;;;%s", e.getMessage(), _finalServerFilePath, queue.getFilePath(), CommonUtils.LINE_BREAK + CommonUtils.LINE_BREAK));
                                    } catch (IOException e2) {
                                        e2.printStackTrace();
                                    }
                                } finally {
                                    try {
                                        fileWriter.flush();
                                        fileWriter.close();
                                    } catch (IOException e) {
                                        logger.error("處理失敗：{}", e);
                                    }
                                }
                            }
                        }
                    }

                    redisService.setValue(CHECK_AND_CLASSIFICATION_FLAG,"0");
                });
                message = "開始處理，處理結果請在當前目錄下查看log日志！";
                logger.info("开始执行:concurrentLinkedQueue.size():" + concurrentLinkedQueue.size());

            }
        } else {
            code = HttpStatus.BAD_REQUEST.value();
            message = String.format("输入错误： filePath【%s】找不到对应的共享路径", filePath);
        }

        return new Result<>(code, message, data);
    }

    /**
     * 检验categroyCode是否存在
     *
     * @param categoryCode
     * @return
     */
    private boolean isCheckCategory(String categoryCode) {
        ListCategory categoryByCode = listCategoryService.getCategoryByCode(categoryCode);
        if (null != categoryByCode) {
            return true;
        }
        return false;


    }

    /**
     * 校驗categorycode是否存在
     *
     * @param categoryCode
     * @param categoryMap
     * @return
     */
    private String isCheckCategory(String categoryCode, Map<String, String> categoryMap) {
        String result = "";
        if (StringUtils.isNotBlank(categoryCode)) {
            if (categoryMap.containsKey(categoryCode)) {
                result = categoryMap.get(categoryCode);
            } else {
                ListCategory categoryByCode = listCategoryService.getCategoryByCode(categoryCode);
                if (null != categoryByCode) {
                    result = categoryByCode.getCategoryCode();
                } else {
                    result = String.format("未配置【%s】該categoryCode", categoryCode);
                }
                categoryMap.put(categoryCode, result);
            }
        } else {
            result = "未配置categoryCode";
        }
        return result;

    }

    protected void classifyMap(Map<String, List<List2FileQueue>> doneResult, List2FileQueue newQueue, String key) {
        List<List2FileQueue> queues = new ArrayList<>();
        if (doneResult.containsKey(key)) {
            queues = doneResult.get(key);
        }
        queues.add(newQueue);
        doneResult.put(key, queues);
    }

    protected void classifyMap(Map<String, List<List2FileQueue>> doneResult, List<List2FileQueue> newQueues, String key) {
        List<List2FileQueue> queues = new ArrayList<>();
        if (doneResult.containsKey(key)) {
            queues = doneResult.get(key);
        }
        queues.addAll(newQueues);
        doneResult.put(key, queues);
    }

    protected String getUploadType(String absolutePath){
        String uploadType = "FW" ;
        List<String> paths = Arrays.asList(absolutePath.split("/")) ;
        if(paths.contains("CJ")){
            uploadType = "CJ";
        }else if(paths.contains("PG")){
            uploadType = "PG";
        }else if(paths.contains("MS")){
            uploadType = "MS";
        }else if(StringUtils.contains(absolutePath,"广告")){
            uploadType = "CJ";
        }else if(StringUtils.contains(absolutePath, "影集")){
            uploadType = "PG";
        }else if(StringUtils.containsAny(absolutePath,"MV","KTV")) {
            uploadType = "FW";
        }else if(StringUtils.contains(absolutePath, "01-TV")){
            uploadType = "MS";
        }
        return uploadType;
    }

    public LinkedHashMap<Integer, List<String>> getTitles(File file, Integer line, String filePath ){

        if(filePath.indexOf("KKBOX") > -1 && file.getName().toLowerCase().endsWith(".xlsx")){
            LinkedHashMap<Integer, List<String>> titlesMap = new LinkedHashMap<>();
            try{
                ExcelReaderUtil.readExcel(filePath, new IRowReader(){
                    @Override
                    public void sendRow(String sheetName, int curRow, List<String> celllist) {
                        if(curRow <= MATCH_LINES && curRow >= line - 1){
                            List<String> list = new ArrayList<>();
                            list.addAll(celllist);
                            titlesMap.put(curRow,list.stream().map(x -> x = x.replaceAll("(\\r\\n|\\n|\\n\\r)", " ")).collect(Collectors.toList()));
                        }
                        if(curRow > MATCH_LINES){
                            throw new MustException(String.format("sheetName：%s, 未匹配到的表頭：%s", sheetName));
                        }
                    }
                });
            }catch (Exception e){

            }
            return titlesMap ;
        } else {
            return ExcelUtil.getTitles(file, line, MATCH_LINES);
        }
    }



}