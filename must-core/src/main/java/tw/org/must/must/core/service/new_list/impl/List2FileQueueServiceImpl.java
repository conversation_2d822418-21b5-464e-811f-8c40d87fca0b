package tw.org.must.must.core.service.new_list.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.common.base.Page;
import tw.org.must.must.common.util.CommonUtils;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.common.util.ExampleUtil;
import tw.org.must.must.common.util.Md5;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.core.handle.redisService.RedisService;
import tw.org.must.must.core.service.claim.ClaimSetInfoService;
import tw.org.must.must.core.service.file.FileReadService;
import tw.org.must.must.core.service.list.*;
import tw.org.must.must.core.service.new_list.*;
import tw.org.must.must.dto.list.List2FileQueueDeleteImpactDto;
import tw.org.must.must.model.new_list.*;
import tw.org.must.must.core.shiro.LoginUtil;
import tw.org.must.must.mapper.new_list.List2FileQueueMapper;
import tw.org.must.must.model.claim.ClaimSetInfo;
import tw.org.must.must.model.list.*;


import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class List2FileQueueServiceImpl extends BaseServiceImpl<List2FileQueue> implements List2FileQueueService {

    private static final Logger log = LoggerFactory.getLogger(List2FileQueueServiceImpl.class);

    private final List2FileQueueMapper listFileQueueMapper;

    @Autowired
    private ListFilePathChangeService listFilePathChangeService;

    @Lazy
    @Autowired
    private List2BasicFileBaseService listBasicFileBaseService;

    @Autowired
    private ListDspFileBaseService listDspFileBaseService;

    @Autowired
    private ClaimSetInfoService claimSetInfoService;

    @Autowired
    private FileReadService fileReadService;

    @Autowired
    private ListCategoryService listCategoryService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ListSourceService listSourceService;

    @Lazy
    @Autowired
    private List2BasicFileCombinedFileQueueService list2BasicFileCombinedFileQueueService;

    @Autowired
    private List2BasicFileCombinedTotalService list2BasicFileCombinedTotalService;

    @Autowired
    private List2BasicSampleDataMappingService list2BasicSampleDataMappingService;

    @Autowired
    private List2SampleRuleBaseService list2SampleRuleBaseService;

    @Autowired
    private List2SampleRuleBaseRelationService list2SampleRuleBaseRelationService;

    private static final String SINGLE_SESSION_KEY = "single_session_date";

    /**
     * list:
     * file:
     * path: C:\Users\<USER>\Desktop\文件
     */
    @Value("${list.file.path}")
    private String listFilePath;

    @Autowired
    public List2FileQueueServiceImpl(List2FileQueueMapper listFileQueueMapper) {
        super(listFileQueueMapper);
        this.listFileQueueMapper = listFileQueueMapper;
    }

    @Override
    public String upload(List2FileQueue lfq, MultipartFile[] files) {
        if (CommonUtils.isWindows()) {
            listFilePath = "E:\\Test";
        }
        String filePath = listFilePath + File.separator + new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        File parentFile = new File(filePath);
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        String fileName;
        File targetFile;
        try {
            for (MultipartFile file : files) {
                fileName = file.getOriginalFilename();
                StopWatch stopWatch2 = new StopWatch();
                stopWatch2.start("Md5");
                String targetFileName = Md5.getMd5ByInputStream(file.getInputStream());
                stopWatch2.stop();
                //System.out.println(stopWatch2.prettyPrint());
                if (StringUtils.isBlank(targetFileName))
                    return "文件读取异常";
                String fileExt = fileName.substring(fileName.lastIndexOf("."));
                targetFileName = targetFileName + fileExt;
                targetFile = new File(parentFile, Objects.requireNonNull(targetFileName));
                StopWatch stopWatch = new StopWatch();
                stopWatch.start("transferTo");
                file.transferTo(targetFile);
                stopWatch.stop();
                //System.out.println(stopWatch.prettyPrint());
                // 写入queue   写入之前先去查询是否存在
                String targetFilePath = targetFile.getAbsolutePath();
                String md5DigestAsHex = DigestUtils.md5DigestAsHex(targetFilePath.getBytes());
                List2FileQueue listFileQueue = getListFileQueueByFilePathMd5(md5DigestAsHex);
                if (null != listFileQueue && listFileQueue.getId() != null)
                    return "文件已存在，请重新上传！";
                lfq.setFileSize(targetFile.length());
                lfq.setFilePathMd5(md5DigestAsHex);
                lfq.setFilePath(targetFilePath);
                lfq.setFileName(fileName);
                lfq.setFileExt(fileExt);
                if ("C".equalsIgnoreCase(lfq.getFileType())) {
                    if ("TSV".equalsIgnoreCase(fileExt)) {
                        String checkFileType = fileReadService.checkFileType(targetFilePath);
                        if (StringUtils.isBlank(checkFileType))
                            return "文件内容有误！";
                    }
                }
                listFileQueueMapper.insertUseGeneratedKeys(lfq);
            }
            return "";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "文件上传失败！异常！";
    }

    /**
     * claim上传-目录上传
     *
     * @param categoryCode
     * @param claimSetInfoId
     * @param filePath
     * @return
     */
    @Override
    public String uploadCatalogFile(String categoryCode, Long claimSetInfoId, String filePath, String matchMark) {
        String result = "";
        // 根据输入的共享目录  查询到服务器上映射路径
        String serverPath = listFilePathChangeService.getSeverPathByLikeFilePath(filePath);
        boolean isRepeat = false; // 重复上传做个判断，其中只要有一个不重复的文件，就上传成功，反之，全部重复的就报错
        if (StringUtils.isNotBlank(serverPath)) {
            File parentFile = new File(serverPath);
            if (parentFile.isDirectory()) {
                File[] files = parentFile.listFiles();
                List<List2FileQueue> list = new ArrayList<>();
                for (File file : files) {
                    if (!file.isDirectory()) {
                        try {
                            List2FileQueue lfq = this.createListFileQueue(categoryCode, "C", claimSetInfoId, file);
                            lfq.setMatchMark(matchMark);
                            list.add(lfq);
                            isRepeat = true;
                        } catch (Exception e) {
                            result = "解析文件出錯： " + e.getMessage();
                        }
                    } else {
                        result = "服務器目錄下文件已處理，包含文件夾的文件暫時不處理！";
                    }
                }
                if (!list.isEmpty()) {
                    this.addList(list);
                }
            } else {
                result = "目錄出錯！";
            }
        } else {
            result = "上傳路徑找不到服務路徑！";
        }
        if (isRepeat) {
            result = "";
        }
        return result;
    }

    @Override
    public List2FileQueue uploadWithoutStatus(List2FileQueue queue) {
        File file = new File(queue.getFilePath());
        String fileName = file.getName();
        String fileType = "P2";
//        String uploadType = "節目";
        String sequence = "5";
        queue.init();
        queue.setFileType(fileType);
//        queue.setUploadType(uploadType);
        queue.setSequence(StringUtils.isBlank(sequence) ? 1 : Integer.valueOf(sequence));
       if(StringUtils.isBlank(queue.getUploadUserName())){
           queue.setUploadUserId(LoginUtil.getUserId());
           queue.setUploadUserName(LoginUtil.getUserName());
       }
        String fileExt = "";
        if (StringUtils.isNotBlank(fileName)) {
            fileExt = fileName.substring(fileName.lastIndexOf("."));
            queue.setFileName(fileName);
            queue.setFileExt(fileExt.toLowerCase());
        }
        queue.setFileSize(file.length());
        String filePath = file.getAbsolutePath();
        queue.setFilePathMd5(DigestUtils.md5DigestAsHex(filePath.getBytes()));
        queue.setFilePath(filePath);
        listFileQueueMapper.insertUseGeneratedKeys(queue);
        return queue;
    }

    @Override
    public int uploadList(List<List2FileQueue> uploadQueues) {
        String fileType = "P";
        String uploadType = "節目";
        String sequence = "5";
        for (List2FileQueue lfq : uploadQueues) {
            lfq.setAmendTime(new Date());
            lfq.setCreateTime(new Date());
            lfq.setFileType(fileType);
            lfq.setUploadType(uploadType);
            lfq.setSequence(StringUtils.isBlank(sequence) ? 1 : Integer.valueOf(sequence));
            lfq.setStatus(0);
            lfq.setUploadUserId(LoginUtil.getUserId());
            lfq.setUploadUserName(LoginUtil.getUserName());
            String fileExt = "";
            File targetFile = new File(lfq.getFilePath());
            String fileName = targetFile.getName();
            if (StringUtils.isNotBlank(fileName)) {
                fileExt = fileName.substring(fileName.lastIndexOf("."));
                lfq.setFileName(fileName);
                lfq.setFileExt(fileExt);
            }
            lfq.setFileSize(targetFile.length());
            String filePath = targetFile.getAbsolutePath();
            lfq.setFilePathMd5(DigestUtils.md5DigestAsHex(filePath.getBytes()));
            lfq.setFilePath(filePath);
        }
        return listFileQueueMapper.insertList(uploadQueues);
    }

    protected List2FileQueue createListFileQueue(String categoryCode, String fileType, Long claimSetInfoId, File file) throws Exception {
        List2FileQueue lfq = new List2FileQueue();
        String fileName = file.getName();
        String fileExt = fileName.substring(fileName.lastIndexOf("."));
        // 写入queue   写入之前先去查询是否存在
        String targetFilePath = file.getAbsolutePath();
        String md5DigestAsHex = DigestUtils.md5DigestAsHex(targetFilePath.getBytes());
        List2FileQueue listFileQueue = getListFileQueueByFilePathMd5(md5DigestAsHex);
        if (null != listFileQueue && listFileQueue.getId() != null) {
            throw new Exception("文件已存在，请重新上传！");
        } else {
            if ("TSV".equalsIgnoreCase(fileExt)) {
                String checkFileType = fileReadService.checkFileType(targetFilePath);
                if (StringUtils.isBlank(checkFileType))
                    throw new Exception("文件内容有误！");
            }
            lfq.setFileSize(file.length());
            lfq.setFilePathMd5(md5DigestAsHex);
            lfq.setFilePath(targetFilePath);
            lfq.setFileName(fileName);
            lfq.setFileExt(fileExt);

            lfq.setAmendTime(new Date());
            lfq.setCreateTime(new Date());
            lfq.setFileType(fileType);
            lfq.setSequence(1);
            lfq.setCategoryCode(categoryCode);
            lfq.setStatus(0);
            lfq.setUploadUserId(LoginUtil.getUserId());
            lfq.setUploadUserName(LoginUtil.getUserName());
            JSONObject object = new JSONObject();
            object.put("claimSetInfoId", claimSetInfoId);
            lfq.setExtJson(object.toString());
        }
        return lfq;
    }

    @Override
    public List2FileQueue getListFileQueueByFilePathMd5(String md5DigestAsHex) {
        if (StringUtils.isBlank(md5DigestAsHex))
            return null;
        Example example = new Example(List2FileQueue.class);
        Criteria createCriteria = example.createCriteria();
        createCriteria.andEqualTo("filePathMd5", md5DigestAsHex);
        return listFileQueueMapper.selectOneByExample(example);
    }

    private static final String SOURCE_NAME = "CON";


    @Override
    public ListCategory generateSingleSessionCategoryCode() {

        ListSource listSource = listSourceService.getBySourceName(SOURCE_NAME);
        if (Objects.isNull(listSource)) {
            throw new RuntimeException("沒有'CON'的source，不符合當前邏輯，請先添加該source！");
        }
        String format = DateParse.format(new Date(), DateParse.patternDate);
        String key = String.format("%s_%s", SINGLE_SESSION_KEY, format);
        String value = redisService.incr(key, 3);
        String categoryCode = String.format("%s%s", format, value);
        ListCategory addListCategory = new ListCategory();
        addListCategory.setCategoryCode(categoryCode);
        addListCategory.setCategoryDesc(categoryCode);
        addListCategory.setPoolCode("C");
        addListCategory.setPoolRight("PR");
        addListCategory.setSourceName(listSource.getSourceName());
        addListCategory.setListSourceId(listSource.getId());
        addListCategory.setDistType("P");
        addListCategory.setIsDist(1);
        addListCategory.setCreateUserId(LoginUtil.getUserId());
        addListCategory.setCreateUserName(LoginUtil.getUserName());
        ListCategory listCategory = null;
        try {
            listCategory = listCategoryService.addListCategory(addListCategory);
        } catch (MustException e) {
            if (e.getErrCode() == ResultCode.NAME_REPEAT.getCode()) {
                //redis和数据库不同步，再次自动生成执行
                listCategory = generateSingleSessionCategoryCode();
            } else {
                throw new RuntimeException(e.getMessage());
            }
        }
        return listCategory;
    }

    @Override
    public List<List2FileQueue> getParsedListFileQueueByFolder(String folder) {
        return getParsedListFileQueueByFolder(folder, null, null);
    }

    @Override
    public List<List2FileQueue> getParsedListFileQueueByFolder(String folder, Date combinedStartDate, Date combinedEndDate) {
        if (StringUtils.isBlank(folder)) {
            return new ArrayList<>();
        }
        Example example = new Example(List2FileQueue.class);
        Criteria createCriteria = example.createCriteria();
        createCriteria.andLike("filePath", ExampleUtil.exampleLikeAll(folder));
        createCriteria.andIn("status", Arrays.asList(2, 4));
        if (Objects.nonNull(combinedStartDate)) {
            createCriteria.andGreaterThanOrEqualTo("createTime", combinedStartDate);
        }
        if (Objects.nonNull(combinedEndDate)) {
            createCriteria.andLessThanOrEqualTo("createTime", combinedEndDate);
        }
        return listFileQueueMapper.selectByExample(example);
    }

    @Override
    public List<List2FileQueue> getListFileQueueListByUploadTime(Integer status, Date startDate, Date endDate) {
        if (null == startDate && null == endDate) {
            return new ArrayList<>();
        }
        Example example = new Example(List2FileQueue.class);
        Criteria createCriteria = example.createCriteria();
        if(null != status) {
            createCriteria.andEqualTo("status", 2);
        }
        if (Objects.nonNull(startDate)) {
            createCriteria.andGreaterThanOrEqualTo("createTime", startDate);
        }
        if (Objects.nonNull(endDate)) {
            createCriteria.andLessThanOrEqualTo("createTime", endDate);
        }
        return listFileQueueMapper.selectByExample(example);
    }


    public List2FileQueue getListFileQueueByFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return null;
        }
        Example example = new Example(List2FileQueue.class);
        Criteria createCriteria = example.createCriteria();
        createCriteria.andEqualTo("fileName", fileName);
        return listFileQueueMapper.selectOneByExample(example);
    }

    public List2FileQueue writeToListFileQueue(File targetFile, String fileName, String fileType, String categoryCode, String uploadType,
                                              String sequence) {
        List2FileQueue lfq = new List2FileQueue();
        lfq.init();
        lfq.setFileType(fileType);
        lfq.setUploadType(uploadType);
        lfq.setSequence(StringUtils.isBlank(sequence) ? 1 : Integer.valueOf(sequence));
        lfq.setCategoryCode(categoryCode);
        lfq.setStatus(0);
        lfq.setUploadUserId(LoginUtil.getUserId());
        lfq.setUploadUserName(LoginUtil.getUserName());
        String fileExt = "";
        if (StringUtils.isNotBlank(fileName)) {
            fileExt = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
            lfq.setFileName(fileName);
            lfq.setFileExt(fileExt);
        }
        lfq.setFileSize(targetFile.length());
        String filePath = targetFile.getAbsolutePath();
        lfq.setFilePathMd5(DigestUtils.md5DigestAsHex(filePath.getBytes()));
        lfq.setFilePath(filePath);
        listFileQueueMapper.insertUseGeneratedKeys(lfq);
        return lfq;
    }

    @Override
    public List<List2FileQueue> getAffectiveListFileQueue(String status, String fileType) {
        Example example = new Example(List2FileQueue.class);
        Criteria createCriteria = example.createCriteria();
        createCriteria.andEqualTo("status", status);
        if (StringUtils.isNotBlank(fileType)) {
            createCriteria.andEqualTo("fileType", fileType);
        } else {
            createCriteria.andNotEqualTo("fileType", "C");
        }
        example.orderBy("sequence").desc();
        return listFileQueueMapper.selectByExample(example);
    }

    /**
     * 目录上传已近合并到模板校验上传中，现在该目录只有单场次上传了，同时旧规则不在适用，有文件就上传，categoryCode統一是單場次
     *
     * @Deprecated 制定规则：根据上传的共享目录
     * 第一层目录名称为categoryCode
     * 第二层目录名称为：节目/FW/CJ/PG/MS
     * 第三层开始可能是文件or文件夹
     */
    @Override
    @Transactional
    public String upload(String fileType, String sequence, String filePath, Map<String, Integer> msgResult) throws Exception {
        String result = "";
        // 根据输入的共享目录  查询到服务器上映射路径
        String serverFilePath = listFilePathChangeService.getSeverPathByLikeFilePath(filePath);
        if (StringUtils.isNotBlank(serverFilePath)) {
            File file = new File(serverFilePath);
            File[] tempList = file.listFiles();

            for (File childFile : tempList) {
                uploadSingleSession(childFile, fileType, "單場次", msgResult);
            }
            /*
            // 第一层
            for (int i = 0; i < tempList.length; i++) {
                File firstChildFile = tempList[i];
                String categoryCode = firstChildFile.getName();
                if (firstChildFile.isDirectory()) {
                    //文件名，不包含路径
                    File[] secondChildFileList = firstChildFile.listFiles();
                    //第二层
                    for (int j = 0; j < secondChildFileList.length; j++) {
                        File secondChildFile = secondChildFileList[j];
                        String uploadType = secondChildFile.getName();
                        if (secondChildFile.isDirectory()) {
                            File[] lastFileList = secondChildFile.listFiles();
                            for (int z = 0; z < lastFileList.length; z++) {
                                File lastFile = lastFileList[z];
                                List<File> list = this.getAllFile(lastFile);
                                list.forEach(it -> {
                                    String fileName = it.getName();
                                    // 写入queue
                                    writeToListFileQueue(it, fileName, fileType, categoryCode, uploadType, sequence);
                                });
                            }
                            result = "目录上传成！";
                        } else {
                            result = "二级目录层级关系出错！";
                            return result;
                        }
                    }
                } else {
                    result = "一级目录层级关系出错！";
                    return result;
                }
            }*/
        } else {
            result = "共享目錄不存在，請先配置！";
            return result;
        }
        return result;
    }

    private void uploadSingleSession(File file, String fileType, String categoryCode, Map<String, Integer> result) {
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File file1 : files) {
                uploadSingleSession(file1, fileType, categoryCode, result);
            }
        } else {
            try {
                String fleName = file.getName();
                if (fleName.startsWith("~$")) {
                    // 临时文件过滤
                    return;
                }
                writeToListFileQueue(file, fleName, fileType, categoryCode, "FW", "1");
                result.put("succ", result.getOrDefault("succ", 0) + 1);
            } catch (Exception e) {
                if (e instanceof DuplicateKeyException) {
                    result.put("err", result.getOrDefault("err", 0) + 1);
                } else {
                    throw new RuntimeException(e.getMessage());
                }
            }
        }
    }

    protected List<File> getAllFile(File file) {
        List<File> result = new ArrayList<>();
        if (null != file) {
            if (file.isDirectory()) {
                File[] files = file.listFiles();
                for (File f : files) {
                    result.addAll(getAllFile(f));
                }
            } else {
                result.add(file);
            }
        }
        return result;
    }

    @Override
    public List<List2FileQueue> getAffectiveListFileQueueForYoutube(String status, Integer fileSource) {
        if (fileSource == null && StringUtils.isBlank(status))
            return new ArrayList<>();
        Example example = new Example(List2FileQueue.class);
        Criteria createCriteria = example.createCriteria();
        if (null != fileSource)
            createCriteria.andEqualTo("fileSource", fileSource);
        if (StringUtils.isNotBlank(status))
            createCriteria.andEqualTo("status", status);
        example.orderBy("sequence").desc();
        return listFileQueueMapper.selectByExample(example);
    }

    @Override
    public Integer updateStatusById(List2FileQueue listqueue) {
        if (null == listqueue || listqueue.getId() == null) {
            return -1;
        }
        List2FileQueue fileQueue = new List2FileQueue();
        fileQueue.setId(listqueue.getId());
        fileQueue.setStatus(listqueue.getStatus());
        fileQueue.setDescription(listqueue.getDescription());
        return listFileQueueMapper.updateByPrimaryKeySelective(fileQueue);
    }

    @Override
    public List<List2FileQueue> getListFileQueueList(Long id, String categoryCode, String fileName, String fileUploadStartDate,
                                                    String fileUploadEndDate, String uploadType, Integer status, String fileType, Integer isNoCategory, String filePath) {
        Example example = new Example(List2FileQueue.class);
        Criteria createCriteria = example.createCriteria();

        if (Objects.nonNull(id) && id > 0L) {
            createCriteria.andEqualTo("id", id);
        } else {
            if (StringUtils.isNotBlank(categoryCode)) {
                createCriteria.andEqualTo("categoryCode", categoryCode);
            }
            if (StringUtils.isNotBlank(fileName)) {
                createCriteria.andLike("fileName", ExampleUtil.exampleLikeAll(fileName));
            }
            if (StringUtils.isNotBlank(fileUploadStartDate)) {
                createCriteria.andGreaterThan("createTime", fileUploadStartDate);
            }
            if (StringUtils.isNotBlank(fileUploadEndDate)) {
                createCriteria.andLessThan("createTime", fileUploadEndDate);
            }
            if (StringUtils.isNotBlank(uploadType)) {
                createCriteria.andLike("uploadType", ExampleUtil.exampleLikeAll(uploadType));
            }
            if (null != status) {
                createCriteria.andEqualTo("status", status);
            }
            if (StringUtils.equalsIgnoreCase("PS", fileType)) {
                createCriteria.andNotEqualTo("fileType", "C");
            } else {
                createCriteria.andEqualTo("fileType", fileType);
            }
            if (Objects.nonNull(isNoCategory)) {
                createCriteria.andEqualTo("isNoCategory", isNoCategory);
            }
            if (StringUtils.isNotBlank(filePath)) {
                createCriteria.andLike("filePath", ExampleUtil.exampleLikeAll(filePath.trim()));
            }
        }

        example.orderBy("createTime").desc();

        List<List2FileQueue> list = listFileQueueMapper.selectByExample(example);

        List<ListFilePathChange> listFilePathChanges = listFilePathChangeService.listAll();
        List<String> serverFilePaths = listFilePathChanges.stream().map(ListFilePathChange::getServerFilePath).collect(Collectors.toList());
        for (List2FileQueue listFileQueue : list) {
            String extJson = listFileQueue.getExtJson();
            String fileQueueFilePath = listFileQueue.getFilePath();
            serverFilePaths.forEach(x -> {
                if (fileQueueFilePath.contains(x)) {
                    listFileQueue.setFilePath(fileQueueFilePath.replace(x, ""));
                    return;
                }
            });
            if (StringUtils.isNotBlank(extJson)) {
                JSONObject fromObject = JSONObject.fromObject(extJson);
                if (fromObject.containsKey("claimSetInfoId")) {
                    Long claimSetInfoId = fromObject.getLong("claimSetInfoId");
                    if (null != claimSetInfoId) {
                        ClaimSetInfo claimSetInfo = claimSetInfoService.getById(claimSetInfoId);
                        if (null != claimSetInfo) {
                            String company = claimSetInfo.getCompany();
                            listFileQueue.setCompany(company);
                        }
                    }
                }
            }
        }

        return list;
    }

    @Override
    public PageInfo<List2FileQueue> getListFileQueueList2(Page page, Long id, String categoryCode, String fileName, String fileUploadStartDate,
                                                         String fileUploadEndDate, String uploadType, Integer status, String fileType, Integer isNoCategory,
                                                         String filePath, String usageTime) {
        if(null != id) {
            List2FileQueue queue = this.getById(id);
            return new PageInfo<>(Arrays.asList(queue), 1);
        }else if (StringUtils.isBlank(categoryCode)) {
            PageHelper.startPage(page.getPageNum(), page.getPageSize());
            List<List2FileQueue> list = this.getListFileQueueList(null, null, fileName, fileUploadStartDate, fileUploadEndDate, uploadType, status, fileType, isNoCategory, filePath);
            return new PageInfo<>(list);
        }else {
            // categoryCode不为空的情况下，需要左连接查询 base表
            // 根据单场次catgegoryCode 唯一的情况下，对应的queue其实只有一条。无需左连接查询

            // 当前base表 又分一般清单的，claim清单的，海外清单的（好像没公用该接口）
            PageHelper.startPage(page.getPageNum(), page.getPageSize());
            List<List2FileQueue> list = new ArrayList<>();
            if(!"C".equalsIgnoreCase(fileType)) {
                list = listFileQueueMapper.getListFileQueueList(categoryCode, fileName, fileUploadStartDate, fileUploadEndDate, uploadType, status, fileType, isNoCategory, filePath, usageTime);
            }else {
                // claim清单的
                list = listFileQueueMapper.getListFileQueueListWithClaimBase(categoryCode, fileName, fileUploadStartDate, fileUploadEndDate, uploadType, status, fileType, isNoCategory, filePath, usageTime);
            }

            // 补充其他一些相关信息
            List<ListFilePathChange> listFilePathChanges = listFilePathChangeService.listAll();
            List<String> serverFilePaths = listFilePathChanges.stream().map(ListFilePathChange::getServerFilePath).collect(Collectors.toList());
            for (List2FileQueue listFileQueue : list) {
                String extJson = listFileQueue.getExtJson();
                String fileQueueFilePath = listFileQueue.getFilePath();
                serverFilePaths.forEach(x -> {
                    if (fileQueueFilePath.contains(x)) {
                        listFileQueue.setFilePath(fileQueueFilePath.replace(x, ""));
                        return;
                    }
                });
                if (StringUtils.isNotBlank(extJson)) {
                    JSONObject fromObject = JSONObject.fromObject(extJson);
                    if (fromObject.containsKey("claimSetInfoId")) {
                        Long claimSetInfoId = fromObject.getLong("claimSetInfoId");
                        if (null != claimSetInfoId) {
                            ClaimSetInfo claimSetInfo = claimSetInfoService.getById(claimSetInfoId);
                            if (null != claimSetInfo) {
                                String company = claimSetInfo.getCompany();
                                listFileQueue.setCompany(company);
                            }
                        }
                    }
                }
            }
            return new PageInfo<>(list);
        }
    }

    @Override
    public List<List2FileQueue> getListFileQueueByIds(List<String> ids) {
        Example example = new Example(List2FileQueue.class);
        Criteria createCriteria = example.createCriteria();
        createCriteria.andIn("id",ids);

        return listFileQueueMapper.selectByExample(example);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Integer deleteListFileQueue(Long id) {

        List2FileQueue listFileQueue = this.getById(id);
        if (Objects.isNull(listFileQueue)) {
            return 0;
        }
        listFileQueueMapper.deleteByPrimaryKey(id);
        //根据FileQueueId删除对应base数据
        if (StringUtils.equalsAnyIgnoreCase(listFileQueue.getFileType(), "S", "P")) {
            List<List2BasicFileBase> listBasicFileBaseList = listBasicFileBaseService.getListBasicFileBaseList(id, null, null, null);
            if (!listBasicFileBaseList.isEmpty()) {
                listBasicFileBaseList.stream().forEach(x -> x.setDeleted(1));
                listBasicFileBaseService.updateBatchByPrimaryKeySelective(listBasicFileBaseList);
            }
            // 单场次清单与categoryCode是一一对应的，删除相对应的list_categoryCode
            if(StringUtils.equalsIgnoreCase(listFileQueue.getFileType(), "S")) {
                List<String> categoryCodeList = listBasicFileBaseList.stream().map(List2BasicFileBase::getCategoryCode).distinct().collect(Collectors.toList());
                listCategoryService.deleteByCategoryCodes(categoryCodeList);
            }
        } else if (StringUtils.equalsIgnoreCase(listFileQueue.getFileType(), "C")) {
            List<ListDspFileBase> listDspFileBaseList = listDspFileBaseService.getListDspFileBaseByQueueId(id);
            if (!listDspFileBaseList.isEmpty()) {
                listDspFileBaseList.stream().forEach(x -> x.setDeleted(1));
                listDspFileBaseService.updateBatchByPrimaryKeySelective(listDspFileBaseList);
            }
        }
        return 1;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Integer deleteListFileQueueNew(Long id) {
        if (id == null) {
            return 0;
        }

        try {
            log.info("开始执行P2类型文件队列删除，ID: {}", id);

            // 1. 根据list2_file_queue表的id查询获取list2_basic_file_base表中对应的记录
            List<List2BasicFileBase> list2BasicFileBaseList = listBasicFileBaseService.getListBasicFileBaseList(id, null, null, null);
            if (list2BasicFileBaseList == null || list2BasicFileBaseList.isEmpty()) {
                log.info("未找到对应的list2_basic_file_base记录，file_queue_id: {}", id);
                // 直接删除list2_file_queue记录
                listFileQueueMapper.deleteByPrimaryKey(id);
                return 1;
            }

            log.info("找到 {} 条list2_basic_file_base记录", list2BasicFileBaseList.size());

            // 2. 对查询到的list2_basic_file_base记录执行软删除操作
            for (List2BasicFileBase fileBase : list2BasicFileBaseList) {
                fileBase.setDeleted(1);
                listBasicFileBaseService.updateSelective(fileBase);
                log.info("软删除list2_basic_file_base记录，ID: {}，相关的list2_basic_file_data_mapping数据将由定时任务清理", fileBase.getId());
            }

            // 4. 根据list2_file_queue.id查询list2_basic_file_combined_file_queue表，获取对应的base_id
            List<List2BasicFileCombinedFileQueue> combinedFileQueues = list2BasicFileCombinedFileQueueService.getByFileQueueId(id);
            log.info("找到 {} 条list2_basic_file_combined_file_queue记录", combinedFileQueues.size());

            if (!combinedFileQueues.isEmpty()) {
                // 5. 根据查询到的base_id查询list2_basic_file_combined_total表中的对应数据
                for (List2BasicFileCombinedFileQueue combinedFileQueue : combinedFileQueues) {
                    Long baseId = combinedFileQueue.getBaseId();
                    List2BasicFileCombinedTotal combinedTotal = list2BasicFileCombinedTotalService.getById(baseId);

                    if (combinedTotal != null) {
                        Integer status = combinedTotal.getStatus();
                        log.info("找到list2_basic_file_combined_total记录，ID: {}, status: {}", baseId, status);

                        // 对涉及到的合并总表记录执行软删除操作
                        if (status != null) {
                            list2BasicFileCombinedTotalService.softDelete(baseId);
                            log.info("软删除list2_basic_file_combined_total记录，ID: {}", baseId);
                        }
                    }
                }
            } else {
                // 6. 如果不存在合并数据，执行附加逻辑：检查是否在抽样过程中使用过该文件
                for (List2BasicFileBase fileBase : list2BasicFileBaseList) {
                    List2BasicSampleDataMapping sampleDataMapping = list2BasicSampleDataMappingService.getByBaseIdAndSource(fileBase.getId(), 1);

                    if (sampleDataMapping != null) {
                        Long sampleRuleBaseId = sampleDataMapping.getSampleRuleBaseId();
                        log.info("找到抽样数据映射记录，base_id: {}, sample_rule_base_id: {}", fileBase.getId(), sampleRuleBaseId);

                        if (sampleRuleBaseId != null) {
                            // 软删除抽样规则
                            List2SampleRuleBase updateRule = new List2SampleRuleBase();
                            updateRule.setId(sampleRuleBaseId);
                            updateRule.setIsDeleted((short) 1);
                            updateRule.setAmendTime(new Date());
                            list2SampleRuleBaseService.updateSelective(updateRule);
                            log.info("软删除抽样规则记录，ID: {}", sampleRuleBaseId);
                        }
                    }
                }
            }

            // 7. 最终清理操作：确保list2_basic_file_base记录已软删除，然后删除list2_file_queue记录，以及list2_basic_file_combined_file_queue表中记录
            for (List2BasicFileBase fileBase : list2BasicFileBaseList) {
                if (fileBase.getDeleted() == null || fileBase.getDeleted() != 1) {
                    fileBase.setDeleted(1);
                    listBasicFileBaseService.updateSelective(fileBase);
                    log.info("确保list2_basic_file_base记录已软删除，ID: {}", fileBase.getId());
                }
            }

            // 清理list2_basic_file_combined_file_queue表中的相关记录
            try {
                int deletedCombinedFileQueueCount = list2BasicFileCombinedFileQueueService.deleteByFileQueueId(id);
                log.info("删除list2_basic_file_combined_file_queue表中file_queue_id={}的记录，删除数量: {}", id, deletedCombinedFileQueueCount);
            } catch (Exception e) {
                log.warn("删除list2_basic_file_combined_file_queue表记录时发生异常，file_queue_id: {}, 错误: {}", id, e.getMessage());
                // 不抛出异常，继续执行后续清理操作
            }

            // 删除list2_file_queue表中的原始记录
            listFileQueueMapper.deleteByPrimaryKey(id);
            log.info("删除list2_file_queue记录，ID: {}", id);

            log.info("P2类型文件队列删除完成，ID: {}", id);
            return 1;

        } catch (Exception e) {
            log.error("删除P2类型文件队列失败，ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw new RuntimeException("删除P2类型文件队列失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List2FileQueueDeleteImpactDto getDeleteImpact(Long id) {
        if (id == null) {
            return new List2FileQueueDeleteImpactDto(new ArrayList<>(), new ArrayList<>(),
                                                    new ArrayList<>(), "文件队列ID不能为空");
        }

        try {
            log.info("开始查询P2类型文件队列删除影响，ID: {}", id);

            List<Long> combinedTotalIds = new ArrayList<>();
            List<Long> sampleRuleBaseIds = new ArrayList<>();
            List<Long> basicFileBaseIds = new ArrayList<>();

            // 1. 查询会被软删除的list2_basic_file_base记录ID
            List<List2BasicFileBase> list2BasicFileBaseList = listBasicFileBaseService.getListBasicFileBaseList(id, null, null, null);
            if (list2BasicFileBaseList != null && !list2BasicFileBaseList.isEmpty()) {
                basicFileBaseIds = list2BasicFileBaseList.stream()
                    .map(List2BasicFileBase::getId)
                    .collect(Collectors.toList());
                log.info("找到 {} 条list2_basic_file_base记录将被软删除", basicFileBaseIds.size());
            }

            // 2. 查询会被级联删除的list2_basic_file_combined_total记录ID
            List<List2BasicFileCombinedFileQueue> combinedFileQueues = list2BasicFileCombinedFileQueueService.getByFileQueueId(id);
            if (combinedFileQueues != null && !combinedFileQueues.isEmpty()) {
                log.info("找到 {} 条list2_basic_file_combined_file_queue记录", combinedFileQueues.size());

                for (List2BasicFileCombinedFileQueue combinedFileQueue : combinedFileQueues) {
                    Long baseId = combinedFileQueue.getBaseId();
                    List2BasicFileCombinedTotal combinedTotal = list2BasicFileCombinedTotalService.getById(baseId);

                    if (combinedTotal != null) {
                        combinedTotalIds.add(baseId);
                        Integer status = combinedTotal.getStatus();
                        log.info("找到list2_basic_file_combined_total记录，ID: {}, status: {}", baseId, status);

                        // 3. 根据状态查询会被级联删除的抽样规则记录ID
                        if (status != null) {
                            List<Long> relatedSampleRuleIds = getSampleRuleIdsForCombinedTotal(baseId, status);
                            sampleRuleBaseIds.addAll(relatedSampleRuleIds);
                        }
                    }
                }
            } else {
                // 文件没有进行合并，但可能进行了抽样，检查基础文件是否用在了抽样规则中
                log.info("文件未进行合并，检查基础文件是否用在了抽样规则中");
                if (basicFileBaseIds != null && !basicFileBaseIds.isEmpty()) {
                    for (Long basicFileBaseId : basicFileBaseIds) {
                        // 查询list2_sample_rule_base_relation表中source=1且base_id匹配的记录
                        List<List2SampleRuleBaseRelation> relations = list2SampleRuleBaseRelationService.selectBySourceAndBaseId(1, basicFileBaseId);
                        if (relations != null && !relations.isEmpty()) {
                            for (List2SampleRuleBaseRelation relation : relations) {
                                if (relation.getSampleRuleBaseId() != null) {
                                    sampleRuleBaseIds.add(relation.getSampleRuleBaseId());
                                    log.info("找到基础文件{}对应的抽样规则ID: {}", basicFileBaseId, relation.getSampleRuleBaseId());
                                }
                            }
                        }
                    }
                }
            }

            // 去重
            combinedTotalIds = combinedTotalIds.stream().distinct().collect(Collectors.toList());
            sampleRuleBaseIds = sampleRuleBaseIds.stream().distinct().collect(Collectors.toList());
            basicFileBaseIds = basicFileBaseIds.stream().distinct().collect(Collectors.toList());

            // 生成影响描述
            String impactDescription = generateImpactDescription(basicFileBaseIds.size(),
                                                               combinedTotalIds.size(),
                                                               sampleRuleBaseIds.size());

            log.info("P2类型文件队列删除影响查询完成，ID: {}, 影响描述: {}", id, impactDescription);

            return new List2FileQueueDeleteImpactDto(combinedTotalIds, sampleRuleBaseIds,
                                                    basicFileBaseIds, impactDescription);

        } catch (Exception e) {
            log.error("查询P2类型文件队列删除影响失败，ID: {}, 错误信息: {}", id, e.getMessage(), e);
            return new List2FileQueueDeleteImpactDto(new ArrayList<>(), new ArrayList<>(),
                                                    new ArrayList<>(), "查询删除影响失败: " + e.getMessage());
        }
    }

    /**
     * 根据合并总表记录的状态查询相关的抽样规则ID
     * @param baseId 合并总表记录ID
     * @param status 状态
     * @return 抽样规则ID列表
     */
    private List<Long> getSampleRuleIdsForCombinedTotal(Long baseId, Integer status) {
        List<Long> sampleRuleIds = new ArrayList<>();

        try {
            if (status == 2) {
                // status=2: 仅合并完成，根据id查找list2_basic_sample_data_mapping表中source=2的记录
                List2BasicSampleDataMapping sampleData = list2BasicSampleDataMappingService.getByBaseIdAndSource(baseId, 2);
                if (sampleData != null && sampleData.getSampleRuleBaseId() != null) {
                    sampleRuleIds.add(sampleData.getSampleRuleBaseId());
                    log.info("找到status=2的抽样规则ID: {}", sampleData.getSampleRuleBaseId());
                }
            } else if (status == 7) {
                // status=7: 合并+排序完成，需要先获取base_id，然后查找source=3的记录
                List2BasicFileCombinedTotal combinedTotal = list2BasicFileCombinedTotalService.getById(baseId);
                if (combinedTotal != null && combinedTotal.getBaseId() != null) {
                    List2BasicSampleDataMapping sampleData = list2BasicSampleDataMappingService.getByBaseIdAndSource(combinedTotal.getBaseId(), 3);
                    if (sampleData != null && sampleData.getSampleRuleBaseId() != null) {
                        sampleRuleIds.add(sampleData.getSampleRuleBaseId());
                        log.info("找到status=7的抽样规则ID: {}", sampleData.getSampleRuleBaseId());
                    }
                }
            }
        } catch (Exception e) {
            log.warn("查询抽样规则ID失败，baseId: {}, status: {}, 错误: {}", baseId, status, e.getMessage());
        }

        return sampleRuleIds;
    }

    /**
     * 生成删除影响的描述信息
     * @param basicFileBaseCount 基础文件记录数量
     * @param combinedTotalCount 合并总表记录数量
     * @param sampleRuleCount 抽样规则记录数量
     * @return 影响描述
     */
    private String generateImpactDescription(int basicFileBaseCount, int combinedTotalCount, int sampleRuleCount) {
        StringBuilder description = new StringBuilder();
        description.append("删除此文件队列将会影响：");

        if (basicFileBaseCount > 0) {
            description.append(String.format(" %d 條基礎文件記錄", basicFileBaseCount));
        }

        if (combinedTotalCount > 0) {
            if (basicFileBaseCount > 0) {
                description.append("，");
            }
            description.append(String.format(" %d 條合併總表記錄", combinedTotalCount));
        }

        if (sampleRuleCount > 0) {
            if (basicFileBaseCount > 0 || combinedTotalCount > 0) {
                description.append("，");
            }
            description.append(String.format(" %d 條抽樣規則記錄", sampleRuleCount));
        }

        if (basicFileBaseCount == 0 && combinedTotalCount == 0 && sampleRuleCount == 0) {
            description.append(" 無相關數據記錄");
        }

        return description.toString();
    }
}