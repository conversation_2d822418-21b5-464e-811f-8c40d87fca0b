package tw.org.must.must.core.service.orcal.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tw.org.must.must.core.service.orcal.OrcalAgrAgreementExtendService;
import tw.org.must.must.mapper.orcal.OrcalAgrAgreementExtendMapper;
import tw.org.must.must.model.orcal.OrcalAgrAgreementExtend;

@Service
public class OrcalAgrAgreementExtendServiceImpl implements OrcalAgrAgreementExtendService{

	@Autowired
	private OrcalAgrAgreementExtendMapper orcalAgrAgreementExtendMapper;
	
	@Override
	public List<OrcalAgrAgreementExtend> getAgrAgreementExtendByAgrNo(String agrNo) {
		return orcalAgrAgreementExtendMapper.getAgrAgreementExtendByAgrNo(agrNo);
	}

}
