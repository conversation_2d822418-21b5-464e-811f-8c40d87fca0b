package tw.org.must.must.core.service.orcal.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.org.must.must.core.service.orcal.OrcalMbrIpNationalityService;
import tw.org.must.must.mapper.orcal.OrcalMbrIpNationalityMapper;
import tw.org.must.must.model.orcal.OrcalMbrIpNationality;

import java.util.List;

@Service
public class OrcalMbrIpNationalityServiceImpl implements OrcalMbrIpNationalityService {

    @Autowired
    private OrcalMbrIpNationalityMapper orcalMbrIpNationalityMapper;

    @Override
    public List<OrcalMbrIpNationality> getMbrIpNationalityByIpBaseNo(String ipBaseNo) {
        return orcalMbrIpNationalityMapper.getMbrIpNationalityByIpBaseNo(ipBaseNo);
    }

    @Override
    public List<OrcalMbrIpNationality> selectByDateByRowNum(int start, int end) {
        return orcalMbrIpNationalityMapper.selectByDateByRowNum(start,end);
    }


}
