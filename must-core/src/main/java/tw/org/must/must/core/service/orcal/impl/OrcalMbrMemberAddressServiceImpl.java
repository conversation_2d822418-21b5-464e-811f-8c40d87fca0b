package tw.org.must.must.core.service.orcal.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import tw.org.must.must.core.service.orcal.OrcalMbrMemberAddressService;
import tw.org.must.must.mapper.orcal.OrcalMbrMemberAddressMapper;
import tw.org.must.must.model.orcal.OrcalMbrMemberAddress;

@Service
public class OrcalMbrMemberAddressServiceImpl implements OrcalMbrMemberAddressService{

	@Autowired
	private OrcalMbrMemberAddressMapper orcalMbrMemberAddressMapper;
	
	@Override
	public List<OrcalMbrMemberAddress> getMbrMemberAddressByIpBaseNo(String ipBaseNo) {
		return orcalMbrMemberAddressMapper.getMbrMemberAddressByIpBaseNo(ipBaseNo);
	}
}
