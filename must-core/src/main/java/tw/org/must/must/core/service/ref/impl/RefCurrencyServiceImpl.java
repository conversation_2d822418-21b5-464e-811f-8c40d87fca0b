package tw.org.must.must.core.service.ref.impl;

import com.github.pagehelper.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;
import tw.org.must.must.common.util.ExampleUtil;
import tw.org.must.must.model.ref.RefCurrency;
import tw.org.must.must.mapper.ref.RefCurrencyMapper;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.ref.RefCurrencyService;

import java.util.List;

@Service
public class RefCurrencyServiceImpl extends BaseServiceImpl<RefCurrency> implements RefCurrencyService {

	private final RefCurrencyMapper refCurrencyMapper;

    @Autowired
    public RefCurrencyServiceImpl(RefCurrencyMapper refCurrencyMapper) {
        super(refCurrencyMapper);
        this.refCurrencyMapper = refCurrencyMapper;
    }

    @Override
    public List<RefCurrency> listRefCurrency(String currencyCode) {
        if(StringUtils.isBlank(currencyCode)){
            return null;
        }
        RefCurrency refCurrency = new RefCurrency();
        refCurrency.setCurrencyCode(currencyCode);
        return refCurrencyMapper.select(refCurrency);
    }
    
    @Override
    public List<RefCurrency> getRefCurrency(String currencyCode,String currencyName) {
      
        Example example = new Example(RefCurrency.class);
        Criteria createCriteria = example.createCriteria();
        if (StringUtils.isNotBlank(currencyCode)) {
        	createCriteria.andLike("currencyCode", ExampleUtil.exampleLikeAll(currencyCode));
		}
        if(StringUtils.isNotBlank(currencyName)) {
        	createCriteria.andLike("currencyName", ExampleUtil.exampleLikeAll(currencyName));
        }
        example.orderBy("currencyCode").asc();
        return  refCurrencyMapper.selectByExample(example);
    }
}