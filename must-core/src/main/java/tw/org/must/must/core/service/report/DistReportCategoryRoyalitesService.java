package tw.org.must.must.core.service.report;


import tw.org.must.must.model.report.DistReportCategoryRoyalites;
import tw.org.must.must.common.base.BaseService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface DistReportCategoryRoyalitesService extends BaseService<DistReportCategoryRoyalites> {

    int clearByDistNo(String distNo) ;

    List<DistReportCategoryRoyalites> getRetRoyAmtSum(String distNo );

}