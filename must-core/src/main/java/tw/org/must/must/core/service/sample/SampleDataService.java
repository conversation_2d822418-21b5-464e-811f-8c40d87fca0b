package tw.org.must.must.core.service.sample;

import tw.org.must.must.core.MatchTesting.QueryResult;

import java.util.List;

public interface SampleDataService{

	List<QueryResult> queryResult(String title, String artist, String isrc, String composer, String lyricist);

	List<QueryResult> queryResultMax(String title, String artist, String isrc, String composer, String lyricist);

	Boolean initSampleData();

	Boolean initSampleDataByFile();

}