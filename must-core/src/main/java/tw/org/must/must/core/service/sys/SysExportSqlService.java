package tw.org.must.must.core.service.sys;


import tw.org.must.must.model.sys.SysExportSql;
import tw.org.must.must.common.base.BaseService;

import java.text.ParseException;
import java.util.List;

public interface SysExportSqlService extends BaseService<SysExportSql> {
    SysExportSql saveOrUpdateSysExportSql(SysExportSql sysExportSql);
    SysExportSql saveOrUpdateComplexSqlTemplate(SysExportSql sysExportSql);
    List<SysExportSql> listSysExportSql(Integer pageNum, Integer pageSize,String sqlName,String startTime,String endTime) throws ParseException;
    Integer generate(Long id);
    void generateFile();
    Integer changeFileStatus(Long id, Boolean fileStatus);
}