package tw.org.must.must.core.service.sys;


import com.github.pagehelper.PageInfo;
import tw.org.must.must.model.sys.SysJob;

import java.util.List;

public interface SysJobService {
    /**
     * 新增定时任务
     */
    SysJob addJob(SysJob job);

    void editSysJob(SysJob sysJob);

    /**
     * 启动定时任务
     */
    void start(String id);

    /**
     * 暂停定时任务
     */
    void pause(String id);

    /**
     * 删除定时任务
     */
    Integer remove(String id);

    /**
     * 启动所有定时任务
     */
    void startAllJob();

    /**
     * 暂停所有定时任务
     */
    void pauseAllJob();

    /**
     * 立即执行
     *
     * @param id
     */
    void executeJog(String id);
    /*_____________________________*/

    /**
     * 批量删除定时任务
     */
    void removes(List<String> ids);

    /**
     * 批量启动定时任务
     */
    void starts(List<String> ids);

    /**
     * 批量暂停定时任务
     */
    void pauses(List<String> ids);

    /**
     * 批量立即执行
     *
     * @param ids
     */
    void executeJogs(List<String> ids);


    PageInfo<SysJob> listSysJobWithPage(Integer pageNum, Integer pageSize, String beanName, String name, String nameEn, Integer status);


    void refreshRedis();
}