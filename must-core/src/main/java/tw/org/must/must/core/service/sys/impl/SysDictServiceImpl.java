package tw.org.must.must.core.service.sys.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.common.util.ExampleUtil;
import tw.org.must.must.core.service.sys.SysDictItemService;
import tw.org.must.must.core.service.sys.SysDictService;
import tw.org.must.must.mapper.sys.SysDictMapper;
import tw.org.must.must.model.sys.SysDict;
import tw.org.must.must.model.sys.SysDictItem;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class SysDictServiceImpl extends BaseServiceImpl<SysDict> implements SysDictService {

    public static final String REDIS_DICT_KEY = "DICT:";

	private final SysDictMapper sysDictMapper;
    @Autowired
    public SysDictServiceImpl(SysDictMapper sysDictMapper) {
        super(sysDictMapper);
        this.sysDictMapper = sysDictMapper;
    }

    @Autowired
    private SysDictItemService sysDictItemService;
    @Autowired
    private RedisTemplate<String,Object> redisTemplate;

    @Override
    public List<SysDict> getSysDictList(String dictName, String dictCode) {
        Example example = new Example(SysDict.class);
        Example.Criteria criteria = example.createCriteria();
        if(StringUtils.isNotEmpty(dictName)) {
            criteria.andLike("dictName", ExampleUtil.exampleLikeAll(dictName));
        }
        if(StringUtils.isNotEmpty(dictCode)) {
            criteria.andEqualTo("dictCode", dictCode);
        }
        example.orderBy("amendTime").desc();
        return sysDictMapper.selectByExample(example);
    }

    @Override
    public SysDict getByDictCode(String dictCode) {
        if(StringUtils.isBlank(dictCode)) {
            return null;
        }
        Example example = new Example(SysDict.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andLike("dictCode", dictCode);
        return sysDictMapper.selectOneByExample(example);
    }

    @Override
    @Transactional
    public Integer deleteAndItem(Long id) {
        Integer delete = this.delete(id);
        sysDictItemService.deleteByDictId(id);
        return delete;
    }

    @Override
    public Integer initResdis(String dictCode) {
        String redisKey = String.format("%s%s", REDIS_DICT_KEY, dictCode);
        redisTemplate.delete(redisKey);
        SysDict sysDict = getByDictCode(dictCode);
        List<SysDictItem> sysDictItemList = sysDictItemService.getByDictIdAndStatus(sysDict.getId(), 1);
        if(CollectionUtils.isEmpty(sysDictItemList)) {
            return 0;
        }
        Map<String, String> map = sysDictItemList.stream().collect(Collectors.toMap(SysDictItem::getItemText, SysDictItem::getItemValue, (a, b) -> a));
        redisTemplate.opsForHash().putAll(redisKey, map);
        redisTemplate.expire(redisKey, 20, TimeUnit.MINUTES);
        return 1;
    }

    @Override
    public Integer deleteRedisItem(String dictCode) {
        Boolean delete = redisTemplate.delete(String.format("%s%s", REDIS_DICT_KEY, dictCode));
        if(delete) {
            return 1;
        }
        return 0;
    }

    @Override
    public Integer clearDictCache() {
        Boolean delete = redisTemplate.delete(String.format("%s*", REDIS_DICT_KEY));
        if(delete) {
            return 1;
        }
        return 0;
    }

    @Override
    public List<SysDictItem> getItemByCode(String code) {
        SysDict sysDict = this.getByDictCode(code);
        if(Objects.isNull(sysDict)) {
            return new ArrayList<>();
        }
        return sysDictItemService.getByDictIdAndStatus(sysDict.getId(), 1);
    }
}