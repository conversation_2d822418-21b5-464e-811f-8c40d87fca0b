package tw.org.must.must.core.service.wrk.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.model.wrk.WrkWorkRight;
import tw.org.must.must.mapper.wrk.WrkWorkRightMapper;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.wrk.WrkWorkRightService;

import java.util.ArrayList;
import java.util.List;

@Service
public class WrkWorkRightServiceImpl extends BaseServiceImpl<WrkWorkRight> implements WrkWorkRightService {

	private final WrkWorkRightMapper wrkWorkRightMapper;

    @Autowired
    public WrkWorkRightServiceImpl(WrkWorkRightMapper wrkWorkRightMapper) {
        super(wrkWorkRightMapper);
        this.wrkWorkRightMapper = wrkWorkRightMapper;
    }

    @Override
    public WrkWorkRight getWrkWorkRightByWorkUniqueAndRightType(String workUniqueKey, String right_type) {
        if(StringUtils.isBlank(workUniqueKey) || StringUtils.isBlank(right_type))
            return new WrkWorkRight();
        Example example = new Example(WrkWorkRight.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("workUniqueKey",workUniqueKey);
        criteria.andEqualTo("rightType",right_type);
        return wrkWorkRightMapper.selectOneByExample(example);
    }

    @Override
    public void insertDuplicate(List<WrkWorkRight> wrkWorkRightList) {
        wrkWorkRightMapper.insertDuplicate(wrkWorkRightList);
    }

    @Override
    public List<WrkWorkRight> getWrkWorkRightByWorkUnique(String workUniqueKey) {
        Example example = new Example(WrkWorkRight.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("workUniqueKey",workUniqueKey);
        return wrkWorkRightMapper.selectByExample(example);
    }

    @Override
    public void deleteByWorkUniqueKey(String workUniqueKey) {
        if(StringUtils.isBlank(workUniqueKey)){
            return ;
        }
        Example example = new Example(WrkWorkRight.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("workUniqueKey",workUniqueKey);
        wrkWorkRightMapper.deleteByExample(example);
    }

    @Override
    public List<WrkWorkRight> getWrkWorkRightByWorkUniqueListAndRightType(List<String> workUniqueKeyList, String rightType) {
        if(CollectionUtils.isEmpty(workUniqueKeyList)) {
            return new ArrayList<>();
        }
        Example example = new Example(WrkWorkRight.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("workUniqueKey", workUniqueKeyList);
        criteria.andEqualTo("rightType", rightType);
        return wrkWorkRightMapper.selectByExample(example);
    }
}