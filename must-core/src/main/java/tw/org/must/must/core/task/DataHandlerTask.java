package tw.org.must.must.core.task;

import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.core.service.list.YoutubeLi0103DetailService;
import tw.org.must.must.model.list.YoutubeLi0103Detail;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class DataHandlerTask implements Job {

    @Autowired
    private YoutubeLi0103DetailService youtubeLi0103DetailService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

    }

    public void startSchedule() {
        try {
            XxlJobLogger.log("delete  list_dsp_file_data_mapping_yt_li0103_details where rights_controller not equals MUST_CS or Conflict 开始......");
            Long startId = 0L;
            Long count = 0L;
            while (true){
                XxlJobLogger.log("当前Id：{}",startId);
                List<YoutubeLi0103Detail> details = youtubeLi0103DetailService.getDetails(startId);
                if(CollectionUtils.isEmpty(details)){
                    break;
                }
                startId = details.get(details.size() - 1).getId();

                List<Long>  ids = details.stream().filter(d -> !StringUtils.equalsAny(d.getRightsController(),"MUST_CS","Conflict")).map(d->d.getId()).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(ids)){
                    XxlJobLogger.log("delete list_dsp_file_data_mapping_yt_li0103_details 成功，数量:0");
                    continue;
                }

                for(Long id : ids){
                    youtubeLi0103DetailService.delete(id);
                }

                XxlJobLogger.log("delete list_dsp_file_data_mapping_yt_li0103_details 成功，数量:{}",ids.size());
                count += ids.size();

            }

            XxlJobLogger.log("delete list_dsp_file_data_mapping_yt_li0103_details 结束......,总数量：{}",count);
        } catch (Exception e) {
            XxlJobLogger.log("delete list_dsp_file_data_mapping_yt_li0103_details 异常:");
            XxlJobLogger.log(e);
            e.printStackTrace();
        }
    }

    public void transferByRightsController() {
        try {
            XxlJobLogger.log("transferByRightsController  list_dsp_file_data_mapping_yt_li0103_details where rights_controller equals MUST_CS or Conflict 开始......");
            Long startId = 0L;
            Long count = 0L;
            while (true){
                XxlJobLogger.log("当前Id：{}",startId);
                List<YoutubeLi0103Detail> details = youtubeLi0103DetailService.getDetails(startId);
                if(CollectionUtils.isEmpty(details)){
                    break;
                }
                startId = details.get(details.size() - 1).getId();

                List<YoutubeLi0103Detail> temps = details.stream().filter(d -> StringUtils.equalsAny(d.getRightsController(),"MUST_CS","Conflict")).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(temps)){
                    XxlJobLogger.log("transferByRightsController list_dsp_file_data_mapping_yt_li0103_details 成功，数量:0");
                    continue;
                }

                List<List<YoutubeLi0103Detail>> partitions = Lists.partition(temps, Constants.BATCH_SIZE_1000);
                for(List<YoutubeLi0103Detail> partition: partitions){
                    youtubeLi0103DetailService.addList(partition);
                }

                XxlJobLogger.log("transferByRightsController list_dsp_file_data_mapping_yt_li0103_details 成功，数量:{}",temps.size());
                count += temps.size();

            }

            XxlJobLogger.log("transferByRightsController list_dsp_file_data_mapping_yt_li0103_details 结束......,总数量：{}",count);
        } catch (Exception e) {
            XxlJobLogger.log("transferByRightsController list_dsp_file_data_mapping_yt_li0103_details 异常:");
            XxlJobLogger.log(e);
            e.printStackTrace();
        }
    }
}
