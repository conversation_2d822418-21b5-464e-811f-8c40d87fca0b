package tw.org.must.must.core.task.claim.apple;

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tw.org.must.must.common.util.result.Result;
import tw.org.must.must.core.service.claim.ClaimMinimaInfoService;
import tw.org.must.must.core.service.claim.ClaimSignMemberService;
import tw.org.must.must.core.service.list.ListDspFileBaseService;
import tw.org.must.must.core.task.claim.ClaimCcidHandler;
import tw.org.must.must.core.task.claim.WrkWorkHandler;
import tw.org.must.must.core.task.claim.spotify.ClaimCcidCalcResultPostClaimSpotify;
import tw.org.must.must.core.task.claim.youtube.ClaimCcidResultPostClaimAbstract;
import tw.org.must.must.core.task.claim.youtube.entity.MinimaNetRevenueContent;
import tw.org.must.must.model.claim.ClaimCcidHeader;
import tw.org.must.must.model.claim.ClaimMinimaInfo;
import tw.org.must.must.model.claim.ClaimResultCcid;
import tw.org.must.must.model.claim.ClaimSignMember;
import tw.org.must.must.model.list.ListDspFileBase;
import tw.org.must.must.model.list.ListMatchDataDspDone;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkIpShare;
import tw.org.must.must.model.wrk.WrkWorkTitle;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClaimCcidCalcResultPostClaimApple extends ClaimCcidCalcResultPostClaimSpotify {

    private Logger logger = LoggerFactory.getLogger(ClaimCcidCalcResultPostClaimApple.class);

    @Autowired
    private ListDspFileBaseService listDspFileBaseService;

    @Autowired
    private ClaimMinimaInfoService claimMinimaInfoService;

    @Autowired
    private ClaimSignMemberService claimSignMemberService;


    @Override
    public void filterListDspFileBase(ClaimCcidHandler claimCcidHandler) {

    }

    @Override
    public ClaimCcidHandler initClaimCcidHandler(ClaimCcidHeader claimCcidHeader) {
        ClaimCcidHandler claimCcidHandler = new ClaimCcidHandler();

        // 初始化 竞争对手数据
        claimCcidHandler.setClaimHeaderId(claimCcidHeader.getId());

        claimCcidHandler.setVersion(claimCcidHeader.getRecever());

        claimCcidHandler.setCompany(claimCcidHeader.getCompany());

        String claimMinimaInfoIds = claimCcidHeader.getClaimMinimaInfoIds();

        if (StringUtils.isBlank(claimMinimaInfoIds) && StringUtils.isBlank(claimCcidHeader.getFileQueueIds())) {
            setClaimCCidHeaderStatusErrorHandle(claimCcidHeader, "当前header配置错误！");
        }


//        claimCcidHandler.setClaimFilterOthersocHashMap(initClaimFilterOtherSocData());

        claimCcidHandler.setRefSocietyRightMap(initSysterSociety());
        claimCcidHandler.setRefSocietyList(initSociety());

        XxlJobLogger.log("当前可用的ccidHeader中的claimMinimaInfoIds为：{}", claimMinimaInfoIds);
        String[] claimMinimaInfoIdSplit = claimMinimaInfoIds.split(";");
        Date startDate = claimCcidHeader.getStartDate();
        Date endDate = claimCcidHeader.getEndDate();

        String startDateStr = null;
        if (startDate != null) {
            startDateStr = new SimpleDateFormat("yyyy-MM-dd").format(DateUtils.addDays(startDate,-10));
        }
        String endDateStr = null;

        if (endDate != null) {
            endDateStr = new SimpleDateFormat("yyyy-MM-dd").format(endDate);
        }

        List<ClaimMinimaInfo> claimMinimaInfoList = new ArrayList<>();
        for (String claimMinimaInfoId : claimMinimaInfoIdSplit) {
            if (StringUtils.isBlank(claimMinimaInfoId)){
                continue;
            }
            Long cmiId = Long.valueOf(claimMinimaInfoId);

            ClaimMinimaInfo claimMinimaInfo = claimMinimaInfoService.getById(cmiId);
            if (claimMinimaInfo == null) {
                setClaimCCidHeaderStatusErrorHandle(claimCcidHeader, "ClaimMinimaInfoId為：" + cmiId + ",在ClaimMinimaInfo表中查詢無數據~！");
                break;
            }

            List<ClaimMinimaInfo> products = claimMinimaInfoService.getClaimMinimaInfoBySetInfoId(claimMinimaInfo.getClaimSetId());
            //List<ClaimMinimaInfo> products = claimMinimaInfoService.getClaimMinimaInfoByProductFullName(claimMinimaInfo.getProductFullName());


            claimCcidHandler.setSvodChildList(products);

            claimMinimaInfoList.add(claimMinimaInfo);
        }



        String fileQueueIds = claimCcidHeader.getFileQueueIds();
        List<Long> fileQueueIdList = new ArrayList<>();
        if(StringUtils.isNotBlank(fileQueueIds)) {
            try {
                String[] split = fileQueueIds.split(",");
                for (String s : split) {
                    fileQueueIdList.add(Long.valueOf(s));
                }
            } catch (NumberFormatException e) {
                logger.error("claimCcidHeader 字段 file_queue_ids【{}】 不合符要求！", fileQueueIds);
                logger.error("msg: ", e);
            }
        }

        List<ListDspFileBase> listDspFileBaseList = new ArrayList<>();

        if(!fileQueueIdList.isEmpty()) {
            //
            listDspFileBaseList = listDspFileBaseService.getListDspFileBaseByQueueIdsAndFileDate(fileQueueIdList, startDateStr, endDateStr);
            if(listDspFileBaseList.isEmpty()) {
                setClaimCCidHeaderStatusErrorHandle(claimCcidHeader, "根据ListFilqQueueIds = " + fileQueueIds + ", 开始时间：" + startDateStr + ", 结束时间：" + endDateStr + ", 未找到ListDspFileBase！");
                return null;
            }
            List<Long> claimMinimaInfoIdList = listDspFileBaseList.stream().map(ListDspFileBase::getClaimMinimaInfoId).collect(Collectors.toList());
            if (claimMinimaInfoIdList.size() > 0){
                List<ClaimMinimaInfo> claimMinimaInfos = claimMinimaInfoService.listByIds(claimMinimaInfoIdList);
                claimMinimaInfoList.addAll(claimMinimaInfos);
                claimCcidHandler.setClaimMinimaInfoList(claimMinimaInfoList);
            }
            //开始时间如果为空，自动从ListDspFileBaseList中获取最大，最小值
            if (startDate == null || endDate == null) {
                setClaimHeaderByFileList(claimCcidHeader,listDspFileBaseList);
            }

        }else {
            List<Long> claimMinimaInfoIdList = claimMinimaInfoList.stream().map(ClaimMinimaInfo::getId).collect(Collectors.toList());
            // 根據  claimMinimaInfoIdList in查詢 減少查詢次數
            listDspFileBaseList = listDspFileBaseService.getListDspFileBaseByMinimaInfoIdListAndFileDate(claimMinimaInfoIdList, startDateStr, endDateStr);
            if (null == listDspFileBaseList || listDspFileBaseList.size() == 0) {
                setClaimCCidHeaderStatusErrorHandle(claimCcidHeader, "根据minimaIdList = " + claimMinimaInfoIdList + ", 开始时间：" + startDateStr + ", 结束时间：" + endDateStr + ", 未找到ListDspFileBase！");
                return null;
            }
            //开始时间如果为空，自动从ListDspFileBaseList中获取最大，最小值
            if (startDate == null || endDate == null) {
                setClaimHeaderByFileList(claimCcidHeader,listDspFileBaseList);
            }
        }
        claimCcidHandler.setClaimMinimaInfoList(claimMinimaInfoList);

        claimCcidHandler.setListDspFileBaseList(listDspFileBaseList);
        claimCcidHandler.setFileBaseIdAndNameMap(listDspFileBaseList.stream().collect(Collectors.toMap(it -> it.getId(), Function.identity(), (a, b) -> a)));

        Map<String, ClaimSignMember> mechaincalSocList = new HashMap<>();
        Map<String, ClaimSignMember> mechaincalIPList = new HashMap<>();

        claimCcidHandler.getRightTypeList().add("PER");

        Long claimSetId = 0L;
        if (claimMinimaInfoList.size() > 0){
            ClaimMinimaInfo claimMinimaInfo = claimMinimaInfoList.get(0);
            claimSetId = claimMinimaInfo.getClaimSetId();
        }
        //查询参数待传进来
        List<ClaimSignMember> selectAllListByClaimSetId = claimSignMemberService.selectAllListByClaimSetId(claimSetId);

        if (null != selectAllListByClaimSetId && selectAllListByClaimSetId.size() > 0) {
            // 抽取委托重制的协会   用于缓存需要计算重制的协会
            selectAllListByClaimSetId.forEach(signMember -> {
                Integer socNo = signMember.getSocNo();
                String ipBaseNo = signMember.getIpBaseNo();
                Map<String, ClaimSignMember> socMap = new HashMap<>();
                if (null != socNo) {
                    socMap.put(signMember.getSocNo() + "", signMember);

                    claimCcidHandler.getRightTypeList().add("MEC");
                }
                if (StringUtils.isNotBlank(ipBaseNo)) {
                    Map<String, ClaimSignMember> ipMap = new HashMap<>();
                    mechaincalIPList.put(signMember.getIpBaseNo(), signMember);
                    //mechaincalIPList.add(ipMap);
                    claimCcidHandler.getRightTypeList().add("MEC");
                }
                if (socNo != null ) {
                    Map<String, ClaimSignMember> ipMap = new HashMap<>();
                    mechaincalSocList.put(signMember.getSocNo()+"", signMember);
                    //mechaincalIPList.add(ipMap);
                    claimCcidHandler.getRightTypeList().add("MEC");
                }

            });
            claimCcidHandler.setMechaincalIPList(mechaincalIPList);
            claimCcidHandler.setMechaincalSocList(mechaincalSocList);
            claimCcidHandler.setIsAppoint(claimCcidHeader.getIsAppoint().equals(1) ? true : false);
        }


        return claimCcidHandler;
    }



    @Override
    public Result<?> initMininMaxNetRevenue(ClaimCcidHeader claimCcidHeader, ClaimCcidHandler claimCcidHandler) {
        List<ClaimMinimaInfo> claimMinimaInfoList = claimCcidHandler.getClaimMinimaInfoList();


        Map<Long, ListDspFileBase> fileBaseIdAndNameMap = claimCcidHandler.getFileBaseIdAndNameMap();


        //计算revenue金额
        Map<Long, MinimaNetRevenueContent> minimaNetRevenueContentMap = claimCcidHandler.getMinimaNetRevenueContentMap();

        for (Long fileBaseId : fileBaseIdAndNameMap.keySet()) {
            ListDspFileBase listDspFileBase = fileBaseIdAndNameMap.get(fileBaseId);
            if (listDspFileBase == null) {
                continue;
            }
            Optional<ClaimMinimaInfo> claimMinimaInfo = claimMinimaInfoList.stream().filter(minni -> minni.getProductFullName().equals(listDspFileBase.getProductName())
                    || minni.getProductShortName().equals(listDspFileBase.getProductName())).findFirst();

            if (!claimMinimaInfo.isPresent()) {
                log.warn("未找到产品配置" + listDspFileBase.getProductName());
                continue;
            }
            ClaimMinimaInfo claimMinimaInfo1 = claimMinimaInfo.get();

            BigDecimal formulaPublicMechanical = claimMinimaInfo1.getFormulaPublicMechanical();
            BigDecimal listTotalRoy = listDspFileBase.getListTotalRoy();
            BigDecimal totalShare = claimMinimaInfo1.getTotalShare();
            Integer formulaType = claimMinimaInfo1.getFormulaType();

            if (formulaType ==null){
                formulaType = 1;
            }
            if (totalShare == null) {
                totalShare = BigDecimal.ZERO;
            }

            if (formulaPublicMechanical == null) {
                formulaPublicMechanical = BigDecimal.ZERO;
            }
            if (listTotalRoy == null) {
                listTotalRoy = BigDecimal.ZERO;
            }
            BigDecimal publicShare = claimMinimaInfo1.getPublicShare();
            BigDecimal mechanicalShare = claimMinimaInfo1.getMechanicalShare();


            MinimaNetRevenueContent minimaNetRevenueContent = new MinimaNetRevenueContent();
            minimaNetRevenueContent.setPublicShare(publicShare);
            minimaNetRevenueContent.setMechanicalShare(mechanicalShare);
            minimaNetRevenueContent.setListTotalRoy(listTotalRoy);
            setSplitShare(claimMinimaInfo1, minimaNetRevenueContent);
            minimaNetRevenueContent.setTotalShare(claimMinimaInfo1.getTotalShare());

            Long listDspFileBaseId = listDspFileBase.getId();
            minimaNetRevenueContent.setFileBaseId(listDspFileBaseId);

            minimaNetRevenueContent.setListTotalRoy(listDspFileBase.getListTotalRoy());

            minimaNetRevenueContent.setMaxNetRevenue(listTotalRoy);
            if (totalShare.compareTo(BigDecimal.ZERO)<=0){
                log.warn(claimMinimaInfo1.getProductFullName()+"：share比例为0");
                continue;
            }
            //计算netrevenue权利金 = listTotalRoy * total Share/100
            minimaNetRevenueContent.setNetRevenueRoy(listTotalRoy.multiply(totalShare).divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP));
            minimaNetRevenueContent.setSubScribeCount(listDspFileBase.getListSubscribeCount() ==null ? BigDecimal.ZERO :listDspFileBase.getListSubscribeCount() );

            BigDecimal subscriptRoy;
            //2021-12-01根据业务需求更改：计算订阅者数，计算取其高数据,如果设置的是播放次数作为订阅者基数，则使用播放次数计算,
            if (2 == formulaType){
                BigDecimal listTotalClickCount = listDspFileBase.getListTotalClickCount();
                if (listTotalClickCount ==null){
                    listTotalClickCount = BigDecimal.ZERO;
                }
                subscriptRoy = formulaPublicMechanical.multiply(listTotalClickCount).setScale(6, RoundingMode.HALF_UP);
                minimaNetRevenueContent.setSubscriptRoy(subscriptRoy);

            }else{
                subscriptRoy = formulaPublicMechanical.multiply(minimaNetRevenueContent.getSubScribeCount()).setScale(6, RoundingMode.HALF_UP);
                minimaNetRevenueContent.setSubscriptRoy(subscriptRoy);
            }

            if (subscriptRoy.compareTo(minimaNetRevenueContent.getNetRevenueRoy()) > 0) {
                minimaNetRevenueContent.setMaxRoy(subscriptRoy);
                minimaNetRevenueContent.setRolyatyType("M");
            } else {
                minimaNetRevenueContent.setMaxRoy(minimaNetRevenueContent.getNetRevenueRoy());
                minimaNetRevenueContent.setRolyatyType("P");
            }

            minimaNetRevenueContent.setTotalClick(listDspFileBase.getListTotalClickCount());
            if (minimaNetRevenueContent.getTotalClick() != null && minimaNetRevenueContent.getTotalShare().compareTo(BigDecimal.ZERO) > 0) {
                minimaNetRevenueContent.setConstantBaseRoy(minimaNetRevenueContent.getMaxRoy().divide(minimaNetRevenueContent.getTotalClick(), 6, RoundingMode.HALF_UP));

                BigDecimal constantBaseNetRevenue = minimaNetRevenueContent.getConstantBaseRoy()
                        .divide(minimaNetRevenueContent.getTotalShare().divide(BigDecimal.valueOf(100)),6,RoundingMode.HALF_UP);
                minimaNetRevenueContent.setConstantBaseNetRevenue(constantBaseNetRevenue);

            } else {
                minimaNetRevenueContent.setConstantBaseRoy(BigDecimal.ZERO);
                minimaNetRevenueContent.setConstantBaseRoy(BigDecimal.ZERO);
            }



            minimaNetRevenueContent.setProductName(listDspFileBase.getProductName());

            minimaNetRevenueContentMap.put(listDspFileBase.getId(),minimaNetRevenueContent);

            XxlJobLogger.log("------------------取其高计算参数("+listDspFileBase.getId()+")------------------");
            XxlJobLogger.log(minimaNetRevenueContent.toString());
        }


        return new Result<>(200, "获取成功");
    }
    private void setSplitShare(ClaimMinimaInfo claimMinimaInfo1, MinimaNetRevenueContent minimaNetRevenueContent) {

        if (claimMinimaInfo1.getMecSplitShare() != null && claimMinimaInfo1.getMecSplitShare() != null
                && claimMinimaInfo1.getMecSplitShare().compareTo(BigDecimal.ZERO) > 0
                && claimMinimaInfo1.getPerSplitShare().compareTo(BigDecimal.ZERO) > 0) {
            minimaNetRevenueContent.setPublicSplitShare(claimMinimaInfo1.getPerSplitShare());
            minimaNetRevenueContent.setMechanicalSplitShare(claimMinimaInfo1.getMecSplitShare());
            return;
        }

        BigDecimal publicShare = claimMinimaInfo1.getPublicShare();
        BigDecimal mechanicalShare = claimMinimaInfo1.getMechanicalShare();
        BigDecimal totalShare = claimMinimaInfo1.getTotalShare();
        if (publicShare != null && mechanicalShare != null) {
            if (claimMinimaInfo1.getTotalShare() == null) {
                totalShare = publicShare.add(mechanicalShare);
                claimMinimaInfo1.setTotalShare(totalShare);
            }
            if (totalShare != null && totalShare.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal publicSplitShare = publicShare.divide(totalShare, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                BigDecimal mecSplitShare = mechanicalShare.divide(totalShare, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                minimaNetRevenueContent.setPublicSplitShare(publicSplitShare);
                minimaNetRevenueContent.setMechanicalSplitShare(mecSplitShare);
            }

        }


    }


    @Override
    public boolean isMatched(ClaimCcidHeader claimCcidHeader) {
        if (StringUtils.equalsAnyIgnoreCase(claimCcidHeader.getCompany(), "apple", "AppleMusic")) {
            return true;
        }

        return false;
    }


    /**
     * 生成apple的ccid数据
     * FIXME 20211125 ， pub_id内容会输出 PERF+MEC数据。同时会记录一条MEC给会员的记录
     * @param claimCcidHandler
     * @param listMatchDataDspDone
     * @param wrkWorkHandler
     * @return
     */
    @Override
    public ClaimResultCcid createClaimResultDataBean(ClaimCcidHandler claimCcidHandler, ListMatchDataDspDone listMatchDataDspDone, WrkWorkHandler wrkWorkHandler) {

        ClaimResultCcid claimResultCcid = new ClaimResultCcid();
        claimResultCcid.init();


        Long listDspFileBaseId = listMatchDataDspDone.getFileBaseId();
        Map<Long, ListDspFileBase> fileBaseIdAndNameMap = claimCcidHandler.getFileBaseIdAndNameMap();

        ListDspFileBase listDspFileBase = fileBaseIdAndNameMap.get(listDspFileBaseId);
        Map<Long, MinimaNetRevenueContent> minimaNetRevenueContentMap = claimCcidHandler.getMinimaNetRevenueContentMap();
        MinimaNetRevenueContent minimaNetRevenueContent = minimaNetRevenueContentMap.get(listDspFileBaseId);
        if (minimaNetRevenueContent ==null){
            return null;
        }

        claimResultCcid.setProductName(minimaNetRevenueContent.getProductName());


        claimResultCcid.setOriginalTitle(listMatchDataDspDone.getTitle());
        claimResultCcid.setOtherTitle(listMatchDataDspDone.getMatchTitle());
        claimResultCcid.setClaimWorkTitle(listMatchDataDspDone.getMatchTitle());
        WrkWorkTitle workTitlePriorOT = wrkWorkHandler.getWorkTitle();
        if (workTitlePriorOT != null) {
            if (StringUtils.isNotBlank(workTitlePriorOT.getTitle())) {
                claimResultCcid.setClaimWorkTitle(workTitlePriorOT.getTitle());
            } else {
                if (StringUtils.isNotBlank(workTitlePriorOT.getTitleEn())) {
                    claimResultCcid.setClaimWorkTitle(workTitlePriorOT.getTitleEn());
                }
            }
        }


        claimResultCcid.setClaimWorkId(listMatchDataDspDone.getMatchWorkId());
        claimResultCcid.setClaimWorkSoc(listMatchDataDspDone.getMatchWorkSocietyCode() + "");
        claimResultCcid.setClaimWorkUniqueKey(listMatchDataDspDone.getWorkUniqueKey());
        claimResultCcid.setOriginalReleaseId(listMatchDataDspDone.getBusinessId());
        claimResultCcid.setOriginalResourceId(listMatchDataDspDone.getResourceId());
        claimResultCcid.setClaimWorkIsrc(listMatchDataDspDone.getIsrc());


        //2021 03 05 ,根据CCID v14 YouTube guidelines for the Subscription services v1.2文档说明改成从作品中获取
        WrkWork wrkWrok = wrkWorkHandler.getWrkWrok();
        if (wrkWrok != null) {
            claimResultCcid.setClaimWorkIswc(wrkWrok.getISWC());
        }


        claimResultCcid.setOriginalUseType(listMatchDataDspDone.getUsage());
        BigDecimal clickNumber = listMatchDataDspDone.getClickNumber();
        if (null == clickNumber) {
            clickNumber = BigDecimal.ZERO;
        }
        claimResultCcid.setOriginalUseQuantity(clickNumber);
        claimResultCcid.setRoyaltyType(minimaNetRevenueContent.getRolyatyType());
        String salesTransactionId = listMatchDataDspDone.getSalesTransactionId();
        if(StringUtils.isBlank(salesTransactionId)){
            String extJson = listMatchDataDspDone.getExtJson();
            if (StringUtils.isNotBlank(extJson)) {
                net.sf.json.JSONObject jsonObject = net.sf.json.JSONObject.fromObject(extJson);
                if (null != jsonObject) {
                    boolean su0302 = jsonObject.containsKey("su0302");
                    if (su0302) {
                        net.sf.json.JSONObject objectJson = jsonObject.getJSONObject("su0302");
                        salesTransactionId = objectJson.getString("SalesTransactionId");
                    }
                }
            }
        }
        claimResultCcid.setSalesTransactionId(salesTransactionId);
        claimResultCcid.setResourceShare(new BigDecimal("100.00"));
        claimResultCcid.setRestrictions("");
        if (listMatchDataDspDone.getListFileStartTime() != null) {
            claimResultCcid.setListFileStartDate(new SimpleDateFormat("yyyy-MM-dd").format(listMatchDataDspDone.getListFileStartTime()));

        }
        if (listMatchDataDspDone.getListFileEndTime() != null) {

            claimResultCcid.setListFileEndDate(new SimpleDateFormat("yyyy-MM-dd").format(listMatchDataDspDone.getListFileEndTime()));

        }
        claimResultCcid.setDspDoneId(listMatchDataDspDone.getId());
        claimResultCcid.setFileBaseId(listDspFileBaseId);
        claimResultCcid.setFileMappingId(listMatchDataDspDone.getFileMappingId());

        claimResultCcid.setCommercialModel(listMatchDataDspDone.getCommercialModel());
        claimResultCcid.setOriginalCurrency(listDspFileBase.getCurrency());
        claimResultCcid.setHeaderId(claimCcidHandler.getClaimHeaderId());
        claimResultCcid.setBlockId(listMatchDataDspDone.getGroupCode());
        claimResultCcid.setClaimWorkPerfShare(wrkWorkHandler.getPerfIpShare());


        Map<String, WrkWorkHandler.PubObject> pubObjectForMECHMap = wrkWorkHandler.getPubObjectForMECHMap(); // 表示的MECH的权利


        //FIXME 20211025 客户需求，mec与perf输出到一个文件，不需要拆分，所以这里也同时计算mec share
        BigDecimal mecShare = BigDecimal.ZERO;
        /*if (null != pubObjectForMECHMap && pubObjectForMECHMap.size() > 0) {
            Set<String> keySet = pubObjectForMECHMap.keySet();

            for (String s : keySet) {
                WrkWorkHandler.PubObject pubObject = pubObjectForMECHMap.get(s);
                BigDecimal pubObjectPubIpShare = pubObject.getPubIpShare();
                if (pubObjectPubIpShare == null) {
                    pubObjectPubIpShare = BigDecimal.ZERO;
                }
                if (pubObjectPubIpShare.compareTo(BigDecimal.ZERO) > 0) {
                    mecShare = mecShare.add(pubObjectPubIpShare);
                }
            }


        }
        if (mecShare.compareTo(BigDecimal.valueOf(100)) > 0) {
            mecShare = BigDecimal.valueOf(100);
        }*/

        claimResultCcid.setClaimWorkMechShare(mecShare);


        claimResultCcid.setMustPerfShare(minimaNetRevenueContent.getPublicShare());
        claimResultCcid.setMustMechShare(minimaNetRevenueContent.getMechanicalShare());
        BigDecimal mustTotalShare = minimaNetRevenueContent.getTotalShare();
        if (mustTotalShare == null) {
            mustTotalShare = minimaNetRevenueContent.getPublicShare().add(minimaNetRevenueContent.getMechanicalShare());
        }
        claimResultCcid.setMustTotalShare(mustTotalShare);


        BigDecimal singleRevenue;
        BigDecimal netRenvenue;
        BigDecimal revenueBasic; // YOUTUBE的收入
        BigDecimal singleRoy; // YOUTUBE的应收权利金
        BigDecimal subscriptRoy = minimaNetRevenueContent.getSubscriptRoy();


        netRenvenue = minimaNetRevenueContent.getMaxNetRevenue();
        //如果净收入没有，根据订阅者价值获取数据做相应转换
        if (netRenvenue ==null || netRenvenue.compareTo(BigDecimal.ZERO) ==0 ){
            BigDecimal totalShare = minimaNetRevenueContent.getTotalShare();
            if (subscriptRoy.compareTo(BigDecimal.ZERO) > 0 && totalShare.compareTo(BigDecimal.ZERO)>0){
                BigDecimal divide = subscriptRoy.divide(totalShare.divide(BigDecimal.valueOf(100),6,RoundingMode.HALF_UP),6,RoundingMode.HALF_UP);
                netRenvenue = divide;
            }
        }

        singleRevenue = minimaNetRevenueContent.getConstantBaseNetRevenue();
        singleRoy = minimaNetRevenueContent.getConstantBaseRoy();
        revenueBasic = singleRevenue.multiply(clickNumber);
        BigDecimal royBasic = singleRoy.multiply(clickNumber);

        claimResultCcid.setNetRenvenue(netRenvenue);
        //claimResultCcid.setSingleRevenue(singleRevenue);

        claimResultCcid.setRevenueBasis(revenueBasic);
        claimResultCcid.setOriginalReleaseRevenueBasis(revenueBasic);
        claimResultCcid.setOriginalResourceRevenueBasis(revenueBasic);


        // 可分配 为0表示 但是没有钱
        if (netRenvenue.compareTo(BigDecimal.ZERO) <= 0) {
            claimResultCcid.setClaimFlag("N");
        } else {
            claimResultCcid.setClaimFlag("Y");
        }

        BigDecimal claimLicensorPerf = wrkWorkHandler.getPerfIpShare() == null ? BigDecimal.ZERO : wrkWorkHandler.getPerfIpShare();
        BigDecimal claimLicensorMech = mecShare == null ? BigDecimal.ZERO : mecShare;
        claimResultCcid.setClaimLicensorPerf(claimLicensorPerf);
        claimResultCcid.setClaimLicensorMech(claimLicensorMech);

        BigDecimal claimPdPerf = BigDecimal.ZERO;
        BigDecimal claimPdMech = BigDecimal.ZERO;
        claimResultCcid.setClaimPdPerf(claimPdPerf);
        claimResultCcid.setClaimPdMech(claimPdMech);

        BigDecimal claimCopconPerf = BigDecimal.ZERO;
        BigDecimal claimCopconMech = BigDecimal.ZERO;
        claimResultCcid.setClaimCopconPerf(claimCopconPerf);
        claimResultCcid.setClaimCopconMech(claimCopconMech);

        BigDecimal claimUnmatchedPerf = BigDecimal.ZERO;
        BigDecimal claimUnmatchedMech = BigDecimal.ZERO;
        claimResultCcid.setClaimUnmatchedPerf(claimUnmatchedPerf);
        claimResultCcid.setClaimUnmatchedMech(claimUnmatchedMech);

        BigDecimal claimNotCollectedPerf = new BigDecimal("100").subtract(claimLicensorPerf).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : new BigDecimal("100").subtract(claimLicensorPerf);
        BigDecimal claimNotCollectedMech = new BigDecimal("100").subtract(claimLicensorMech).compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : new BigDecimal("100").subtract(claimLicensorMech);
        claimResultCcid.setClaimNotCollectedPerf(claimNotCollectedPerf);
        claimResultCcid.setClaimNotCollectedMech(claimNotCollectedMech);

        BigDecimal mustPerfSplitShare = minimaNetRevenueContent.getPublicSplitShare().divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP);
        BigDecimal mustMechSplitShare = minimaNetRevenueContent.getMechanicalSplitShare().divide(BigDecimal.valueOf(100), 6, RoundingMode.HALF_UP);

        // 计算出所有的amount
        BigDecimal amountLicensorPerf = calcAmountByClaim(royBasic, claimLicensorPerf, mustPerfSplitShare); // MUST实际claim到的金额

        //FIXME apple music输出需要输出 combinedshare，但是实际上mech 在PERF下是不应该有钱的，所以share比例有值，金额设置成0处理,否则输出ccr时，combinedshare不好获取。
        //输出报表时注意只获取pub_id=161的值输出，否则会重复计算
        //BigDecimal amountLicensorMech = calcAmountByClaim(royBasic, claimLicensorMech, mustMechSplitShare);
        BigDecimal amountLicensorMech = BigDecimal.ZERO;
        claimResultCcid.setAmountLicensorPerf(amountLicensorPerf);
        claimResultCcid.setAmountLicensorMech(amountLicensorMech);

        BigDecimal amountPdPerf = calcAmountByClaim(royBasic, claimPdPerf, mustPerfSplitShare);
        BigDecimal amountPdMech = calcAmountByClaim(royBasic, claimPdMech, mustMechSplitShare);
        claimResultCcid.setAmountPdPerf(amountPdPerf);
        claimResultCcid.setAmountPdMech(amountPdMech);

        BigDecimal amountCopconPerf = calcAmountByClaim(royBasic, claimCopconPerf, mustPerfSplitShare);
        BigDecimal amountCopconMech = calcAmountByClaim(royBasic, claimCopconMech, mustMechSplitShare);
        claimResultCcid.setAmountCopconPerf(amountCopconPerf);
        claimResultCcid.setAmountCopconMech(amountCopconMech);

        BigDecimal amountUnmatchedPerf = calcAmountByClaim(royBasic, claimUnmatchedPerf, mustPerfSplitShare);
        BigDecimal amountUnmatchedMech = calcAmountByClaim(royBasic, claimUnmatchedMech, mustMechSplitShare);
        claimResultCcid.setAmountUnmatchedMech(amountUnmatchedMech);
        claimResultCcid.setAmountUnmatchedPerf(amountUnmatchedPerf);

        BigDecimal amountNotCollectedPerf = calcAmountByClaim(royBasic, claimNotCollectedPerf, mustPerfSplitShare);
        BigDecimal amountNotCollectedMech = calcAmountByClaim(royBasic, claimNotCollectedMech, mustMechSplitShare);
        claimResultCcid.setAmountNotCollectedMech(amountNotCollectedMech);
        claimResultCcid.setAmountNotCollectedPerf(amountNotCollectedPerf);

        BigDecimal claimLicensorCombined = calcCombinedByClaim(claimLicensorMech, mustMechSplitShare, claimLicensorPerf, mustPerfSplitShare);
        BigDecimal claimPdCombined = calcCombinedByClaim(claimPdMech, mustMechSplitShare, claimPdPerf, mustPerfSplitShare);
        BigDecimal claimCopconCombined = calcCombinedByClaim(claimCopconMech, mustMechSplitShare, claimCopconPerf, mustPerfSplitShare);
        BigDecimal claimUnmatchedCombined = calcCombinedByClaim(claimUnmatchedMech, mustMechSplitShare, claimUnmatchedPerf, mustPerfSplitShare);
        BigDecimal claimNotCollectedCombined = calcCombinedByClaim(claimNotCollectedMech, mustMechSplitShare, claimNotCollectedPerf, mustPerfSplitShare);
        claimResultCcid.setClaimLicensorCombined(claimLicensorCombined);
        claimResultCcid.setClaimPdCombined(claimPdCombined);
        claimResultCcid.setClaimCopconCombined(claimCopconCombined);
        claimResultCcid.setClaimUnmatchCombined(claimUnmatchedCombined);
        claimResultCcid.setClaimNotCollectedCombined(claimNotCollectedCombined);

        BigDecimal amountInvoicedTotal = amountLicensorMech.add(amountLicensorPerf).add(amountPdPerf).add(amountPdMech).add(amountCopconMech).add(amountCopconPerf).add(amountUnmatchedMech).add(amountUnmatchedPerf);

        String pubId = wrkWorkHandler.getPubId();
        String pubName = wrkWorkHandler.getPubName();
        if (StringUtils.isBlank(pubId)) {
            pubName = "MUSTPR";
            pubId = "MUSTPR";
        }
        claimResultCcid.setPubName(pubName);
        claimResultCcid.setPubId(pubId);

        // Y表示可分配的
        if ("Y".equalsIgnoreCase(claimResultCcid.getClaimFlag())) {
            // ==0表示 没有claim到 没有权利
            if (amountInvoicedTotal.compareTo(BigDecimal.ZERO) <= 0) {
                claimResultCcid.setClaimFlag("R");
            } else {
                // 有权利分且有钱
                claimResultCcid.setClaimFlag("T");
            }
        }

        if (claimLicensorPerf.compareTo(new BigDecimal("100")) > 0) {
            claimResultCcid.setClaimFlag("O"); // over 溢出
        }

        claimResultCcid.setAmountInvoicedTotal(amountInvoicedTotal);


        String appliedTraiff = "";
        BigDecimal royality = BigDecimal.ZERO;
        if (StringUtils.equalsIgnoreCase(minimaNetRevenueContent.getRolyatyType(), "P")) {
            appliedTraiff = minimaNetRevenueContent.getTotalShare() + "";
        } else if (StringUtils.equalsIgnoreCase(minimaNetRevenueContent.getRolyatyType(), "M")) {
            //FIXME 20210702 明威要求改成与youtube给到的pdf中的一致,在royalty type=P的情况下，不输出
            royality = singleRoy;
        }

        claimResultCcid.setAppliedTariff(appliedTraiff);

        claimResultCcid.setRoyality(royality);


        //封装作词，作曲，发布者信息写入到扩展属性
        setExtInfo(wrkWorkHandler, claimResultCcid);


        // 如果是竞争对手的作品则标记为F TODO 20210308
        Boolean otherSocWrk = wrkWorkHandler.isOtherSocWrk();
        if (null != otherSocWrk && otherSocWrk) {
            claimResultCcid.setClaimFlag("F");
            return claimResultCcid;
        }

        if(CollectionUtils.isNotEmpty(wrkWorkHandler.getPerFilterSet()) && wrkWorkHandler.getPerFilterSet().contains(claimResultCcid.getPubId())){
            claimResultCcid.setClaimFlag("F");
        }


        return claimResultCcid;
    }

    public void setExtInfo(WrkWorkHandler workHandler, ClaimResultCcid claimResultCcid) {


        String extJson = claimResultCcid.getExtInfo();
        List<WrkWorkIpShare> workIpShareList = workHandler.getWorkIpShareList();
        if (workIpShareList == null) {
            workIpShareList = new ArrayList<>();
        }
        Set<String> composerAuthorSet = new HashSet<>();
        Set<String> publisherSet = new HashSet<>();
        for (WrkWorkIpShare wrkWorkIpShare : workIpShareList) {
            String ipType = wrkWorkIpShare.getWorkIpRole();
            String ipName = StringUtils.isBlank(wrkWorkIpShare.getDummyNameRoman()) ? wrkWorkIpShare.getName() : wrkWorkIpShare.getDummyNameRoman();

            if (StringUtils.isBlank(ipName)) {
                continue;
            }

            if (StringUtils.equalsAnyIgnoreCase(ipType, "CA", "A", "C")) {
                composerAuthorSet.add(ipName);

            } else if (StringUtils.equalsAnyIgnoreCase(ipType, "E", "SE")) {
                publisherSet.add(ipName);
            }


        }

        JSONObject jsonObject = null;
        if (StringUtils.isBlank(extJson)) {
            jsonObject = new JSONObject();
        } else {
            try {
                jsonObject = (JSONObject) JSONObject.parse(extJson);
            } catch (Exception ex) {
                jsonObject = new JSONObject();
            }

        }


        //获取作词作曲者，截取前10个
        if (composerAuthorSet.size() > 0) {
            ArrayList<String> composerAuthorList = new ArrayList<>(composerAuthorSet);
            if (composerAuthorSet.size() > 10) {
                List<String> tempList = composerAuthorList.subList(0, 10);
                jsonObject.put("composerAuthors", StringUtils.join(tempList.toArray(new String[tempList.size()]), ";"));
            } else {
                jsonObject.put("composerAuthors", StringUtils.join(composerAuthorSet.toArray(new String[composerAuthorSet.size()]), ";"));
            }

        }

        if (publisherSet.size() > 0) {
            ArrayList<String> publisherList = new ArrayList<>(publisherSet);
            if (publisherSet.size() > 10) {

                List<String> tempList = publisherList.subList(0, 10);
                jsonObject.put("publishers", StringUtils.join(tempList.toArray(new String[tempList.size()]), ";"));

            } else {
                jsonObject.put("publishers", StringUtils.join(publisherSet.toArray(new String[publisherSet.size()]), ";"));
            }

        }
        claimResultCcid.setExtInfo(jsonObject.toJSONString());


    }
}
