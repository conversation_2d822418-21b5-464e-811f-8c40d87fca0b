package tw.org.must.must.core.task.claim.meta;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tw.org.must.must.common.constants.DicConstants;
import tw.org.must.must.common.util.CollectionGroupUtil;
import tw.org.must.must.core.service.calcIpShare.CalcIpShareService;
import tw.org.must.must.core.service.claim.*;
import tw.org.must.must.core.service.distdata.DistDataSnapshotService;
import tw.org.must.must.core.service.distdata.DistDataSnapshotWorkIpShareService;
import tw.org.must.must.core.service.distdata.DistDataSnapshotWorkService;
import tw.org.must.must.core.service.list.ListDspFileBaseService;
import tw.org.must.must.core.service.list.ListDspFileDataMappingService;
import tw.org.must.must.core.service.list.ListMatchDataDspDoneService;
import tw.org.must.must.core.service.ref.RefSocietyRightService;
import tw.org.must.must.core.service.sys.SysDictItemService;
import tw.org.must.must.core.service.wrk.WrkWorkService;
import tw.org.must.must.core.service.wrk.WrkWorkTitleService;
import tw.org.must.must.core.task.claim.ClaimCcidHandler;
import tw.org.must.must.core.task.claim.WrkWorkHandler;
import tw.org.must.must.core.task.claim.youtube.ClaimCcidCalcBase;
import tw.org.must.must.core.task.claim.youtube.YoutubeMaxShareHandle;
import tw.org.must.must.model.claim.*;
import tw.org.must.must.model.distdata.DistDataSnapshotWork;
import tw.org.must.must.model.distdata.DistDataSnapshotWorkIpShare;
import tw.org.must.must.model.list.ListDspFileBase;
import tw.org.must.must.model.list.ListDspFileDataMapping;
import tw.org.must.must.model.list.ListMatchDataDspDone;
import tw.org.must.must.model.sys.SysDictItem;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkIpShare;
import tw.org.must.must.model.wrk.WrkWorkSource;
import tw.org.must.must.model.wrk.WrkWorkTitle;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class ClaimCcidResultFbClaim extends ClaimCcidCalcBase<ClaimResultPreclaim> {

    private Logger logger = LoggerFactory.getLogger(ClaimCcidResultFbClaim.class);


    @Autowired
    private ClaimMinimaInfoService claimMinimaInfoService;

    @Autowired
    private ListDspFileBaseService listDspFileBaseService;

    @Autowired
    private ClaimSignMemberService claimSignMemberService;


    @Autowired
    private ListDspFileDataMappingService listDspFileDataMappingService;

    @Autowired
    private DistDataSnapshotWorkService distDataSnapshotWorkService;

    @Autowired
    private DistDataSnapshotWorkIpShareService distDataSnapshotWorkIpShareService;

    @Autowired
    private ClaimWorkIpshareService claimWorkIpshareService;

    @Autowired
    protected ClaimCcidHeaderService claimCcidHeaderService;

    @Autowired
    protected ClaimFilterOthersocService claimFilterOthersocService;

    @Autowired
    protected ClaimFilterOtherIpService claimFilterOtherIpService;

    @Autowired
    protected RefSocietyRightService refSocietyRightService;

    @Autowired
    protected ClaimPremDspService claimPremDspService;


    @Autowired
    protected DistDataSnapshotService distDataSnapshotService;
    @Autowired
    protected YoutubeMaxShareHandle youtubeMaxShareHandle;

    @Autowired
    protected ListMatchDataDspDoneService listMatchDataDspDoneService;

    @Autowired
    protected CalcIpShareService calcIpShareService;

    @Autowired
    private WrkWorkTitleService wrkWorkTitleService;

    @Autowired
    private WrkWorkService wrkWorkService;


    @Autowired
    private ClaimResultPreclaimService claimResultPreclaimService;

    @Autowired
    private ClaimFilterOtherWorkService claimFilterOtherWorkService;

    @Autowired
    private SysDictItemService sysDictItemService;


    private static ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("ccidCalcThread-pool-%d").build();

    private static ExecutorService threadPool = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 2, Runtime.getRuntime().availableProcessors() * 2,
            0L, TimeUnit.MINUTES, new LinkedBlockingQueue<>(), threadFactory);

    private final static Boolean TEST = true;


    private Set<String> synSet = Collections.synchronizedSet(new HashSet<>());

    @Override
    public boolean isMatched(ClaimCcidHeader claimCcidHeader) {
        if (StringUtils.equalsIgnoreCase("fbcsv", claimCcidHeader.getFileType())) {
            return true;
        }
        return false;
    }

    /**
     * 初始化分配计算所需要基础参数ClaimCcidHandler
     *
     * @param claimCcidHeader
     * @return
     */
    @Override
    public ClaimCcidHandler initClaimCcidHandler(ClaimCcidHeader claimCcidHeader) {
        ClaimCcidHandler claimCcidHandler = new ClaimCcidHandler();

        // 初始化 竞争对手数据

        claimCcidHandler.setClaimHeaderId(claimCcidHeader.getId());

        claimCcidHandler.setVersion(claimCcidHeader.getRecever());

        String claimMinimaInfoIds = claimCcidHeader.getClaimMinimaInfoIds() == null ? "" : claimCcidHeader.getClaimMinimaInfoIds();

        if (StringUtils.isBlank(claimMinimaInfoIds) && StringUtils.isBlank(claimCcidHeader.getFileQueueIds())) {
            setClaimCCidHeaderStatusErrorHandle(claimCcidHeader, "当前header配置错误！");
        }


//        claimCcidHandler.setClaimFilterOthersocHashMap(initClaimFilterOtherSocData());
        claimCcidHandler.setRefSocietyRightMap(initSysterSociety());
        claimCcidHandler.setRefSocietyList(initSociety());


        XxlJobLogger.log("当前可用的ccidHeader中的claimMinimaInfoIds为：{}", claimMinimaInfoIds);
        String[] claimMinimaInfoIdSplit = claimMinimaInfoIds.split(";");
        Date startDate = claimCcidHeader.getStartDate();
        Date endDate = claimCcidHeader.getEndDate();
        String startDateStr = null;
        if (startDate != null) {
            startDateStr = new SimpleDateFormat("yyyy-MM-dd").format(startDate);
        }
        String endDateStr = null;

        if (endDate != null) {
            endDateStr = new SimpleDateFormat("yyyy-MM-dd").format(endDate);
        }


        List<ClaimMinimaInfo> claimMinimaInfoList = new ArrayList<>();
        for (String claimMinimaInfoId : claimMinimaInfoIdSplit) {
            if (StringUtils.isBlank(claimMinimaInfoId)) {
                continue;
            }
            Long cmiId = Long.valueOf(claimMinimaInfoId);

            ClaimMinimaInfo claimMinimaInfo = claimMinimaInfoService.getById(cmiId);
            if (claimMinimaInfo == null) {
                setClaimCCidHeaderStatusErrorHandle(claimCcidHeader, "ClaimMinimaInfoId為：" + cmiId + ",在ClaimMinimaInfo表中查詢無數據~！");
                break;
            }

           /* List<ClaimMinimaInfo> products = claimMinimaInfoService.getClaimMinimaInfoByProductFullName(claimMinimaInfo.getProductFullName());
            List<ClaimMinimaInfo> resultProducts = products.stream().filter(it -> !"SVOD".equalsIgnoreCase(it.getProductShortName())).collect(Collectors.toList());

            claimCcidHandler.setSvodChildList(resultProducts);*/

            claimMinimaInfoList.add(claimMinimaInfo);
        }

        claimCcidHandler.setClaimMinimaInfoList(claimMinimaInfoList);

        String fileQueueIds = claimCcidHeader.getFileQueueIds();
        List<Long> fileQueueIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(fileQueueIds)) {
            try {
                String[] split = fileQueueIds.split(",");
                for (String s : split) {
                    fileQueueIdList.add(Long.valueOf(s));
                }
            } catch (NumberFormatException e) {
                XxlJobLogger.log("claimCcidHeader 字段 file_queue_ids【{}】 不合符要求！", fileQueueIds);
                XxlJobLogger.log(e);
            }
        }

        List<ListDspFileBase> listDspFileBaseList = new ArrayList<>();

        //根据上传文件id初始化数据
        if (!fileQueueIdList.isEmpty()) {

            listDspFileBaseList = listDspFileBaseService.getListDspFileBaseByQueueIdsAndFileDate(fileQueueIdList, startDateStr, endDateStr);
            if (listDspFileBaseList.isEmpty()) {
                setClaimCCidHeaderStatusErrorHandle(claimCcidHeader, "根据ListFilqQueueIds = " + fileQueueIds + ", 开始时间：" + startDateStr + ", 结束时间：" + endDateStr + ", 未找到ListDspFileBase！");
                return null;
            }
            List<Long> claimMinimaInfoIdList = listDspFileBaseList.stream().map(ListDspFileBase::getClaimMinimaInfoId).collect(Collectors.toList());
            if (claimMinimaInfoIdList.size() > 0) {
                List<ClaimMinimaInfo> claimMinimaInfos = claimMinimaInfoService.listByIds(claimMinimaInfoIdList);
                claimMinimaInfoList.addAll(claimMinimaInfos);
                claimCcidHandler.setClaimMinimaInfoList(claimMinimaInfoList);
            }
            //开始时间如果为空，自动从ListDspFileBaseList中获取最大，最小值
            if (startDate == null || endDate == null) {
                setClaimHeaderByFileList(claimCcidHeader,listDspFileBaseList);
            }

        } else {
            List<Long> claimMinimaInfoIdList = claimMinimaInfoList.stream().map(ClaimMinimaInfo::getId).collect(Collectors.toList());
            // 根據  claimMinimaInfoIdList in查詢 減少查詢次數
            listDspFileBaseList = listDspFileBaseService.getListDspFileBaseByMinimaInfoIdListAndFileDate(claimMinimaInfoIdList, startDateStr, endDateStr);
            if (null == listDspFileBaseList || listDspFileBaseList.size() == 0) {
                setClaimCCidHeaderStatusErrorHandle(claimCcidHeader, "根据minimaIdList = " + claimMinimaInfoIdList + ", 开始时间：" + startDateStr + ", 结束时间：" + endDateStr + ", 未找到ListDspFileBase！");
                return null;
            }
            //开始时间如果为空，自动从ListDspFileBaseList中获取最大，最小值
            if (startDate == null || endDate == null) {
                setClaimHeaderByFileList(claimCcidHeader,listDspFileBaseList);
            }
        }

        claimCcidHandler.setListDspFileBaseList(listDspFileBaseList);
        claimCcidHandler.setFileBaseIdAndNameMap(listDspFileBaseList.stream().collect(Collectors.toMap(it -> it.getId(), Function.identity(), (a, b) -> a)));

        Map<String, ClaimSignMember> mechaincalSocList = new HashMap<>();
        Map<String, ClaimSignMember> mechaincalIPList = new HashMap<>();

        claimCcidHandler.getRightTypeList().add("PER");

        Long claimSetId = claimCcidHeader.getClaimSetId();

        if (claimMinimaInfoList.size() > 0) {
            ClaimMinimaInfo claimMinimaInfo = claimMinimaInfoList.get(0);
            claimSetId = claimMinimaInfo.getClaimSetId();
        }

        //查询参数待传进来
        List<ClaimSignMember> selectAllListByClaimSetId = claimSignMemberService.selectAllListByClaimSetId(claimSetId);

        if (null != selectAllListByClaimSetId && selectAllListByClaimSetId.size() > 0) {
            // 抽取委托重制的协会   用于缓存需要计算重制的协会
            selectAllListByClaimSetId.forEach(signMember -> {
                Integer socNo = signMember.getSocNo();
                String ipBaseNo = signMember.getIpBaseNo();
                Map<String, ClaimSignMember> socMap = new HashMap<>();
                if (null != socNo) {
                    mechaincalSocList.put(signMember.getSocNo() + "", signMember);

                    claimCcidHandler.getRightTypeList().add("MEC");
                }
                if (StringUtils.isNotBlank(ipBaseNo)) {
                    Map<String, ClaimSignMember> ipMap = new HashMap<>();
                    mechaincalIPList.put(signMember.getIpBaseNo(), signMember);

                    claimCcidHandler.getRightTypeList().add("MEC");
                }
            });
            claimCcidHandler.setMechaincalIPList(mechaincalIPList);
            claimCcidHandler.setMechaincalSocList(mechaincalSocList);
        }


        //初始化pre claim label 过滤关键词
       /* List<SysDictItem> sysDictItemList = sysDictItemService.getByDictCode(DicConstants.PRE_CLAIM_FILTER_LABEL_CODE);
        List<String> labels = sysDictItemList.stream().filter(a -> a != null && StringUtils.isNotBlank(a.getItemText())).map(SysDictItem::getItemText).map(String::toLowerCase).collect(Collectors.toList());

        claimCcidHandler.setFilterLabel(labels);*/


        return claimCcidHandler;
    }

    @Override
    public void handleData(ClaimCcidHandler claimCcidHandler, List<ListMatchDataDspDone> listMatchDataDspDoneList) {
        try {
            // 这里的数据需要放在最外层   用ThreadLocal
            List<ClaimResultPreclaim> claimResultPreclaimList = Collections.synchronizedList(new ArrayList<>());
            List<ClaimWorkIpshare> claimWorkIpShareList = Collections.synchronizedList(new ArrayList<>());
            List<DistDataSnapshotWork> distDataSnapshotWorkList = Collections.synchronizedList(new ArrayList<>());
            List<DistDataSnapshotWorkIpShare> distDataSnapshotWorkIpShareListAll = Collections.synchronizedList(new ArrayList<>());

            if (claimCcidHandler.getIsBatchPartial() && CollectionUtils.isEmpty(listMatchDataDspDoneList)) {
                List<Long> fileMappingIds = claimResultPreclaimService.getFileMappingIdsByHeaderIdAndNotClaimFlag(claimCcidHandler.getClaimHeaderId(), "T");
                listMatchDataDspDoneList = listMatchDataDspDoneService.getByFileMappingId(fileMappingIds);
            }
            if (CollectionUtils.isEmpty(listMatchDataDspDoneList)) {
                return;
            }

            // 每个线程数量
            int threadSize = 1000;
            // 总的数据量
            int dspDoneListSize = listMatchDataDspDoneList.size();
            // 一共的线程数
            List<List<ListMatchDataDspDone>> splitList = CollectionGroupUtil.groupListByQuantity(listMatchDataDspDoneList, threadSize);
            int threadNum = splitList.size();

            XxlJobLogger.log("数据量：" + dspDoneListSize + "开启线程数：" + threadNum);

            CountDownLatch countDownLatch = new CountDownLatch(threadNum);
            for (List<ListMatchDataDspDone> resultList : splitList) {
                threadPool.execute(() -> {
                    try {
                        if (resultList == null || resultList.size() == 0) {
                            countDownLatch.countDown();
                            return;
                        }
                        XxlJobLogger.log("当前数量为：====" + countDownLatch.getCount());
                        long start = System.currentTimeMillis();
                        handleSingleData(claimCcidHandler, claimResultPreclaimList, claimWorkIpShareList, distDataSnapshotWorkList, distDataSnapshotWorkIpShareListAll, resultList);
                        XxlJobLogger.log(Thread.currentThread().getName() + " 线程启动；当前ListMatchDataDspDone的id为：" + resultList.get(0).getId() + "；总共处理了 ： " + threadSize + " ； 所消耗的时间为 ：" + (System.currentTimeMillis() - start) + "毫秒");
                    } catch (Exception ex) {

                        XxlJobLogger.log( ex);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            XxlJobLogger.log("等待所有线程完毕...................：====");
            //等待线程全部
            countDownLatch.await();


            XxlJobLogger.log("所有线程已执行完毕，开始进行数据插入" + countDownLatch.getCount());
//            if (distDataSnapshotWorkList.size() > 0) {
//                distDataSnapshotWorkService.insertDuplicateList(distDataSnapshotWorkList);
//            }
//            if (distDataSnapshotWorkIpShareListAll.size() > 0) {
//                distDataSnapshotWorkIpShareService.insertDuplicateList(distDataSnapshotWorkIpShareListAll);
//            }
            if (claimResultPreclaimList.size() > 0) {
                if (claimCcidHandler.getIsBatchPartial()) {
                    // 如果部分再次计算的claimFlag = T 需要覆盖更新数据
                    List<ClaimResultPreclaim> preclaims = claimResultPreclaimList.stream().filter(t -> "T".equals(t.getClaimFlag())).collect(Collectors.toList());
                    if (!preclaims.isEmpty()) {
                        // fileMappingId和dataUnqiueKey是一对一对关系，索引建立的是组合索引 headerId 和 dataUniqueKey
                        claimResultPreclaimService.deleteByHeaderIdAndDataUniqueKey(claimCcidHandler.getClaimHeaderId(), preclaims.stream().map(ClaimResultPreclaim::getDataUniqueKey).collect(Collectors.toList()));
                        claimResultPreclaimService.addList(preclaims);
                    }
                } else {
                    claimResultPreclaimService.addList(claimResultPreclaimList);
                }
            }
            if (claimWorkIpShareList.size() > 0) {
                claimWorkIpshareService.insertDuplicateClaimWorkIpshareList(claimWorkIpShareList,claimCcidHandler.getClaimHeaderId());
            }
            XxlJobLogger.log("所有数据批量插入完毕。。。。。。。。。。。" + countDownLatch.getCount());
        } catch (Exception e) {
            XxlJobLogger.log("dealMatchDspDoneList出错了！");
            XxlJobLogger.log( e);
            e.printStackTrace();
        }
    }

    ReentrantLock reentrantLock = new ReentrantLock();

    /**
     * 处理一条done数据
     *
     * @param claimCcidHandler
     * @param claimResultPreclaimList
     * @param claimWorkIpShareList
     * @param distDataSnapshotWorkList
     * @param distDataSnapshotWorkIpShareListAll
     * @param listMatchDataDspDoneList
     */
    private void handleSingleData(ClaimCcidHandler claimCcidHandler, List<ClaimResultPreclaim> claimResultPreclaimList, List<ClaimWorkIpshare> claimWorkIpShareList, List<DistDataSnapshotWork> distDataSnapshotWorkList, List<DistDataSnapshotWorkIpShare> distDataSnapshotWorkIpShareListAll, List<ListMatchDataDspDone> listMatchDataDspDoneList) {
        for (ListMatchDataDspDone listMatchDataDspDone : listMatchDataDspDoneList) {
            try {
                String workUniqueKey = listMatchDataDspDone.getWorkUniqueKey();

                if (StringUtils.isBlank(workUniqueKey)) {
                    XxlJobLogger.log("作品id: " + listMatchDataDspDone.getMatchWorkId() + "workUniqueKey为空");
                    continue;
                }

//                ClaimFilterOthersoc claimFilterOthersoc = claimCcidHandler.getClaimFilterOthersocHashMap().get(workUniqueKey);
                List<ClaimFilterOtherWork> claimFilterOtherWork = claimFilterOtherWorkService.getByParam(claimCcidHandler.getCompany(),listMatchDataDspDone.getMatchWorkId(),listMatchDataDspDone.getMatchWorkSocietyCode(),null,null);

                if (!CollectionUtils.isEmpty(claimFilterOtherWork)) {
                    XxlJobLogger.log("作品workUniqueKey:" + workUniqueKey + "为MUST競爭對手作品");
                    WrkWorkHandler wrkWorkHandler = new WrkWorkHandler();
                    WrkWork wrkWork = new WrkWork();
                    wrkWork.setWorkId(listMatchDataDspDone.getMatchWorkId());
                    wrkWork.setWorkSocietyCode(listMatchDataDspDone.getMatchWorkSocietyCode());
                    wrkWork.setWorkUniqueKey(workUniqueKey);
                    wrkWorkHandler.setWrkWrok(wrkWork);
                    wrkWorkHandler.setOtherSocWrk(true);
                    createClaimResultData(wrkWorkHandler, claimResultPreclaimList, distDataSnapshotWorkList, listMatchDataDspDone, claimCcidHandler);
                    continue;
                }

                String mapUniqueKey = workUniqueKey + claimCcidHandler.getClaimHeaderId() + "";
                ConcurrentHashMap<String, WrkWorkHandler> workUniqueKeyMap = claimCcidHandler.getWorkUniqueKeyMap();

                WrkWorkHandler wrkWorkHandler = workUniqueKeyMap.get(mapUniqueKey);

                //FIXME 这里解决多线程并发问题，可能会多个线程会同时进来操作一个mapUniqueKey
                if (wrkWorkHandler == null) {

                    boolean addWait = true;
                    reentrantLock.lock();
                    try{
                        if (wrkWorkHandler == null) {

                            //set中如果已经存在，使用add会添加失败，根据这个判断是否可以继续往下处理
                            addWait = synSet.add(mapUniqueKey);
                        }
                    }catch (Exception ex){

                    }finally {
                        reentrantLock.unlock();
                    }




                    //等待work hander初始化完成，休眠10秒
                    while (!addWait && synSet.contains(mapUniqueKey)) {
                        Thread.sleep(10000);
                    }

                }


                if (null == wrkWorkHandler) {
                    try {
                        wrkWorkHandler = new WrkWorkHandler();

                        WrkWork wrkWork = wrkWorkService.getWrkWorkByWorkUniqueKey(workUniqueKey);
                        if (null == wrkWork) {
                            // 没有work 记录到checked表中
                            ccidChecked(null,claimCcidHandler.getClaimHeaderId(),listMatchDataDspDone);
                            XxlJobLogger.log("根據作品workUniqueKey:" + workUniqueKey + "查無此作品");
                            synSet.remove(mapUniqueKey);
                            continue;
                        }

                        wrkWorkHandler.setWrkWrok(wrkWork);


                        //查询作品标题信息
                        /*List<WrkWorkTitle> wrkWorkTitleByWorkUniqueKey = wrkWorkTitleService.getWrkWorkTitleByWorkUniqueKey(workUniqueKey);
                        wrkWorkHandler.setWorkTitleList(wrkWorkTitleByWorkUniqueKey);*/
                        WrkWorkTitle wrkWorkTitle = wrkWorkTitleService.getWorkTitlePriorOT(workUniqueKey);
                        wrkWorkHandler.setWorkTitle(wrkWorkTitle);

                        //查询作品 source信息，用来处理youtube最大化需求
                        List<WrkWorkSource> workSourceList = wrkWorkService.getWorkSourceListByWorkUniqueKey(workUniqueKey);
                        if(CollectionUtils.isEmpty(workSourceList)){
                            wrkWorkHandler.setSourceType((byte)1);
                        } else {
                            List<WrkWorkSource> collect = workSourceList.stream().filter(item -> "PRE".equalsIgnoreCase(item.getSourceType())
                                    || "TOC".equalsIgnoreCase(item.getSourceType())).collect(Collectors.toList());
                            if(collect.size() > 0 ){
                                wrkWorkHandler.setSourceType((byte)3);
                            } else {
                                wrkWorkHandler.setSourceType((byte)2);
                            }
                        }

                        DistDataSnapshotWork distDataSnapshotWork = cloneSnapWorkByDspDone(wrkWork, claimCcidHandler.getClaimHeaderId(), listMatchDataDspDone);
                        if (null == distDataSnapshotWork) {
                            XxlJobLogger.log("作品workUniqueKey为:" + wrkWorkHandler.getWrkWrok().getWorkUniqueKey() + " 进行快照work的时候返回null");

                            synSet.remove(mapUniqueKey);
                            continue;
                        }
                        distDataSnapshotWorkList.add(distDataSnapshotWork);

                        if (createClaimWorkIpShareAndResultNew(claimCcidHandler, claimResultPreclaimList, claimWorkIpShareList, distDataSnapshotWorkList, distDataSnapshotWorkIpShareListAll, listMatchDataDspDone, workUniqueKey, wrkWorkHandler, wrkWork)) {
                            XxlJobLogger.log("当前作品workUniqueKey为 ： " + wrkWorkHandler.getWrkWrok().getWorkUniqueKey() + " 无相关ipShare，不进行计算");
                            synSet.remove(mapUniqueKey);
                            continue;
                        }
                        workUniqueKeyMap.put(mapUniqueKey, wrkWorkHandler);
                        //添加成功解除锁

                    } catch (Exception ex) {
                        XxlJobLogger.log(ex.getMessage() + "當前異常id為：======================" + listMatchDataDspDone.getId(), ex);
                        ex.printStackTrace();
                    } finally {
                        synSet.remove(mapUniqueKey);
                    }

                } else {
                    // 已经存在的如何处理
                    List<WrkWorkIpShare> workIpShareList = wrkWorkHandler.getWorkIpShareList();

                    if (null == workIpShareList || workIpShareList.size() < 1) {
                        // TODO 如果没有ipshare如何处理  claim到的work还需要吗？
                        createClaimResultData(wrkWorkHandler, claimResultPreclaimList, distDataSnapshotWorkList, listMatchDataDspDone, claimCcidHandler);
                        continue;
                    }

                    createClaimResultData(wrkWorkHandler, claimResultPreclaimList, distDataSnapshotWorkList, listMatchDataDspDone, claimCcidHandler);
                }

            } catch (Exception e) {
                XxlJobLogger.log(e.getMessage() + "當前異常id為：======================" + listMatchDataDspDone.getId());
                e.printStackTrace();
                continue;
            }
        }
    }


    @Override
    public void createClaimResultData(WrkWorkHandler wrkWorkHandler, List<ClaimResultPreclaim> claimResultPreclaimList, List<DistDataSnapshotWork> distDataSnapshotWorkList, ListMatchDataDspDone listMatchDataDspDone, ClaimCcidHandler claimCcidHandler) {

        if(wrkWorkHandler.getPerFilterSet() == null || wrkWorkHandler.getMecFilterSet() == null){
            List<ClaimFilterOthersoc> claimFilterOthersocList = claimFilterOthersocService.getByParam(claimCcidHandler.getCompany(), listMatchDataDspDone.getMatchWorkId(),listMatchDataDspDone.getMatchWorkSocietyCode(),null,null,null );

            List<ClaimFilterOtherIp> claimFilterOtherIpList = claimFilterOtherIpService.getByParam(claimCcidHandler.getCompany(), listMatchDataDspDone.getMatchWorkId(),listMatchDataDspDone.getMatchWorkSocietyCode(),null,null,null,null );

            Set<String> perFilterSet = new HashSet<>();
            Set<String> mecFilterSet = new HashSet<>();
            if(!CollectionUtils.isEmpty(claimFilterOthersocList)){
                perFilterSet.addAll(claimFilterOthersocList.stream().filter(c-> c.getPer() == 1).map(c -> c.getSocietyCode() + "").collect(Collectors.toSet()));
                mecFilterSet.addAll(claimFilterOthersocList.stream().filter(c-> c.getMec() == 1).map(c -> c.getSocietyCode() + "").collect(Collectors.toSet()));
            }

            if(!CollectionUtils.isEmpty(claimFilterOtherIpList)){
                perFilterSet.addAll(claimFilterOtherIpList.stream().filter(c-> c.getPer() == 1).map(c -> c.getIpBaseNo()).collect(Collectors.toSet()));
                mecFilterSet.addAll(claimFilterOtherIpList.stream().filter(c-> c.getMec() == 1).map(c -> c.getIpBaseNo()).collect(Collectors.toSet()));
            }

            wrkWorkHandler.setPerFilterSet(perFilterSet);
            wrkWorkHandler.setMecFilterSet(mecFilterSet);
        }
        //输出per 部分
        ClaimResultPreclaim claimResultPreclaim = createClaimResultDataBean(claimCcidHandler, listMatchDataDspDone, wrkWorkHandler);
        // 校验ccid是否需要 入checked表
        if (claimResultPreclaim == null){
            ccidChecked("E2", claimCcidHandler.getClaimHeaderId(), listMatchDataDspDone);
        }else {
            if (claimResultPreclaim.getClaimFlag() != null
                    && !"T".equals(claimResultPreclaim.getClaimFlag())){
                ccidChecked(claimResultPreclaim.getClaimFlag(), claimCcidHandler.getClaimHeaderId(), listMatchDataDspDone);
            }
        }
        if (claimResultPreclaim.getMechanicalShare().compareTo(BigDecimal.ZERO) == 1 && "161".equals(claimResultPreclaim.getPubId())) {
            XxlJobLogger.log("001.{} --- fileMappingId: {}, mechanicalShare:{}, pubId:{}, performanceShare:{}", Thread.currentThread().getName(),
                    claimResultPreclaim.getFileMappingId(), claimResultPreclaim.getMechanicalShare(), claimResultPreclaim.getPubId(), claimResultPreclaim.getPerformanceShare());
        }
        claimResultPreclaimList.add(claimResultPreclaim);
        //检查是否需要输出mec部分，如果需要，增加mec部分输出
        addMecData(claimResultPreclaim, wrkWorkHandler, claimResultPreclaimList);

    }

    /**
     * 增加 mec部分数据
     *
     * @param claimResultPreclaim
     * @param wrkWorkHandler
     * @param claimResultPreclaimList
     */
    private void addMecData(ClaimResultPreclaim claimResultPreclaim, WrkWorkHandler wrkWorkHandler, List<ClaimResultPreclaim> claimResultPreclaimList) {

        Map<String, WrkWorkHandler.PubObject> pubObjectForMECHMap = wrkWorkHandler.getPubObjectForMECHMap();
        if (null != pubObjectForMECHMap && pubObjectForMECHMap.size() > 0) {
            Set<String> keySet = pubObjectForMECHMap.keySet();

            keySet.forEach(ipNameNoKey -> {
                WrkWorkHandler.PubObject pubObject = pubObjectForMECHMap.get(ipNameNoKey);

                if (pubObject.getPubIpShare().compareTo(BigDecimal.ZERO) > 0) {
                    ClaimResultPreclaim claimResultPreClaim = copyMecClaimResultByPerf(claimResultPreclaim, null, pubObject,wrkWorkHandler);
                    if (claimResultPreClaim.getMechanicalShare().compareTo(BigDecimal.ZERO) == 1 && "161".equals(claimResultPreClaim.getPubId())) {
                        XxlJobLogger.log("001.1{} --- fileMappingId: {}, mechanicalShare:{}, pubId:{}, performanceShare:{}", Thread.currentThread().getName(),
                                claimResultPreClaim.getFileMappingId(), claimResultPreClaim.getMechanicalShare(), claimResultPreClaim.getPubId(), claimResultPreClaim.getPerformanceShare());
                    }
                    claimResultPreclaimList.add(claimResultPreClaim);
                }

            });

        }

    }

    /**
     * 创建mec对象
     *
     * @param claimResultPreclaim
     * @param pubObject
     * @return
     */
    @Override
    protected ClaimResultPreclaim copyMecClaimResultByPerf(ClaimResultPreclaim claimResultPreclaim, ClaimCcidHandler claimCcidHandler, WrkWorkHandler.PubObject pubObject, WrkWorkHandler wrkWorkHandler) {
        ClaimResultPreclaim resultClaim = new ClaimResultPreclaim();
        BeanUtils.copyProperties(claimResultPreclaim, resultClaim);

        resultClaim.setPubName(pubObject.getPubName());
        resultClaim.setPubId(pubObject.getPubId());


        BigDecimal claimLicensorMech = pubObject.getPubIpShare();
        resultClaim.setPerformanceShare(BigDecimal.ZERO); //mec部分不输出perform部分
        resultClaim.setMechanicalShare(claimLicensorMech);


        // 如果 claimLicensorPerf >0 或者是 claimLicensorMech >0 表示的是Y 可分配， 如果2个都是0表示的是N   如果其中任意一个存在>100的 表示的是O
        if (claimLicensorMech.compareTo(BigDecimal.ZERO) == 0) {
            resultClaim.setClaimFlag("N");
        } else {
            resultClaim.setClaimFlag("T");
        }

        Boolean otherSocWrk = wrkWorkHandler.isOtherSocWrk();
        if (null != otherSocWrk && otherSocWrk) {
            resultClaim.setClaimFlag("F");
            return resultClaim;
        }

        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(wrkWorkHandler.getMecFilterSet()) && wrkWorkHandler.getMecFilterSet().contains(resultClaim.getPubId())){
            resultClaim.setClaimFlag("F");
            return resultClaim;
        }

        return resultClaim;
    }


    @Override
    public ClaimResultPreclaim createClaimResultDataBean(ClaimCcidHandler claimCcidHandler, ListMatchDataDspDone listMatchDataDspDone, WrkWorkHandler wrkWorkHandler) {
        ClaimResultPreclaim claimResultPreclaim = new ClaimResultPreclaim();
        claimResultPreclaim.init();

        claimResultPreclaim.setHeaderId(claimCcidHandler.getClaimHeaderId());
        Long listDspFileBaseId = listMatchDataDspDone.getFileBaseId();
        claimResultPreclaim.setFileBaseId(listDspFileBaseId);
        Long fileMappingId = listMatchDataDspDone.getFileMappingId();
        claimResultPreclaim.setFileMappingId(fileMappingId);
        ListDspFileDataMapping listDspFileDataMapping = listDspFileDataMappingService.getById(fileMappingId);
        if (listDspFileDataMapping == null) {
            XxlJobLogger.log("查找不到原始数据，跳过该条" + fileMappingId);
            return null;
        }

        claimResultPreclaim.setCompId(listDspFileDataMapping.getCompId());
        claimResultPreclaim.setOriginalTitle(listDspFileDataMapping.getTitle());

        if (listMatchDataDspDone.getMatchWorkSocietyCode().compareTo(999) == 0) {
            claimResultPreclaim.setClaimTitle(listDspFileDataMapping.getTitle());
        } else {
            claimResultPreclaim.setClaimTitle(listMatchDataDspDone.getMatchTitle());
        }


        WrkWorkTitle workTitlePriorOT = wrkWorkHandler.getWorkTitle();
        if (workTitlePriorOT != null) {
            if (StringUtils.isNotBlank(workTitlePriorOT.getTitle())) {
                claimResultPreclaim.setClaimTitle(workTitlePriorOT.getTitle());
            } else {
                if (StringUtils.isNotBlank(workTitlePriorOT.getTitleEn())) {
                    claimResultPreclaim.setClaimTitle(workTitlePriorOT.getTitleEn());
                }
            }
        }


        if (StringUtils.isBlank(claimResultPreclaim.getClaimTitle())) {
            claimResultPreclaim.setClaimTitle(claimResultPreclaim.getOriginalTitle());
        }


        //20210414 must SPEC上 TITLE是要出我們MUST DB的TITLE
        claimResultPreclaim.setOtherTitle(StringUtils.isBlank(listMatchDataDspDone.getMatchTitle()) ? listMatchDataDspDone.getTitle() : listMatchDataDspDone.getMatchTitle());
        claimResultPreclaim.setBussinessId(listMatchDataDspDone.getResourceId());
        Map<Long, ListDspFileBase> fileBaseIdAndNameMap = claimCcidHandler.getFileBaseIdAndNameMap();
        BigDecimal claimLicensorPerf = wrkWorkHandler.getPerfIpShare() == null ? BigDecimal.ZERO : wrkWorkHandler.getPerfIpShare();
        BigDecimal claimLicensorMech = BigDecimal.ZERO;
        claimResultPreclaim.setPerformanceShare(claimLicensorPerf);
        claimResultPreclaim.setMechanicalShare(claimLicensorMech);

        claimResultPreclaim.setPubName(wrkWorkHandler.getPubName());
        claimResultPreclaim.setPubId(wrkWorkHandler.getPubId());

        // 如果 claimLicensorPerf >0 或者是 claimLicensorMech >0 表示的是Y 可分配， 如果2个都是0表示的是N   如果其中任意一个存在>100的 表示的是O
        if (claimLicensorPerf.compareTo(BigDecimal.ZERO) == 0 && claimLicensorMech.compareTo(BigDecimal.ZERO) == 0) {
            claimResultPreclaim.setClaimFlag("N");
        } else {
            claimResultPreclaim.setClaimFlag("T");
        }

        if (claimLicensorPerf.compareTo(new BigDecimal("100")) > 0 || claimLicensorMech.compareTo(new BigDecimal("100")) > 0) {
            claimResultPreclaim.setClaimFlag("O");
        }


        List<String> filterLabel = claimCcidHandler.getFilterLabel();


        //检查是否包含过滤关键词，如果符合，标记为过滤数据
        if (filterLabel != null && filterLabel.size() > 0) {
            if (StringUtils.isNotBlank(listDspFileDataMapping.getLabel())) {
                for (String label : filterLabel) {
                    //忽略大小写
                    if (StringUtils.contains(listDspFileDataMapping.getLabel().toLowerCase(), label)) {
                        claimResultPreclaim.setClaimFlag("F");
                    }
                }
            }

        }


        //没有title数据过滤掉
        if (StringUtils.isBlank(claimResultPreclaim.getClaimTitle())) {
            claimResultPreclaim.setClaimFlag("FT");
        }

        //pre claim compcustomid有值，不输出报告
        if (StringUtils.isNotBlank(listDspFileDataMapping.getCompCustomId())) {
            claimResultPreclaim.setClaimFlag("FC");
        }


        claimResultPreclaim.setDataUniqueKey(listMatchDataDspDone.getDataUniqueKey());
        ListDspFileBase listDspFileBase = fileBaseIdAndNameMap.get(listDspFileBaseId);
        Long claimMinimaInfoId = listDspFileBase.getClaimMinimaInfoId();
//        ClaimMinimaInfo claimMinimaInfo = claimCcidHandler.getMinimaInfoIdAndNameMap().get(claimMinimaInfoId);
        claimCcidHandler.getClaimMinimaInfoList().stream().forEach(claimMinimaInfo -> {
            if (claimMinimaInfo.getId().equals(claimMinimaInfoId)) {
                claimResultPreclaim.setProductName(claimMinimaInfo.getProductFullName());
            }
        });

        //2021 03 05 ,根据CCID v14 YouTube guidelines for the Subscription services v1.2文档说明改成从作品中获取
        WrkWork wrkWrok = wrkWorkHandler.getWrkWrok();
        if (wrkWrok != null) {
            claimResultPreclaim.setClaimWorkId(wrkWrok.getWorkId());
            claimResultPreclaim.setClaimWorkSoc(wrkWrok.getWorkSocietyCode() + "");
            claimResultPreclaim.setClaimWorkUniqueKey(wrkWrok.getWorkUniqueKey());
            claimResultPreclaim.setIswc(wrkWrok.getISWC());
        } else {
            claimResultPreclaim.setClaimWorkId(listMatchDataDspDone.getMatchWorkId());
            claimResultPreclaim.setClaimWorkSoc(listMatchDataDspDone.getMatchWorkSocietyCode() + "");
            claimResultPreclaim.setClaimWorkUniqueKey(listMatchDataDspDone.getWorkUniqueKey());
            // work等于null 也需要入checked表
            ccidChecked(null,claimCcidHandler.getClaimHeaderId(),listMatchDataDspDone);
        }

        claimResultPreclaim.setOriginalReleaseId(listMatchDataDspDone.getReleaseId());
        claimResultPreclaim.setOriginalClickNumber(listMatchDataDspDone.getClickNumber());
        claimResultPreclaim.setOriginalQuantity(listMatchDataDspDone.getQuantile());

        // ter怎么获取 默认写死tw
        String isrcs = listMatchDataDspDone.getIsrc();
        if(StringUtils.isNotBlank(isrcs) && isrcs.indexOf(",") > -1){
            isrcs = isrcs.substring(0,isrcs.indexOf(","));
        }
        claimResultPreclaim.setOriginalIsrc(isrcs);
        claimResultPreclaim.setTerritory("TW");


        claimResultPreclaim.setWriter(builderWriters(wrkWorkHandler.getWorkIpShareList()));

        String pubId = wrkWorkHandler.getPubId();
        String pubName = wrkWorkHandler.getPubName();
        if (StringUtils.isBlank(pubId)) {
            pubName = "MUSTPR";
            pubId = "161";
        }
        claimResultPreclaim.setPubName(pubName);
        claimResultPreclaim.setPubId(pubId);

        String salesTransactionId = listMatchDataDspDone.getSalesTransactionId() ;
        if(StringUtils.isBlank(salesTransactionId)){
            String extJson = listMatchDataDspDone.getExtJson();
            if (StringUtils.isNotBlank(extJson)) {
                JSONObject jsonObject = JSONObject.fromObject(extJson);
                if (null != jsonObject && jsonObject.containsKey("sales_transaction_id")) {
                    salesTransactionId = jsonObject.getString("sales_transaction_id");
                }
            }
        }
        claimResultPreclaim.setSalesTransactionId(salesTransactionId);
        claimResultPreclaim.setSalesTransactionId(listMatchDataDspDone.getSalesTransactionId());
        if (claimResultPreclaim.getMechanicalShare().compareTo(BigDecimal.ZERO) == 1 && "161".equals(claimResultPreclaim.getPubId())) {
            XxlJobLogger.log("002,  {} --- fileMappingId: {}, mechanicalShare:{}, pubId:{}, performanceShare:{}", Thread.currentThread().getName(),
                    claimResultPreclaim.getFileMappingId(), claimResultPreclaim.getMechanicalShare(), claimResultPreclaim.getPubId(), claimResultPreclaim.getPerformanceShare());
        }

        return claimResultPreclaim;
    }


    public String builderWriters(List<WrkWorkIpShare> ipShareList) {
        Set<String> hashSet = new HashSet<>();

        if (ipShareList == null) {
            return "";
        }
        ipShareList.forEach(item -> {
            String chineseName = item.getChineseName();
            String name = item.getName();
            if (!StringUtils.equalsAnyIgnoreCase(item.getWorkIpRole(), "C", "CA", "A")) {
                return;
            }
            if (StringUtils.isBlank(chineseName)) {
                if (StringUtils.isNotBlank(name)) {
                    hashSet.add(name);
                }
            } else {
                hashSet.add(chineseName);
            }

        });


        ArrayList<String> strings = new ArrayList<>(hashSet);

        String result = hashSet.size() > 40 ? StringUtils.join(strings.subList(0, 50), "|") : StringUtils.join(strings, "|");
        if (result.length() > 1000) {
            return result.substring(0, 1000);
        } else {
            return result;
        }

    }


}
