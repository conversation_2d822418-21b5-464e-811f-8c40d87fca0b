package tw.org.must.must.core.task.listFile;

import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.core.service.new_list.*;
import tw.org.must.must.model.new_list.List2BasicFileBase;
import tw.org.must.must.model.new_list.List2BasicFileCombinedMapping;
import tw.org.must.must.model.new_list.List2BasicFileCombinedSample;
import tw.org.must.must.model.new_list.List2BasicFileCombinedTotal;

import java.util.Date;
import java.util.List;

/**
 * P2排序分配任务
 */
@Component
@Service
@DisallowConcurrentExecution
public class List2CombinedSampleTask implements Job {

    @Autowired
    private List2BasicFileCombinedTotalService list2BasicFileCombinedTotalService;
    @Autowired
    private List2BasicFileBaseService list2BasicFileBaseService;
    @Autowired
    private List2BasicFileDataMappingService list2BasicFileDataMappingService;
    @Autowired
    private List2BasicFileCombinedSampleService list2BasicFileCombinedSampleService;

    @Autowired
    private List2BasicFileCombinedMappingService list2BasicFileCombinedMappingService;

    @Autowired
    private List2SampleRuleBaseRelationService list2SampleRuleBaseRelationService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        sampleDistribution();
    }

    public void sampleDistribution() {
        // 1. 获取待抽样排序的合并任务(状态=5)
        // 查询list2_basic_file_combined_total表中status=5的所有记录
        List<List2BasicFileCombinedTotal> list = list2BasicFileCombinedTotalService.getSampleDistributionList();
        // 检查是否有待处理的任务，如果没有则直接返回
        if (list == null || list.isEmpty()) {
            return;
        }
        // 格式化当前时间用于日志记录，便于追踪任务执行时间
        String formatDate = DateParse.format(new Date(), DateParse.patterDateFormat);
        // 记录P2抽样分配任务开始的日志信息
        XxlJobLogger.log("P2排序分配任务{} 开始！", formatDate);
        // 2. 遍历处理每个任务
        // 循环处理每个状态为5的合并任务
        for (List2BasicFileCombinedTotal list2BasicFileCombinedTotal : list) {
            try {
                // 3. 检查是否已用于抽样
                // 查询list2_sample_rule_base_relation表中source=2的base_id
                List<Long> sampledBaseIds = list2SampleRuleBaseRelationService.selectBaseIdsBySource(2);
                // 检查要排序的合并数据记录的id是否在查询到的base_id中
                if (sampledBaseIds != null && sampledBaseIds.contains(list2BasicFileCombinedTotal.getId())) {
                    // 如果在，返回错误提示并跳过该记录
                    String errorMsg = String.format("即将要排序的合并数据(ID: %d)已经用于抽样，暂不可排序，请删除相关抽样后重试",
                                                   list2BasicFileCombinedTotal.getId());
                    XxlJobLogger.log(errorMsg);
                    // 设置状态为失败并记录错误信息
                    list2BasicFileCombinedTotal.setStatus(8); // 抽样失败
                    list2BasicFileCombinedTotal.setRemark(errorMsg);
                    list2BasicFileCombinedTotal.init();
                    list2BasicFileCombinedTotalService.updateSelective(list2BasicFileCombinedTotal);
                    continue; // 跳过当前记录，处理下一个
                }

                // 4. 设置状态为排序计算中(6)
                // 将任务状态从5更新为6，标记任务正在处理
                list2BasicFileCombinedTotal.setStatus(6);
                // 调用init()方法更新amend_time等基础字段
                list2BasicFileCombinedTotal.init();
                // 将状态变更持久化到数据库，防止重复处理
                list2BasicFileCombinedTotalService.updateSelective(list2BasicFileCombinedTotal);
                // 5. 执行排序计算核心逻辑
                // 获取该合并任务的抽样配置，包含抽样行数(sampleLine)等关键参数
                // 从list2_basic_file_combined_sample表中获取抽样配置信息
                List2BasicFileCombinedSample list2BasicFileCombinedSample = list2BasicFileCombinedSampleService.getByBaseId(list2BasicFileCombinedTotal.getId());
                // TODO 回填base, mapping，用于后续的比对，分配计算（数据量大会比较慢，后续迁移至定时任务中）
                // 根据合并任务创建新的基础文件记录
                // 创建新的List2BasicFileBase记录，设置批次类型为"P2"(新一般分配)，用于后续分配计算
                List2BasicFileBase base = list2BasicFileBaseService.generateBase(list2BasicFileCombinedTotal);
                // 从合并数据中按点击次数排序抽取 指定行数的样本
                // 根据sampleLine参数从list2_basic_file_combined_mapping中抽取数据到list2_basic_file_data_mapping
                int count = list2BasicFileDataMappingService.generateMapping(base.getId(), list2BasicFileCombinedTotal.getId(),
                        list2BasicFileCombinedSample.getSampleLine());
                // 将实际抽取的数据行数设置到基础文件记录中
                base.setNumberOfLines(Long.valueOf(count));

                // 获取合并数据列表，用于提取业务属性信息
                List<List2BasicFileCombinedMapping> list2BasicFileCombinedMappingList = list2BasicFileCombinedMappingService.
                        getByBaseId(list2BasicFileCombinedTotal.getId());
                // 检查合并数据是否存在
                if (!CollectionUtils.isEmpty(list2BasicFileCombinedMappingList)) {
                    // 取第一条合并数据记录作为属性来源(同一合并任务的数据属性通常一致)
                    List2BasicFileCombinedMapping list2BasicFileCombinedMapping = list2BasicFileCombinedMappingList.get(0);
                    // 从合并数据继承上传类型(FW/MS/CJ/PG等音乐作品类型)
                    base.setUploadType(list2BasicFileCombinedMapping.getUploadType());
                    // 从合并数据继承分类代码，用于分配计算的分类标识
                    base.setCategoryCode(list2BasicFileCombinedMapping.getCategoryCode());
                    // 从合并数据继承资金池权利，用于版权分配的权利类型
                    base.setPoolRight(list2BasicFileCombinedMapping.getPoolRight());
                    // 从合并数据继承资金池代码，用于资金分配的池子标识
                    base.setPoolCode(list2BasicFileCombinedMapping.getPoolCode());
                }
                // 更新基础文件记录的amend_time等字段
                base.init();
                // 将完整的基础文件信息持久化到数据库
                list2BasicFileBaseService.updateSelective(base);
                // 将生成的基础文件ID关联到合并任务记录中
                list2BasicFileCombinedTotal.setBaseId(base.getId());
                // 6. 成功后设置状态为抽样完成(7)
                // 将任务状态更新为7(抽样完成)，标记任务成功完成
                list2BasicFileCombinedTotal.setStatus(7);
                // 更新amend_time等基础字段
                list2BasicFileCombinedTotal.init();

                // 将最终状态和baseId持久化到数据库
                list2BasicFileCombinedTotalService.updateSelective(list2BasicFileCombinedTotal);
            } catch (Exception e) {
                // 记录异常信息到XXL-JOB日志中
                XxlJobLogger.log(e);
                // 设置任务状态为8(抽样失败)
                list2BasicFileCombinedTotal.setStatus(8); // 抽样失败
                // 记录详细的错误信息到remark字段，限制长度为500字符防止数据库字段溢出
                list2BasicFileCombinedTotal.setRemark(e.getMessage() == null ? "空指針異常" : e.getMessage().length() > 500 ? e.getMessage().substring(0, 500) : e.getMessage());
                // 更新amend_time等基础字段
                list2BasicFileCombinedTotal.init();
                // 将失败状态和错误信息持久化到数据库
                list2BasicFileCombinedTotalService.updateSelective(list2BasicFileCombinedTotal);
            }
        }
        // 记录P2抽样分配任务结束的日志信息
        XxlJobLogger.log("P2排序分配任务{} 结束！", formatDate);
    }

    public void sampleDistribution(Long id) {
        List2BasicFileCombinedTotal list2BasicFileCombinedTotal = list2BasicFileCombinedTotalService.getById(id);
        if (list2BasicFileCombinedTotal == null) {
            return;
        }
        String formatDate = DateParse.format(new Date(), DateParse.patterDateFormat);
        try {
            // 检查是否已用于抽样
            // 查询list2_sample_rule_base_relation表中source=2的base_id
            List<Long> sampledBaseIds = list2SampleRuleBaseRelationService.selectBaseIdsBySource(2);
            // 检查要排序的合并数据记录的id是否在查询到的base_id中
            if (sampledBaseIds != null && sampledBaseIds.contains(list2BasicFileCombinedTotal.getId())) {
                // 如果在，返回错误提示
                String errorMsg = String.format("即将要排序的合并数据(ID: %d)已经用于抽样，暂不可排序，请删除相关抽样后重试",
                                               list2BasicFileCombinedTotal.getId());
                XxlJobLogger.log(errorMsg);
                // 设置状态为失败并记录错误信息
                list2BasicFileCombinedTotal.setStatus(8); // 抽样失败
                list2BasicFileCombinedTotal.setRemark(errorMsg);
                list2BasicFileCombinedTotal.init();
                list2BasicFileCombinedTotalService.updateSelective(list2BasicFileCombinedTotal);
                return; // 直接返回，不执行排序
            }

            list2BasicFileCombinedTotal.setStatus(6);
            list2BasicFileCombinedTotal.init();
            list2BasicFileCombinedTotalService.updateSelective(list2BasicFileCombinedTotal);

            List2BasicFileCombinedSample list2BasicFileCombinedSample = list2BasicFileCombinedSampleService.getByBaseId(list2BasicFileCombinedTotal.getId());
            // TODO 回填base, mapping，用于后续的比对，分配计算（数据量大会比较慢，后续迁移至定时任务中）
            List2BasicFileBase base = list2BasicFileBaseService.generateBase(list2BasicFileCombinedTotal);
            int count = list2BasicFileDataMappingService.generateMapping(base.getId(), list2BasicFileCombinedTotal.getId(), list2BasicFileCombinedSample.getSampleLine());
            base.setNumberOfLines(Long.valueOf(count));
            base.init();
            list2BasicFileBaseService.updateSelective(base);
            list2BasicFileCombinedTotal.setBaseId(base.getId());
            list2BasicFileCombinedTotal.setStatus(7);
            list2BasicFileCombinedTotal.init();
            list2BasicFileCombinedTotalService.updateSelective(list2BasicFileCombinedTotal);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            list2BasicFileCombinedTotal.setStatus(8); // 抽样失败
            list2BasicFileCombinedTotal.setRemark(e.getMessage() == null ? "空指針異常" : e.getMessage().length() > 500 ? e.getMessage().substring(0, 500) : e.getMessage());
            list2BasicFileCombinedTotal.init();
            list2BasicFileCombinedTotalService.updateSelective(list2BasicFileCombinedTotal);
        }
        //XxlJobLogger.log("P2抽样分配任务 id:{}, folder:{}, date:{} 结束！", id, list2BasicFileCombinedTotal.getFolder(), formatDate);
    }

}
