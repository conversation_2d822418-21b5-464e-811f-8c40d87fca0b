package tw.org.must.must.core.task.overseasdelete;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.session.RowBounds;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.core.service.listoverseas.ListOverseasFileWorkMappingService;
import tw.org.must.must.mapper.listoverseas.ListOverseasFileBaseMapper;
import tw.org.must.must.model.listoverseas.ListOverseasFileBase;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: hanDa
 * @Date: 2021/3/17 16:52
 * @Version:1.0
 * @Description:
 */
@Slf4j
@Component
@DisallowConcurrentExecution
public class OverseasDeleteTask implements Job {

    @Autowired
    private ListOverseasFileWorkMappingService listOverseasFileWorkMappingService;
    @Autowired
    private ListOverseasFileBaseMapper listOverseasFileBaseMapper;
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        delete();

    }

     public void delete() {
        log.info("=============O清单数据删除开始===============");
        Example example = new Example(ListOverseasFileBase.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("fileStatus",1);
        //每次删除两个文件的相关数据
        List<ListOverseasFileBase> listOverseasFileBaseList = listOverseasFileBaseMapper.selectByExampleAndRowBounds(example, new RowBounds(0,2));
        if (CollectionUtils.isEmpty(listOverseasFileBaseList)){
            return;
        }
        listOverseasFileWorkMappingService.deleteListOverseasFileBaseRelateData(listOverseasFileBaseList.stream().map(ListOverseasFileBase::getId).collect(Collectors.toList()));
        log.info("=============O清单数据删除结束===============");
    }
}