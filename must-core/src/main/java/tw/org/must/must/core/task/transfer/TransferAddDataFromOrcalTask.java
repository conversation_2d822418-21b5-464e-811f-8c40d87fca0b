package tw.org.must.must.core.task.transfer;

import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.models.auth.In;
import org.apache.commons.lang3.StringUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.enums.DBTypeEnum;
import tw.org.must.must.common.util.BeanCopyUtil;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.common.vcp.CommonUtil;
import tw.org.must.must.core.config.ProFileUtils;
import tw.org.must.must.core.datasource.DBContextHolder;
import tw.org.must.must.core.handle.redisService.RedisService;
import tw.org.must.must.core.service.agr.AgrWrkSipService;
import tw.org.must.must.core.service.es.ESService;
import tw.org.must.must.core.service.mbr.MbrIpNameMergeService;
import tw.org.must.must.core.service.orcal.*;
import tw.org.must.must.core.service.wrk.*;
import tw.org.must.must.model.agr.AgrWrkSip;
import tw.org.must.must.model.mbr.MbrIpNameMerge;
import tw.org.must.must.model.orcal.*;
import tw.org.must.must.model.wrk.*;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Configuration
@DisallowConcurrentExecution
public class TransferAddDataFromOrcalTask implements Job {

    private Logger logger = LoggerFactory.getLogger(TransferAddDataFromOrcalTask.class);

    @Autowired
    private MbrIpNameMergeService mbrIpNameMergeService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private WrkWorkService wrkWorkService;

    @Autowired
    private OrcalWrkWorkService orcalWrkWorkService;

    @Autowired
    private OrcalWrkWorkRightService orcalWrkWorkRightService;

    @Autowired
    private OrcalWrkArtistService orcalWrkArtistService;
    @Autowired
    private OrcalWrkArtistMergeService orcalWrkArtistMergeService;
    @Autowired
    private OrcalWrkIsrcService orcalWrkIsrcService;
    @Autowired
    private OrcalWrkTvSeriesService orcalWrkTvSeriesService;
    @Autowired
    private OrcalWrkWorkComponentService orcalWrkWorkComponentService;
    @Autowired
    private OrcalWrkWorkIpShareService orcalWrkWorkIpShareService;
    @Autowired
    private OrcalWrkWorkRemarkService orcalWrkWorkRemarkService;
    @Autowired
    private OrcalWrkWorkSourceService orcalWrkWorkSourceService;
    @Autowired
    private OrcalWrkWorkTitleService orcalWrkWorkTitleService;
    @Autowired
    private WrkIsrcService wrkIsrcService;
    @Autowired
    private WrkWorkComponentService wrkWorkComponentService;
    @Autowired
    private WrkWorkIpShareService wrkWorkIpShareService;
    @Autowired
    private OrcalAgrWrkSipService orcalAgrWrkSipService;
    @Autowired
    private AgrWrkSipService agrWrkSipService;

    @Autowired
    private WrkWorkOriginWriterService wrkWorkOriginWriterService;

    @Autowired
    private ESService eSService;
    @Autowired
    private OrcalWrkIswcService orcalWrkIswcService;

    @Value("${schedule.start}")
    private Boolean scheduleIsStart;
    
    private static final String TRANSFER_ADD_WRK_WORK_KEY = "lastWrkWorkAmendTime";

    private static final String TRANSFER_ADD_WRK_WORK_FLAG = "transferAddWrkWorkFlag";

    private static final String LAST_WORK_ORIGIN_AMEND_TIME = "lastWorkOriginAmendTime";

    private static final String LAST_WORK_ORIGIN_FLAG = "lastWorkOriginFlag";

    // TODO 增量数据同步(定时一小时一次)  3、wrkWork   1、member  2、Agr must环境部署的时候 需要放开下面的PostConstruct注释
    // FIXME 这里已经不需要此种方式进行定时器启动，统一集成job 20200713
    // 周一到周五执行
    public void transferAddWrkWork() {
        if (!StringUtils.equalsIgnoreCase(ProFileUtils.getActiveProfile(), "must")) {
            logger.info("当前环境profile = {}，不启动Orcal读取数据任务！", ProFileUtils.getActiveProfile());
            return;
        }

        String flag = redisService.getValue(TRANSFER_ADD_WRK_WORK_FLAG) ;
        if(flag.equals("0")){
            XxlJobLogger.log("有其他work同步任务正在执行，等其他任务执行完成再执行");
            return;
        }

        String lastWrkWorkAmendTime = redisService.getValue(TRANSFER_ADD_WRK_WORK_KEY);
        // 2、orcal获取wrkWork更新时间超过redis记录的时间  数据
        if (StringUtils.isBlank(lastWrkWorkAmendTime)) {
            lastWrkWorkAmendTime = "2019-12-01 00:00:00";
        }

        logger.info("transferAddWrkWork  NEW ---redis 记录的时间为：" + lastWrkWorkAmendTime + "；如果为空，默认从19年12月1号开始查询");

        redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"0");

        Date now = new Date();
        int num = 0,pageSize = Constants.BATCH_SIZE_10000;
        int threadSize = Constants.BATCH_SIZE_1000;
        try {
            while (true) {
                int start = num * pageSize;
                int end = (num + 1) * pageSize;
                DBContextHolder.set(DBTypeEnum.ORCAL);
                XxlJobLogger.log("第{}次查询",num + 1);
                List<OrcalWrkWork> selectByDateByRowNum = orcalWrkWorkService.selectByDateByRowNum(lastWrkWorkAmendTime, start, end);
                if (CollectionUtils.isEmpty(selectByDateByRowNum)) {
                    XxlJobLogger.log("orcal work查询无数据，break！");
                    redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"1");
                    break;
                }
                List<List<OrcalWrkWork>> splitList = Lists.partition(selectByDateByRowNum,threadSize);
                int threadNum = splitList.size();
                ExecutorService workExecutorService = Executors.newFixedThreadPool(threadNum);
                XxlJobLogger.log("数据量：" + selectByDateByRowNum.size() + "开启线程数：" + threadNum);
                CountDownLatch countDownLatch = new CountDownLatch(threadNum);

                for(List<OrcalWrkWork> list : splitList){
                    workExecutorService.execute(() -> {
                        try {

                            if (CollectionUtils.isEmpty(list)) {
                                countDownLatch.countDown();
                                return;
                            }
                            XxlJobLogger.log("线程启动，开始执行work数据同步，线程名：{}", Thread.currentThread().getName());
                            syncAddWorkDataNotLastest(list);
                            XxlJobLogger.log("线程执行work数据同步完毕，线程名：{}", Thread.currentThread().getName());
                        }catch (Exception e){
                            XxlJobLogger.log(e);
                        }finally {
                            countDownLatch.countDown();
                        }
                    });
                }

                XxlJobLogger.log("等待所有线程完毕...................：====");
                //等待线程全部
                countDownLatch.await();
                XxlJobLogger.log("所有线程已执行完毕" );
                num++;

                workExecutorService.shutdown();
            }
            String format = DateParse.format(now, DateParse.patterDateFormat);
            logger.info("transferAddWrkWork  最后更新时间为 {}", format);
            redisService.setValue(TRANSFER_ADD_WRK_WORK_KEY, format);
            redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"1");
        } catch (Exception e) {
            logger.error("msg:", e);
            redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"-1");
        } finally {
            DBContextHolder.clear();
        }
    }

    public void transferAddWrkWorkBySociety() {
        /*if (!StringUtils.equalsIgnoreCase(ProFileUtils.getActiveProfile(), "must")) {
            logger.info("当前环境profile = {}，不启动Orcal读取数据任务！", ProFileUtils.getActiveProfile());
            return;
        }*/

        String flag = redisService.getValue(TRANSFER_ADD_WRK_WORK_FLAG) ;
        if(flag.equals("0")){
            XxlJobLogger.log("有其他work同步任务正在执行，等其他任务执行完成再执行");
            return;
        }
        redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"0");

        int pageSize = Constants.BATCH_SIZE_10000;
        int threadSize = Constants.BATCH_SIZE_1000;
        List<Integer> societyList = Arrays.asList(26, 104, 119, 126,161, 265, 269);
        try {
            for(Integer society : societyList){
                int num = 0;
                String lastAmendTimeKey = TRANSFER_ADD_WRK_WORK_KEY + "_"+ society ;
                String lastWrkWorkAmendTime = redisService.getValue(lastAmendTimeKey);
                // 2、orcal获取wrkWork更新时间超过redis记录的时间  数据
                if (StringUtils.isBlank(lastWrkWorkAmendTime)) {
                    lastWrkWorkAmendTime = "2023-07-25 00:00:00";
                }

                Date updateTime = DateParse.parseDate(lastWrkWorkAmendTime,DateParse.patterDateFormat); //不能用当前时间
                logger.info("transferAddWrkWorkBySociety  NEW ---redis 记录的时间为：" + lastWrkWorkAmendTime + "；如果为空，默认从2023年7月25号开始查询");
                XxlJobLogger.log("transferAddWrkWorkBySociety  NEW ---redis 记录的时间为：" + lastWrkWorkAmendTime + "；如果为空，默认从2023年7月25号开始查询");
                while (true) {
                    int start = num * pageSize;
                    int end = (num + 1) * pageSize;
                    DBContextHolder.set(DBTypeEnum.ORCAL);
                    XxlJobLogger.log("第{}次查询,SOC：{}",num + 1,society);
                    List<OrcalWrkWork> selectByDateByRowNum = orcalWrkWorkService.selectByDateAndSocByRowNum(lastWrkWorkAmendTime,society, start, end);
                    if (CollectionUtils.isEmpty(selectByDateByRowNum)) {
                        XxlJobLogger.log("orcal work查询无数据，break！");
                        break;
                    }
                    updateTime = selectByDateByRowNum.get(selectByDateByRowNum.size() -1).getLAST_AMEND_DATE();
                    List<List<OrcalWrkWork>> splitList = Lists.partition(selectByDateByRowNum,threadSize);
                    int threadNum = splitList.size();
                    ExecutorService workExecutorService = Executors.newFixedThreadPool(threadNum);
                    XxlJobLogger.log("数据量：" + selectByDateByRowNum.size() + "开启线程数：" + threadNum);
                    CountDownLatch countDownLatch = new CountDownLatch(threadNum);

                    for(List<OrcalWrkWork> list : splitList){
                        workExecutorService.execute(() -> {
                            try {

                                if (CollectionUtils.isEmpty(list)) {
                                    countDownLatch.countDown();
                                    return;
                                }
                                XxlJobLogger.log("线程启动，开始执行work数据同步，线程名：{}", Thread.currentThread().getName());
                                syncAddWorkData(list);
                                XxlJobLogger.log("线程执行work数据同步完毕，线程名：{}", Thread.currentThread().getName());
                            }catch (Exception e){
                                XxlJobLogger.log(e);
                            }finally {
                                countDownLatch.countDown();
                            }
                        });
                    }

                    XxlJobLogger.log("等待所有线程完毕...................：====");
                    //等待线程全部
                    countDownLatch.await();
                    XxlJobLogger.log("所有线程已执行完毕" );
                    num++;

                    workExecutorService.shutdown();
                }
                String format = DateParse.format(updateTime, DateParse.patterDateFormat);
                logger.info("transferAddWrkWork  最后更新时间为 {}", format);
                redisService.setValue(lastAmendTimeKey, format);
            }
            redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"1");
        } catch (Exception e) {
            logger.error("msg:", e);
            redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"-1");
        } finally {
            DBContextHolder.clear();
        }
    }

    public void transferThreeDayWrkWork() {
        /*if (!StringUtils.equalsIgnoreCase(ProFileUtils.getActiveProfile(), "must")) {
            logger.info("当前环境profile = {}，不启动Orcal读取数据任务！", ProFileUtils.getActiveProfile());
            return;
        }*/

        String flag = redisService.getValue(TRANSFER_ADD_WRK_WORK_FLAG) ;
        if(flag.equals("0")){
            XxlJobLogger.log("有其他work同步任务正在执行，等其他任务执行完成再执行");
            return;
        }

        redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"0");

        int pageSize = Constants.BATCH_SIZE_10000;
        int threadSize = Constants.BATCH_SIZE_1000;
        try {
            int num = 0;

            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.DAY_OF_MONTH,-3);
            String lastWrkWorkAmendTime = DateParse.format(calendar.getTime(), DateParse.patternDate);
            logger.info("transferThreeDayWrkWork时间为：" + lastWrkWorkAmendTime + "；如果为空，默认从2023年7月25号开始查询");
            XxlJobLogger.log("transferThreeDayWrkWork时间为：" + lastWrkWorkAmendTime + "；如果为空，默认从2023年7月25号开始查询");
            while (true) {
                int start = num * pageSize;
                int end = (num + 1) * pageSize;
                DBContextHolder.set(DBTypeEnum.ORCAL);
                XxlJobLogger.log("第{}次查询",num + 1);
                List<OrcalWrkWork> selectByDateByRowNum = orcalWrkWorkService.selectByDateByRowNum(lastWrkWorkAmendTime, start, end);
                if (CollectionUtils.isEmpty(selectByDateByRowNum)) {
                    redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"1");
                    XxlJobLogger.log("orcal work查询无数据，break！");
                    break;
                }
                List<List<OrcalWrkWork>> splitList = Lists.partition(selectByDateByRowNum,threadSize);
                int threadNum = splitList.size();
                ExecutorService workExecutorService = Executors.newFixedThreadPool(threadNum);
                XxlJobLogger.log("数据量：" + selectByDateByRowNum.size() + "开启线程数：" + threadNum);
                CountDownLatch countDownLatch = new CountDownLatch(threadNum);

                for(List<OrcalWrkWork> list : splitList){
                    workExecutorService.execute(() -> {
                        try {

                            if (CollectionUtils.isEmpty(list)) {
                                countDownLatch.countDown();
                                return;
                            }
                            XxlJobLogger.log("线程启动，开始执行work数据同步，线程名：{}", Thread.currentThread().getName());
                            syncAddWorkDataNotLastest(list);
//                            syncAddWorkData(list);
                            XxlJobLogger.log("线程执行work数据同步完毕，线程名：{}", Thread.currentThread().getName());
                        }catch (Exception e){
                            XxlJobLogger.log(e);
                        }finally {
                            countDownLatch.countDown();
                        }
                    });
                }

                XxlJobLogger.log("等待所有线程完毕...................：====");
                //等待线程全部
                countDownLatch.await();
                XxlJobLogger.log("所有线程已执行完毕" );
                num++;

                workExecutorService.shutdown();
            }
            String format = DateParse.format(now, DateParse.patterDateFormat);
            logger.info("transferAddWrkWork  最后更新时间为 {}", format);
            redisService.setValue(TRANSFER_ADD_WRK_WORK_KEY, format);
            redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"1");
        } catch (Exception e) {
            logger.error("msg:", e);
            redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"-1");
        } finally {
            DBContextHolder.clear();
        }
    }

    /**
     * 每周执行，查询DIVA本周有更新的数据，如果money的最后更新时间<=diva的最后更新时间，更新数据，否则跳过
     */
    public void transferAddWrkWorkDay(String param) {

        Date date = new Date() ;
        if(StringUtils.isNotEmpty(param)){
            try{
                date = DateParse.parseDate(param,DateParse.patternDate);
            }catch (Exception e){
                throw new RuntimeException("输入的日期格式不正确：" + param );
            }
        }

        String dateStr = DateParse.format(date,"yyyy-MM-dd") ;
        String startDate = dateStr + " 00:00:00" ;
        String endDate = dateStr + " 23:59:59";

        logger.info("当前日期：" + dateStr);

        try {
            Date now = new Date();
            int num = 0,pageSize = Constants.BATCH_SIZE_10000;
            int threadSize = Constants.BATCH_SIZE_1000;
            while (true) {
                int start = num * pageSize;
                int end = (num + 1) * pageSize;
                DBContextHolder.set(DBTypeEnum.ORCAL);
                XxlJobLogger.log("第{}次查询",num + 1);
                List<OrcalWrkWork> selectByDateByRowNum = orcalWrkWorkService.selectByDateByRowNumDay(startDate,endDate, start, end);
                if (CollectionUtils.isEmpty(selectByDateByRowNum)) {
                    redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"1");
                    XxlJobLogger.log("orcal work查询无数据，break！");
                    break;
                }
                List<List<OrcalWrkWork>> splitList = Lists.partition(selectByDateByRowNum,threadSize);
                int threadNum = splitList.size();
                ExecutorService workExecutorService = Executors.newFixedThreadPool(threadNum);
                XxlJobLogger.log("数据量：" + selectByDateByRowNum.size() + "开启线程数：" + threadNum);
                CountDownLatch countDownLatch = new CountDownLatch(threadNum);

                for(List<OrcalWrkWork> list : splitList){
                    workExecutorService.execute(() -> {
                        try {

                            if (CollectionUtils.isEmpty(list)) {
                                countDownLatch.countDown();
                                return;
                            }
                            XxlJobLogger.log("线程启动，开始执行work数据同步，线程名：{}", Thread.currentThread().getName());
                            syncAddWorkDataNotLastest(list);
                            XxlJobLogger.log("线程执行work数据同步完毕，线程名：{}", Thread.currentThread().getName());
                        }catch (Exception e){
                            XxlJobLogger.log(e);
                        }finally {
                            countDownLatch.countDown();
                        }
                    });
                }


                XxlJobLogger.log("等待所有线程完毕...................：====");
                //等待线程全部
                countDownLatch.await();
                XxlJobLogger.log("所有线程已执行完毕" );
                num++;

                workExecutorService.shutdown();

            }
            String format = DateParse.format(now, DateParse.patterDateFormat);
            logger.info("transferAddWrkWork  最后更新时间为 {}", format);
        } catch (Exception e) {
            logger.error("msg:", e);
        } finally {
            DBContextHolder.clear();
        }
    }

    public void transferAddWrkWorkCustomDate(String param){
        String flag = redisService.getValue(TRANSFER_ADD_WRK_WORK_FLAG) ;
        if(flag.equals("0")){
            XxlJobLogger.log("有其他work同步任务正在执行，等其他任务执行完成再执行");
            return;
        }

        // 2、orcal获取wrkWork更新时间超过redis记录的时间  数据
        if (StringUtils.isBlank(param)) {
            throw new RuntimeException("未输入日期" );
        }

        String dateStr ;
        try{
            Date date = DateParse.parseDate(param,DateParse.patternDate);
            dateStr = DateParse.format(date,"yyyy-MM-dd") ;
            dateStr = dateStr + " 00:00:00" ;
        }catch (Exception e){
            throw new RuntimeException("输入的日期格式不正确：" + param );
        }

        logger.info("transferAddWrkWorkCustomDate,设定的时间为：" + param );

        redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"0");

        int num = 0,pageSize = Constants.BATCH_SIZE_10000;
        int threadSize = Constants.BATCH_SIZE_1000;
        try {
            while (true) {
                int start = num * pageSize;
                int end = (num + 1) * pageSize;
                DBContextHolder.set(DBTypeEnum.ORCAL);
                XxlJobLogger.log("第{}次查询",num + 1);
                List<OrcalWrkWork> selectByDateByRowNum = orcalWrkWorkService.selectByDateByRowNum(dateStr, start, end);
                if (CollectionUtils.isEmpty(selectByDateByRowNum)) {
                    XxlJobLogger.log("orcal work查询无数据，break！");
                    redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"0");
                    break;
                }
                List<List<OrcalWrkWork>> splitList = Lists.partition(selectByDateByRowNum,threadSize);
                int threadNum = splitList.size();
                ExecutorService workExecutorService = Executors.newFixedThreadPool(threadNum);
                XxlJobLogger.log("数据量：" + selectByDateByRowNum.size() + "开启线程数：" + threadNum);
                CountDownLatch countDownLatch = new CountDownLatch(threadNum);

                for(List<OrcalWrkWork> list : splitList){
                    workExecutorService.execute(() -> {
                        try {

                            if (CollectionUtils.isEmpty(list)) {
                                countDownLatch.countDown();
                                return;
                            }
                            XxlJobLogger.log("线程启动，开始执行work数据同步，线程名：{}", Thread.currentThread().getName());
                            syncAddWorkDataNotLastest(list);
                            XxlJobLogger.log("线程执行work数据同步完毕，线程名：{}", Thread.currentThread().getName());
                        }catch (Exception e){
                            XxlJobLogger.log(e);
                        }finally {
                            countDownLatch.countDown();
                        }
                    });
                }

                XxlJobLogger.log("等待所有线程完毕...................：====");
                //等待线程全部
                countDownLatch.await();
                XxlJobLogger.log("所有线程已执行完毕" );
                num++;

                workExecutorService.shutdown();
            }
            redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"1");
        } catch (Exception e) {
            logger.error("msg:", e);
            redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"-1");
        } finally {
            DBContextHolder.clear();
        }
    }

    public void syncWorkOriginWriter(){
        String lastWorkOriginAmendTime = redisService.getValue(LAST_WORK_ORIGIN_AMEND_TIME);

        if (StringUtils.isBlank(lastWorkOriginAmendTime)) {
            lastWorkOriginAmendTime = "2019-12-01 00:00:00";
        }

        logger.info("syncWorkOriginWriter  NEW ---redis 记录的时间为：" + lastWorkOriginAmendTime + "；如果为空，默认从19年12月1号开始查询");
        AtomicReference<Date> lastAmendDate = new AtomicReference<>(DateParse.parseDate(lastWorkOriginAmendTime, DateParse.patterDateFormat));
        redisService.setValue(LAST_WORK_ORIGIN_FLAG,"0");

        int num = 0;
        final int pageSize = Constants.BATCH_SIZE_10000;
        int threadSize = Constants.BATCH_SIZE_1000;
        Date start = lastAmendDate.get();
        Date now = new Date();
        try {
            while (true) {
                int offset = num * pageSize;
                logger.info("第{}次查询",num + 1);
                List<WrkWork> wrkWorks = wrkWorkService.getWrkWorkListGreaterAmendTime(start, pageSize, offset);
                if (CollectionUtils.isEmpty(wrkWorks)) {
                    logger.info("orcal work查询无数据，break！");
                    redisService.setValue(LAST_WORK_ORIGIN_FLAG,"0");
                    break;
                }

                List<List<WrkWork>> splitList = Lists.partition(wrkWorks,threadSize);
                int threadNum = splitList.size();
                ExecutorService workExecutorService = Executors.newFixedThreadPool(threadNum);
                XxlJobLogger.log("数据量：" + wrkWorks.size() + "开启线程数：" + threadNum);
                CountDownLatch countDownLatch = new CountDownLatch(threadNum);
                for(List<WrkWork> list : splitList){
                    workExecutorService.execute(() -> {
                        try {

                            if (CollectionUtils.isEmpty(list)) {
                                countDownLatch.countDown();
                                return;
                            }
                            XxlJobLogger.log("线程启动，开始执行WorkOriginWriter数据同步，线程名：{}", Thread.currentThread().getName());
                            for(WrkWork ww : list){
                                wrkWorkOriginWriterService.saveOrUpdateWrkWorkOriginWriter(ww);
                            }
                            XxlJobLogger.log("线程执行WorkOriginWriter数据同步完毕，线程名：{}", Thread.currentThread().getName());
                        }catch (Exception e){
                            XxlJobLogger.log(e);
                        }finally {
                            countDownLatch.countDown();
                        }
                    });
                }

                XxlJobLogger.log("等待所有线程完毕...................：====");
                //等待线程全部
                countDownLatch.await();
                XxlJobLogger.log("所有线程已执行完毕" );
                num++;

                workExecutorService.shutdown();
            }
            String format = DateParse.format(now, DateParse.patterDateFormat);
            logger.info("syncWorkOriginWriter  最后更新时间为 {}", format);
            redisService.setValue(LAST_WORK_ORIGIN_AMEND_TIME, format);
            redisService.setValue(LAST_WORK_ORIGIN_FLAG,"1");
        } catch (Exception e) {
            logger.error("msg:", e);
            redisService.setValue(LAST_WORK_ORIGIN_FLAG,"-1");
        }finally {
            DBContextHolder.clear();
        }

    }

    public void syncWorkOriginWriterAll(){

        int num = 0;
        final int pageSize = Constants.BATCH_SIZE_10000;
        int threadSize = Constants.BATCH_SIZE_1000;
        Long start = 8962931L;
        try {
            while (true) {
                logger.info("第{}次查询",num + 1);
                XxlJobLogger.log("start={}",start);
                List<WrkWork> wrkWorks = wrkWorkService.getWrkWorkListGreaterId(start, pageSize);
                if (CollectionUtils.isEmpty(wrkWorks)) {
                    logger.info("orcal work查询无数据，break！");
                    break;
                }

                start = wrkWorks.get(wrkWorks.size() -1).getId();
                List<List<WrkWork>> splitList = Lists.partition(wrkWorks,threadSize);
                int threadNum = splitList.size();
                ExecutorService workExecutorService = Executors.newFixedThreadPool(threadNum);
                XxlJobLogger.log("数据量：" + wrkWorks.size() + "开启线程数：" + threadNum);
                CountDownLatch countDownLatch = new CountDownLatch(threadNum);
                for(List<WrkWork> list : splitList){
                    workExecutorService.execute(() -> {
                        try {

                            if (CollectionUtils.isEmpty(list)) {
                                countDownLatch.countDown();
                                return;
                            }
                            XxlJobLogger.log("线程启动，开始执行WorkOriginWriter数据同步，线程名：{}", Thread.currentThread().getName());
                            for(WrkWork ww : list){
                                wrkWorkOriginWriterService.saveOrUpdateWrkWorkOriginWriter(ww);
                            }
                            XxlJobLogger.log("线程执行WorkOriginWriter数据同步完毕，线程名：{}", Thread.currentThread().getName());
                        }catch (Exception e){
                            XxlJobLogger.log(e);
                        }finally {
                            countDownLatch.countDown();
                        }
                    });
                }

                XxlJobLogger.log("等待所有线程完毕...................：====");
                //等待线程全部
                countDownLatch.await();
                XxlJobLogger.log("所有线程已执行完毕" );

                workExecutorService.shutdown();
            }
        } catch (Exception e) {
            logger.error("msg:", e);
        }finally {
            DBContextHolder.clear();
        }

    }

    public void syncWorkIpShare(){
        String lastWrkWorkAmendTime = "2000-12-01 00:00:00";

        logger.info("syncWorkIpShare  NEW ---redis 记录的时间为：" + lastWrkWorkAmendTime + "；如果为空，默认从19年12月1号开始查询");
        AtomicReference<Date> lastAmendDate = new AtomicReference<>(DateParse.parseDate(lastWrkWorkAmendTime, DateParse.patterDateFormat));

        try {
            int num = 0;
            // 创建线程池
            final int nThreads = 8;
            final Semaphore semaphore = new Semaphore(nThreads);
            while (true) {
                int start = num * 200;
                int end = (num + 1) * 200;
                DBContextHolder.set(DBTypeEnum.ORCAL);
                List<OrcalWrkWork> selectByDateByRowNum = orcalWrkWorkService.selectByDateByRowNum(lastWrkWorkAmendTime, start, end);
                if (CollectionUtils.isEmpty(selectByDateByRowNum)) {
                    logger.info("orcal work查询无数据，break！");
                    break;
                }
                int finalNum = num;
                CompletableFuture.supplyAsync(() -> {
                    Date date = null;
                    try {
                        semaphore.acquire();
                        logger.info("开始执行第{}组第{}个线程（{}个一组），线程名：{}", finalNum / nThreads + 1, finalNum % nThreads + 1, nThreads, Thread.currentThread().getName());
                        syncAddWorkIpShareData(selectByDateByRowNum);
                        semaphore.release();
                    } catch (InterruptedException e) {
                        logger.error("msg:", e);
                    }
                    return date;
                }).thenAccept(t -> {
                    if (null != t && lastAmendDate.get().before(t)) {
                        lastAmendDate.set(t);
                    }
                });
                num++;
            }
            while (semaphore.availablePermits() != nThreads) {
                logger.info("transferAddWrkWork同步任务还未执行完，剩余：{} 个线程，当前休眠2分钟！", semaphore.availablePermits());
                TimeUnit.MINUTES.sleep(2);
            }
        } catch (Exception e) {
            logger.error("msg:", e);
        } finally {
            DBContextHolder.clear();
        }
    }


    public void transferAddWrkWorkNew() {
       /* if (!StringUtils.equalsIgnoreCase(ProFileUtils.getActiveProfile(), "must")) {
            logger.info("当前环境profile = {}，不启动Orcal读取数据任务！", ProFileUtils.getActiveProfile());
            return;
        }*/

        String flag = redisService.getValue(TRANSFER_ADD_WRK_WORK_FLAG) ;
        if(flag.equals("0")){
            XxlJobLogger.log("有其他work同步任务正在执行，等其他任务执行完成再执行");
            return;
        }

        redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"0");
        int num = 0,size = Constants.BATCH_SIZE_10000;
        int threadSize = Constants.BATCH_SIZE_1000;
        try {

            while (true) {
                logger.info("transferSupplementWrkWork:第{}次查询",num);
                DBContextHolder.set(DBTypeEnum.MASTER);
                List<WrkWork> changeWrkWorks = wrkWorkService.selectFromWrkWorkOracle( size);
                if (CollectionUtils.isEmpty(changeWrkWorks)) {
                    redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"1");
                    logger.info("wrk_work_compare_result查询无数据，break！");
                    break;
                }

                List<List<WrkWork>> splitList = Lists.partition(changeWrkWorks,threadSize);
                int threadNum = splitList.size();
                ExecutorService workExecutorService = Executors.newFixedThreadPool(threadNum);
                XxlJobLogger.log("数据量：" + changeWrkWorks.size() + "开启线程数：" + threadNum);

                CountDownLatch countDownLatch = new CountDownLatch(threadNum);

                for(List<WrkWork> list : splitList){
                    workExecutorService.execute(() -> {
                        try {

                            if (CollectionUtils.isEmpty(list)) {
                                countDownLatch.countDown();
                                return;
                            }
                            XxlJobLogger.log("线程启动，开始执行work数据同步，线程名：{}", Thread.currentThread().getName());
                            syncSupplementWorkData(list);
                            XxlJobLogger.log("线程执行work数据同步完毕，线程名：{}", Thread.currentThread().getName());
                        }catch (Exception e){
                            XxlJobLogger.log(e);
                        }finally {
                            countDownLatch.countDown();
                        }
                    });
                }

                XxlJobLogger.log("等待所有线程完毕...................：====");
                //等待线程全部
                countDownLatch.await();
                XxlJobLogger.log("所有线程已执行完毕" );
                num++;

                workExecutorService.shutdown();
                redisService.setValue(TRANSFER_ADD_WRK_WORK_FLAG,"1");
            }
        } catch (Exception e) {
            logger.error("msg:", e);
        } finally {
            DBContextHolder.clear();
        }
    }

    public void transferSupplementWrkWork1() {
       /* if (!StringUtils.equalsIgnoreCase(ProFileUtils.getActiveProfile(), "must")) {
            logger.info("当前环境profile = {}，不启动Orcal读取数据任务！", ProFileUtils.getActiveProfile());
            return;
        }*/

        int num = 0,size = Constants.BATCH_SIZE_10000;
        int threadSize = Constants.BATCH_SIZE_1000;
        try {

            while (true) {
                int offset = num * size;
                logger.info("transferSupplementWrkWork:第{}次查询",num);
                DBContextHolder.set(DBTypeEnum.MASTER);
                List<WrkWork> missWrkWorks = wrkWorkService.selectForMissData( offset, size);
                if (CollectionUtils.isEmpty(missWrkWorks)) {
                    logger.info("wrk_work_compare_result查询无数据，break！");
                    break;
                }

                List<List<WrkWork>> splitList = Lists.partition(missWrkWorks,threadSize);
                int threadNum = splitList.size();
                ExecutorService workExecutorService = Executors.newFixedThreadPool(threadNum);
                XxlJobLogger.log("数据量：" + missWrkWorks.size() + "开启线程数：" + threadNum);

                CountDownLatch countDownLatch = new CountDownLatch(threadNum);

                for(List<WrkWork> list : splitList){
                    workExecutorService.execute(() -> {
                        try {

                            if (CollectionUtils.isEmpty(list)) {
                                countDownLatch.countDown();
                                return;
                            }
                            XxlJobLogger.log("线程启动，开始执行work数据同步，线程名：{}", Thread.currentThread().getName());
                            syncSupplementWorkData(list);
                            XxlJobLogger.log("线程执行work数据同步完毕，线程名：{}", Thread.currentThread().getName());
                        }catch (Exception e){
                            XxlJobLogger.log(e);
                        }finally {
                            countDownLatch.countDown();
                        }
                    });
                }

                XxlJobLogger.log("等待所有线程完毕...................：====");
                //等待线程全部
                countDownLatch.await();
                XxlJobLogger.log("所有线程已执行完毕" );
                num++;

                workExecutorService.shutdown();
            }
        } catch (Exception e) {
            logger.error("msg:", e);
        } finally {
            DBContextHolder.clear();
        }
    }


    // 定时补work缺失数据
    // 1.清空数据  delMIssData
    // 2.把oracle work全量数据写到表wrk_work_oracle，只写主键。transferAllWrkWorkPrimarykey
    // 3.缺失数据写入到表wrk_work_compare_result status = 0缺失，status=1 oracle中没有的数据  wrkWorkService.insertForMissData()
    // 4.同步数据。查询wrk_work_compare_result status = 0的数据  transferSupplementWrkWork

    public void transferSupplementWrkWorkAll(){

        // step 1
        logger.info("step 1 start : delMIssData");
        delMIssData();
        logger.info("step 1 end : delMIssData");

        // step 2
        logger.info("step 2 start : transferAllWrkWorkPrimarykey");
        transferAllWrkWorkPrimarykey();
        logger.info("step 2 end : transferAllWrkWorkPrimarykey");

        // step 3
        logger.info("step 3 start : insertForMissData");
        wrkWorkService.insertForMissData();
        logger.info("step 3 end : insertForMissData");

        // step 4
        logger.info("step 4 start : transferSupplementWrkWork");
        transferSupplementWrkWork1();
        logger.info("step 4 end : transferSupplementWrkWork");

    }

    public void delMIssData(){
        wrkWorkService.truncateMissData(); // money缺少的数据
        wrkWorkService.truncateAllOraclePrimaryData(); // oracle全量数据，只存了主键
    }


    // 全量同步oracle work主键
    public void transferAllWrkWorkPrimarykey() {
        /*if (!StringUtils.equalsIgnoreCase(ProFileUtils.getActiveProfile(), "must")) {
            logger.info("当前环境profile = {}，不启动Orcal读取数据任务！", ProFileUtils.getActiveProfile());
            return;
        }*/

        try {
            int num = 0,size = Constants.BATCH_SIZE_10000 * 10;
            // 创建线程池
            while (true) {
                int start = num * size;
                int end = (num + 1) * size;
                logger.info("第{}次查询",num + 1);
                DBContextHolder.set(DBTypeEnum.ORCAL);
                List<OrcalWrkWork> selectByDateByRowNum = orcalWrkWorkService.selectWorknumAndSociety( start, end);
                if (CollectionUtils.isEmpty(selectByDateByRowNum)) {
                    XxlJobLogger.log("orcal work查询无数据，break！");
                    break;
                }

                synyWorkDataPrimarykey(selectByDateByRowNum);
                num++;

            }
        } catch (Exception e) {
            XxlJobLogger.log("msg:", e);
        } finally {
            DBContextHolder.clear();
        }
    }


    public void transferAddWrkWorkByWorkNums(Long workNum, Integer work_source_no_society) {
        if(workNum == null || work_source_no_society == null){
            return;
        }
        try{
            DBContextHolder.set(DBTypeEnum.ORCAL);
            OrcalWrkWork orcalWrkWork = orcalWrkWorkService.getOrcalWrkWorkByWorkNumAndSoc(workNum,work_source_no_society);
            List<OrcalWrkWork> list = new ArrayList<>() ;
            list.add(orcalWrkWork);
            syncAddWorkData(list);
            logger.info("transferAddWrkWork成功，WORKNUM :{},WORKNUM_SOCIETY:{}",workNum,work_source_no_society);
        }catch (Exception e){
            logger.error("msg:", e);
        }finally {
            DBContextHolder.clear();
        }

    }

    public void syncAddWorkData(List<OrcalWrkWork> selectByDateByRowNum) {
        long startTime = System.currentTimeMillis();
        if (!CollectionUtils.isEmpty(selectByDateByRowNum)) {
            for (OrcalWrkWork orcalWrkWork : selectByDateByRowNum) {
                logger.info("syncAddWorkData,workId:{},workSoc:{}",orcalWrkWork.getWORKNUM(),orcalWrkWork.getWORKNUM_SOCIETY());
                syncAddWorkData(orcalWrkWork);
//                result = orcalWrkWork.getLAST_AMEND_DATE();
            }
//            result = selectByDateByRowNum.get(selectByDateByRowNum.size() -1).getLAST_AMEND_DATE();
        }
        logger.info(Thread.currentThread().getName() + " 线程启动； syncAddWorkData()，所消耗的时间为 ：" + (System.currentTimeMillis() - startTime) + "毫秒");
    }

    public void syncAddWorkDataNotLastest(List<OrcalWrkWork> selectByDateByRowNum) {
        long startTime = System.currentTimeMillis();
        if (!CollectionUtils.isEmpty(selectByDateByRowNum)) {
            List<String> workUniqueKeys= selectByDateByRowNum.stream().map(w -> Constants.getWorkUniqueKey(w.getWORKNUM_SOCIETY(), Long.valueOf(w.getWORKNUM()))).collect(Collectors.toList());

            List<WrkWork> wrkWorks = wrkWorkService.getWrkWorkByWorkUniqueKeys(workUniqueKeys);
            DBContextHolder.set(DBTypeEnum.MASTER);
            Map<String,Date> wrkWorkAmendMap = wrkWorks.stream().collect(Collectors.toMap(WrkWork ::getWorkUniqueKey,WrkWork :: getOracleAmendTime,(a,b) -> a)) ;
            DBContextHolder.set(DBTypeEnum.ORCAL);
            for (OrcalWrkWork orcalWrkWork : selectByDateByRowNum) {
                String workUniqueKey = Constants.getWorkUniqueKey(orcalWrkWork.getWORKNUM_SOCIETY(), Long.valueOf(orcalWrkWork.getWORKNUM()));
                Date wrkWorkAmend = wrkWorkAmendMap.get(workUniqueKey) ;
                Date oracleAmendTime = orcalWrkWork.getLAST_AMEND_DATE();
                if(wrkWorkAmend == null || !oracleAmendTime.equals(wrkWorkAmend)){
                    logger.info("syncAddWorkData,workId:{},workSoc:{}",orcalWrkWork.getWORKNUM(),orcalWrkWork.getWORKNUM_SOCIETY());
                    syncAddWorkData(orcalWrkWork);
                }
            }
        }
        logger.info(Thread.currentThread().getName() + " 线程启动； syncAddWorkData()，所消耗的时间为 ：" + (System.currentTimeMillis() - startTime) + "毫秒");
    }

    public Date syncAddWorkIpShareData(List<OrcalWrkWork> selectByDateByRowNum) {
        long startTime = System.currentTimeMillis();
        Date result = null;
        if (!CollectionUtils.isEmpty(selectByDateByRowNum)) {
            for (OrcalWrkWork orcalWrkWork : selectByDateByRowNum) {
                Integer workId = orcalWrkWork.getWORKNUM(), workSocietyCode = orcalWrkWork.getWORKNUM_SOCIETY();
                List<OrcalWrkWorkIpShare> orcalWrkWorkIpShareList = orcalWrkWorkIpShareService.getWrkWorkIpShare(workId, workSocietyCode);
                if(!CollectionUtils.isEmpty(orcalWrkWorkIpShareList)){
                    List<String> orcalWrkWorkIpShareKeyList = orcalWrkWorkIpShareList.stream().map(it -> (it.getGROUP_INDICATOR() == null ? 0 : it.getGROUP_INDICATOR()) + it.getRIGHT_TYPE() + it.getIP_NAME_NO() + (it.getOIP_LINK_ID() == null ? 0 : it.getOIP_LINK_ID())).collect(Collectors.toList());
                    if(orcalWrkWorkIpShareKeyList.size() < orcalWrkWorkIpShareList.size()){
                        logger.info("同步数据，workId:{}，workSocietyCode:{}");
                        wrkWorkIpShareService.deleteByWorkId(Long.valueOf(workId), workSocietyCode);

                        List<WrkWorkIpShare> addWrkWorkIpShareList = new ArrayList<>();
                        for(OrcalWrkWorkIpShare orcalWrkWorkIpShare : orcalWrkWorkIpShareList ){
                            WrkWorkIpShare wrkWorkIpShare = new WrkWorkIpShare();
                            WrkWorkIpShare wwis = createOrUpdateWrkWorkIpShare(wrkWorkIpShare, orcalWrkWorkIpShare);
                            addWrkWorkIpShareList.add(wwis) ;
                        }

                        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(addWrkWorkIpShareList)){
                            wrkWorkIpShareService.addList(addWrkWorkIpShareList);
                        }
                    }
                }
            }
        }
        logger.info(Thread.currentThread().getName() + " 线程启动； syncAddWorkData()，所消耗的时间为 ：" + (System.currentTimeMillis() - startTime) + "毫秒");
        return result;
    }

    public Long syncSupplementWorkData(List<WrkWork> selectByDateByRowNum) {
        long startTime = System.currentTimeMillis();
        Long result = 0L;
        if (!CollectionUtils.isEmpty(selectByDateByRowNum)) {
            for (WrkWork wrkWork : selectByDateByRowNum) {

                logger.info("同步 work id {} ", wrkWork.getWorkId());
                DBContextHolder.set(DBTypeEnum.ORCAL);
                OrcalWrkWork orcalWrkWork = orcalWrkWorkService.getOrcalWrkWorkByWorkNumAndSoc(wrkWork.getWorkId(),wrkWork.getWorkSocietyCode()) ;
                if(orcalWrkWork != null){
                    syncAddWorkData(orcalWrkWork);
                }
//                wrkWorkService.deleteWrkWorkOracle(wrkWork.getId());
            }
        }
        logger.info(Thread.currentThread().getName() + " 线程启动； syncAddWorkData()，所消耗的时间为 ：" + (System.currentTimeMillis() - startTime) + "毫秒");
        return result;
    }


    public Long synyWorkDataPrimarykey(List<OrcalWrkWork> selectByDateByRowNum) {
        long startTime = System.currentTimeMillis();
        Long result = 0L;
        if (!CollectionUtils.isEmpty(selectByDateByRowNum)) {
            DBContextHolder.set(DBTypeEnum.MASTER);
            wrkWorkService.batchInsertWorkIdAndSoc(selectByDateByRowNum);
        }
        logger.info(Thread.currentThread().getName() + " 线程启动； syncAddWorkData()，所消耗的时间为 ：" + (System.currentTimeMillis() - startTime) + "毫秒");
        return result;
    }



    public void syncAddWorkData(OrcalWrkWork orcalWrkWork) {
        OrcalWrkWorkBulder orcalWrkWorkBulder = new OrcalWrkWorkBulder(orcalWrkWork).invoke();
        List<OrcalWrkArtist> orcalWrkArtistList = orcalWrkWorkBulder.getOrcalWrkArtistList();
        Integer workNum = orcalWrkWorkBulder.getWorkNum();
        Long workId = orcalWrkWorkBulder.getWorkId();
        Integer workNumSociety = orcalWrkWorkBulder.getWorkNumSociety();

        List<OrcalWrkWorkArtistMerge> orcalWrkArtistMergeList = orcalWrkWorkBulder.getOrcalWrkArtistMergeList();
        List<OrcalWrkIsrc> orcalWrkIsrcList = orcalWrkWorkBulder.getOrcalWrkIsrcList();
        List<OrcalWrkTvSeries> orcalWrkTvSeriesList = orcalWrkWorkBulder.getOrcalWrkTvSeriesList();
        List<OrcalWrkWorkComponent> orcalWrkWorkComponentList = orcalWrkWorkBulder.getOrcalWrkWorkComponentList();
        List<OrcalWrkWorkIpShare> orcalWrkWorkIpShareList = orcalWrkWorkBulder.getOrcalWrkWorkIpShareList();
        List<OrcalWrkWorkRemark> orcalWrkWorkRemarkList = orcalWrkWorkBulder.getOrcalWrkWorkRemarkList();
        List<OrcalWrkWorkSource> orcalWrkWorkSourceList = orcalWrkWorkBulder.getOrcalWrkWorkSourceList();
        List<OrcalWrkWorkTitle> orcalWrkWorkTitleList = orcalWrkWorkBulder.getOrcalWrkWorkTitleList();
        List<OrcalWrkWorkRight> orcalWrkWorkRightList = orcalWrkWorkBulder.getOrcalWrkWorkRightList();
        List<OrcalWrkIswc> orcalWrkIswcList = orcalWrkWorkBulder.getOrcalWrkIswcList();
        List<OrcalAgrWrkSip> oasList = orcalWrkWorkBulder.getOasList();
        List<Integer> sipLinkIdList = orcalWrkWorkBulder.getSipLinkIdList();

        // TODO: 2021-02-22 huyong: 切换数据源了, 起一个新方法，不同源不能使用 @Transtional注解
        // 切换数据源
        DBContextHolder.set(DBTypeEnum.MASTER);
        WrkWork existWrkWork = wrkWorkService.generateByOrcalWrkWork(orcalWrkWork, orcalWrkArtistList, workNum, workId, workNumSociety,
                orcalWrkArtistMergeList, orcalWrkIsrcList, orcalWrkTvSeriesList, orcalWrkWorkComponentList, orcalWrkWorkIpShareList,
                orcalWrkWorkRemarkList, orcalWrkWorkSourceList, orcalWrkWorkTitleList, orcalWrkWorkRightList, orcalWrkIswcList, oasList, sipLinkIdList);
        if (existWrkWork == null) {
            return;
        }
        // 同步至es
//        eSService.addNewWrkWorkToES(existWrkWork);
    }

    private void dealWrkIpShareAndAgrWrkSip(Long workId, Integer workNumSociety, List<OrcalWrkWorkIpShare> orcalWrkWorkIpShareList, List<OrcalAgrWrkSip> oasList, List<Integer> sipLinkIdList) {
        if (null != orcalWrkWorkIpShareList && orcalWrkWorkIpShareList.size() > 0) {

            List<WrkWorkIpShare> addWrkWorkIpShareList = new ArrayList<>();
            List<WrkWorkIpShare> uptWrkWorkIpShareList = new ArrayList<>();
            List<Long> wrkWorkIpShareIdsList = new ArrayList<>();

//            wrkWorkIpShareService.deleteByWorkId(workId, workNumSociety);

            List<WrkWorkIpShare> mustWrkWorkIpShareList = wrkWorkIpShareService.getWrkWorkIpShareByWrkId(workId, workNumSociety);

            if (null != mustWrkWorkIpShareList && mustWrkWorkIpShareList.size() > 0) {
                List<String> mustWrkWorkIpShareKeyList = mustWrkWorkIpShareList.stream().map(it -> it.getGroupIndicator() + it.getRightType() + it.getIpNameNo() + it.getOipLinkId()).collect(Collectors.toList());
                List<String> orcalWrkWorkIpShareKeyList = orcalWrkWorkIpShareList.stream().map(it -> it.getGROUP_INDICATOR() + it.getRIGHT_TYPE() + it.getIP_NAME_NO() + it.getOIP_LINK_ID()).collect(Collectors.toList());

                Map<String, List<WrkWorkIpShare>> mustWrkWorkIpShareKeyMap = mustWrkWorkIpShareList.stream().collect(Collectors.groupingBy(it -> it.getGroupIndicator() + it.getRightType() + it.getIpNameNo() + it.getOipLinkId()));

                List<String> copyOrcalWrkWorkIpShareKeyList = BeanCopyUtil.copyImplSerializable(orcalWrkWorkIpShareKeyList);
                copyOrcalWrkWorkIpShareKeyList.retainAll(mustWrkWorkIpShareKeyList);// 更新
                if (null != copyOrcalWrkWorkIpShareKeyList && copyOrcalWrkWorkIpShareKeyList.size() > 0) {
                    for (OrcalWrkWorkIpShare orcalWrkWorkIpShare : orcalWrkWorkIpShareList) {
                        String group_indicator = orcalWrkWorkIpShare.getGROUP_INDICATOR();
                        String right_type = orcalWrkWorkIpShare.getRIGHT_TYPE();
                        String ip_name_no = orcalWrkWorkIpShare.getIP_NAME_NO();
                        Integer oip_link_id = orcalWrkWorkIpShare.getOIP_LINK_ID();

                        String key = group_indicator + right_type + ip_name_no + oip_link_id;
                        if (copyOrcalWrkWorkIpShareKeyList.contains(key)) {
                            List<WrkWorkIpShare> workIpShareList = mustWrkWorkIpShareKeyMap.get(key);
                            WrkWorkIpShare wrkWorkIpShare = workIpShareList.get(0);
                            WrkWorkIpShare wwis = createOrUpdateWrkWorkIpShare(wrkWorkIpShare, orcalWrkWorkIpShare);
                            uptWrkWorkIpShareList.add(wwis);
                        }
                    }
                }

                List<String> copyAddOrcalWrkWorkIpShareKeyList = BeanCopyUtil.copyImplSerializable(orcalWrkWorkIpShareKeyList);
                copyAddOrcalWrkWorkIpShareKeyList.removeAll(mustWrkWorkIpShareKeyList);// 新增
                if (null != copyAddOrcalWrkWorkIpShareKeyList && copyAddOrcalWrkWorkIpShareKeyList.size() > 0) {
                    for (OrcalWrkWorkIpShare orcalWrkWorkIpShare : orcalWrkWorkIpShareList) {
                        String group_indicator = orcalWrkWorkIpShare.getGROUP_INDICATOR();
                        String right_type = orcalWrkWorkIpShare.getRIGHT_TYPE();
                        String ip_name_no = orcalWrkWorkIpShare.getIP_NAME_NO();
                        Integer oip_link_id = orcalWrkWorkIpShare.getOIP_LINK_ID();

                        String key = group_indicator + right_type + ip_name_no + oip_link_id;
                        if (copyAddOrcalWrkWorkIpShareKeyList.contains(key)) {
                            WrkWorkIpShare wrkWorkIpShare = new WrkWorkIpShare();
                            WrkWorkIpShare wwis = createOrUpdateWrkWorkIpShare(wrkWorkIpShare, orcalWrkWorkIpShare);
                            addWrkWorkIpShareList.add(wwis);
                        }
                    }
                }

                mustWrkWorkIpShareKeyList.removeAll(orcalWrkWorkIpShareKeyList); // 删除
                if (null != mustWrkWorkIpShareKeyList && mustWrkWorkIpShareKeyList.size() > 0) {
                    for (WrkWorkIpShare wrkWorkIpShare : mustWrkWorkIpShareList) {
                        String group_indicator = wrkWorkIpShare.getGroupIndicator();
                        String right_type = wrkWorkIpShare.getRightType();
                        String ip_name_no = wrkWorkIpShare.getIpNameNo();
                        Long oip_link_id = wrkWorkIpShare.getOipLinkId();

                        String key = group_indicator + right_type + ip_name_no + oip_link_id;
                        if (mustWrkWorkIpShareKeyList.contains(key)) {
                            wrkWorkIpShareIdsList.add(wrkWorkIpShare.getId());
                        }
                    }
                }


            } else {
                orcalWrkWorkIpShareList.forEach(orcalWrkWorkIpShare -> {
                    WrkWorkIpShare wrkWorkIpShare = new WrkWorkIpShare();
                    WrkWorkIpShare wwis = createOrUpdateWrkWorkIpShare(wrkWorkIpShare, orcalWrkWorkIpShare);
                    addWrkWorkIpShareList.add(wwis);
                });
            }

            if (addWrkWorkIpShareList.size() > 0)
                wrkWorkIpShareService.addList(addWrkWorkIpShareList);
            if (uptWrkWorkIpShareList.size() > 0)
                wrkWorkIpShareService.updateList(workId, workNumSociety, uptWrkWorkIpShareList);
            if (wrkWorkIpShareIdsList.size() > 0)
                wrkWorkIpShareService.delete(wrkWorkIpShareIdsList);

            // 根据sipLinkIdList去查询
            if (null != oasList && oasList.size() > 0) {
                List<AgrWrkSip> addAwsList = new ArrayList<>();
                List<AgrWrkSip> uptAwsList = new ArrayList<>();
                List<Long> agrWrkSipIdsList = new ArrayList<>();

                List<AgrWrkSip> mustAgrWrkSipList = agrWrkSipService.getAgrWrkSipByLinkIds(sipLinkIdList);

                // linkId唯一的！！！
                if (null != mustAgrWrkSipList && mustAgrWrkSipList.size() > 0) {
                    List<String> mustAgrWrkSipKeyList = mustAgrWrkSipList.stream().map(it -> it.getLinkId() + "").collect(Collectors.toList());
                    List<String> orcalAgrWrkSipKeyList = oasList.stream().map(it -> it.getLINK_ID() + "").collect(Collectors.toList());
                    Map<String, List<AgrWrkSip>> mustAgrWrkSipKeyMap = mustAgrWrkSipList.stream().collect(Collectors.groupingBy(it -> it.getLinkId() + ""));

                    List<String> copyUptOrcalAgrWrkSipKeyList = BeanCopyUtil.copyImplSerializable(orcalAgrWrkSipKeyList);
                    copyUptOrcalAgrWrkSipKeyList.retainAll(mustAgrWrkSipKeyList);// 更新
                    if (null != copyUptOrcalAgrWrkSipKeyList && copyUptOrcalAgrWrkSipKeyList.size() > 0) {
                        for (OrcalAgrWrkSip orcalAgrWrkSip : oasList) {
                            Integer link_id = orcalAgrWrkSip.getLINK_ID();
                            if (copyUptOrcalAgrWrkSipKeyList.contains(link_id + "")) {
                                List<AgrWrkSip> agrWrkSips = mustAgrWrkSipKeyMap.get(link_id + "");
                                AgrWrkSip agrWrkSip = agrWrkSips.get(0);
                                AgrWrkSip aws = createOrUpdateAgrWrkSip(agrWrkSip, orcalAgrWrkSip);
                                uptAwsList.add(aws);
                            }
                        }
                    }

                    List<String> copyAddOrcalAgrWrkSipKeyList = BeanCopyUtil.copyImplSerializable(orcalAgrWrkSipKeyList);
                    copyAddOrcalAgrWrkSipKeyList.removeAll(mustAgrWrkSipKeyList);
                    if (null != copyAddOrcalAgrWrkSipKeyList && copyAddOrcalAgrWrkSipKeyList.size() > 0) {
                        for (OrcalAgrWrkSip orcalAgrWrkSip : oasList) {
                            Integer link_id = orcalAgrWrkSip.getLINK_ID();
                            if (copyAddOrcalAgrWrkSipKeyList.contains(link_id + "")) {
                                AgrWrkSip agrWrkSip = new AgrWrkSip();
                                AgrWrkSip aws = createOrUpdateAgrWrkSip(agrWrkSip, orcalAgrWrkSip);
                                addAwsList.add(aws);
                            }
                        }
                    }

                    mustAgrWrkSipKeyList.removeAll(orcalAgrWrkSipKeyList);
                    if (null != mustAgrWrkSipKeyList && mustAgrWrkSipKeyList.size() > 0) {
                        for (AgrWrkSip agrWrkSip : mustAgrWrkSipList) {
                            Long linkId = agrWrkSip.getLinkId();
                            if (mustAgrWrkSipKeyList.contains(linkId + "")) {
                                agrWrkSipIdsList.add(agrWrkSip.getId());
                            }
                        }
                    }

                } else {
                    oasList.forEach(oas -> {
                        AgrWrkSip agrWrkSip = new AgrWrkSip();
                        AgrWrkSip aws = createOrUpdateAgrWrkSip(agrWrkSip, oas);
                        addAwsList.add(aws);
                    });
                }

//                agrWrkSipService.deleteBySipLinkIds(sipLinkIdList);
                if (addAwsList.size() > 0)
                    agrWrkSipService.addList(addAwsList);
                if (uptAwsList.size() > 0)
                    agrWrkSipService.updateBatchByPrimaryKeySelective(uptAwsList);
                if (agrWrkSipIdsList.size() > 0) {
                    agrWrkSipService.delete(agrWrkSipIdsList);
                }
            }
        }
    }

    private void dealOrcalWrkWorkComponent(Long workId, Integer workNumSociety, List<OrcalWrkWorkComponent> orcalWrkWorkComponentList) {
        if (orcalWrkWorkComponentList.size() > 0) {
            List<WrkWorkComponent> wwcAddList = new ArrayList<>();
            List<WrkWorkComponent> wwcUptList = new ArrayList<>();
            List<Long> wwcIdsList = new ArrayList<>();

//            wrkWorkComponentService.deleteByWorkId(workId, workNumSociety);
            List<WrkWorkComponent> mustWrkWorkComponentList = wrkWorkComponentService.getWrkWorkComponentByWrkId(workId, workNumSociety);

            if (null != mustWrkWorkComponentList && mustWrkWorkComponentList.size() > 0) {
                List<String> orcalWrkWorkComponentKeyList = orcalWrkWorkComponentList.stream().map(i -> i.getCOMPONENT_WORKNUM() + "_" + i.getCOMP_WORKNUM_SOCIETY()).collect(Collectors.toList());
                List<String> mustWrkWorkComponentKeyList = mustWrkWorkComponentList.stream().map(i -> i.getComponentWorkId() + "_" + i.getComWorkSociety()).collect(Collectors.toList());

                Map<String, List<WrkWorkComponent>> mustWrkWorkComponetMap = mustWrkWorkComponentList.stream().collect(Collectors.groupingBy(o -> o.getComponentWorkId() + "_" + o.getComWorkSociety()));

                List<String> copyOrcalWrkWorkComponentKeyList = BeanCopyUtil.copyImplSerializable(orcalWrkWorkComponentKeyList);
                copyOrcalWrkWorkComponentKeyList.retainAll(mustWrkWorkComponentKeyList); // 更新操作
                if (null != copyOrcalWrkWorkComponentKeyList && copyOrcalWrkWorkComponentKeyList.size() > 0) {
                    for (OrcalWrkWorkComponent orcalWrkWorkComponent : orcalWrkWorkComponentList) {
                        Integer component_worknum = orcalWrkWorkComponent.getCOMPONENT_WORKNUM();
                        Integer comp_worknum_society = orcalWrkWorkComponent.getCOMP_WORKNUM_SOCIETY();
                        String key = component_worknum + "_" + comp_worknum_society;
                        if (copyOrcalWrkWorkComponentKeyList.contains(key)) {
                            List<WrkWorkComponent> wrkWorkComponents = mustWrkWorkComponetMap.get(key);
                            WrkWorkComponent wrkWorkComponent = wrkWorkComponents.get(0);
                            WrkWorkComponent wwc = createOrUpdateWrkWorkComponent(wrkWorkComponent, orcalWrkWorkComponent);
                            wwcUptList.add(wwc);
                        }
                    }
                }

                List<String> copyAddOrcalWrkWorkComponentKeyList = BeanCopyUtil.copyImplSerializable(orcalWrkWorkComponentKeyList);
                copyAddOrcalWrkWorkComponentKeyList.removeAll(mustWrkWorkComponentKeyList); // 新增操作
                if (null != copyAddOrcalWrkWorkComponentKeyList && copyAddOrcalWrkWorkComponentKeyList.size() > 0) {
                    for (OrcalWrkWorkComponent orcalWrkWorkComponent : orcalWrkWorkComponentList) {
                        Integer component_worknum = orcalWrkWorkComponent.getCOMPONENT_WORKNUM();
                        Integer comp_worknum_society = orcalWrkWorkComponent.getCOMP_WORKNUM_SOCIETY();
                        String key = component_worknum + "_" + comp_worknum_society;
                        if (copyAddOrcalWrkWorkComponentKeyList.contains(key)) {
                            WrkWorkComponent wrkWorkComponent = new WrkWorkComponent();
                            WrkWorkComponent wwc = createOrUpdateWrkWorkComponent(wrkWorkComponent, orcalWrkWorkComponent);
                            wwcAddList.add(wwc);
                        }
                    }
                }

                mustWrkWorkComponentKeyList.removeAll(orcalWrkWorkComponentKeyList); // 删除操作
                if (null != mustWrkWorkComponentKeyList && mustWrkWorkComponentKeyList.size() > 0) {
                    for (WrkWorkComponent wrkWorkComponent : mustWrkWorkComponentList) {
                        Long componentWorkId = wrkWorkComponent.getComponentWorkId();
                        Integer comWorkSociety = wrkWorkComponent.getComWorkSociety();
                        String key = componentWorkId + "_" + comWorkSociety;
                        if (mustWrkWorkComponentKeyList.contains(key)) {
                            wwcIdsList.add(wrkWorkComponent.getId());
                        }

                    }
                }

            } else {
                orcalWrkWorkComponentList.forEach(orcalWkrWorkComponenet -> {
                    WrkWorkComponent wrkWorkComponent = new WrkWorkComponent();
                    WrkWorkComponent wwc = createOrUpdateWrkWorkComponent(wrkWorkComponent, orcalWkrWorkComponenet);
                    wwcAddList.add(wwc);
                });
            }

            if (wwcAddList.size() > 0)
                wrkWorkComponentService.addList(wwcAddList);
            if (wwcUptList.size() > 0)
                wrkWorkComponentService.updateList(workId, workNumSociety, wwcUptList);
            if (wwcIdsList.size() > 0)
                wrkWorkComponentService.delete(wwcIdsList);
        }
    }

    private void dealOrcalWrkIsrcList(Long workId, Integer workNumSociety, List<OrcalWrkIsrc> orcalWrkIsrcList) {
        if (orcalWrkIsrcList.size() > 0) {
            List<WrkIsrc> newWrkIsrc = new ArrayList<WrkIsrc>();
            List<WrkIsrc> updateWrkIsrc = new ArrayList<WrkIsrc>();
            List<Long> ids = new ArrayList<>();
            // 交集：retainAll 更新    差集：removeAll 以Orcal那边为主 那么应该以本地的差orcal的  这样就是删除的数据   用orcal的差本地的 那么就是新增的数据
            List<String> orcalIsrcList = orcalWrkIsrcList.stream().map(OrcalWrkIsrc::getISRC).collect(Collectors.toList());
//            wrkIsrcService.deleteByWorkId(workId, workNumSociety);
            List<WrkIsrc> mustIsrcList = wrkIsrcService.getWrkIsrcByWrkId(workId, workNumSociety);

            if (null != mustIsrcList && mustIsrcList.size() > 0) {
                List<String> copyOrcalIsrcList = BeanCopyUtil.copyImplSerializable(orcalIsrcList);
                List<String> isrcList = mustIsrcList.stream().map(WrkIsrc::getIsrc).collect(Collectors.toList());
                Map<String, List<WrkIsrc>> isrcMap = mustIsrcList.stream().collect(Collectors.groupingBy(WrkIsrc::getIsrc));
                // 更新操作
                copyOrcalIsrcList.retainAll(isrcList); // orcalIsrcList为 共有的isrc
                for (OrcalWrkIsrc orcalWrkIsrc : orcalWrkIsrcList) {
                    String isrc = orcalWrkIsrc.getISRC();
                    if (copyOrcalIsrcList.contains(isrc)) {
                        List<WrkIsrc> wrkIsrcList = isrcMap.get(isrc);
                        WrkIsrc wrkIsrc = wrkIsrcList.get(0);
                        WrkIsrc wi = createOrUpdateWrkIsrc(wrkIsrc, orcalWrkIsrc);
                        updateWrkIsrc.add(wi);
                    }
                }

                List<String> copyOrcalIsrcListForAdd = BeanCopyUtil.copyImplSerializable(orcalIsrcList);
                copyOrcalIsrcListForAdd.removeAll(isrcList); // 做新增操作   剩余的数据为新增数据  orcal有must无
                for (OrcalWrkIsrc orcalWrkIsrc : orcalWrkIsrcList) {
                    String isrc = orcalWrkIsrc.getISRC();
                    if (copyOrcalIsrcListForAdd.contains(isrc)) {
                        WrkIsrc wrkIsrc = new WrkIsrc();
                        WrkIsrc wi = createOrUpdateWrkIsrc(wrkIsrc, orcalWrkIsrc);
                        newWrkIsrc.add(wi);
                    }
                }

                isrcList.removeAll(orcalIsrcList);// 做删除同步   must存在但是orcal不存在 这部分数据不需要了
                for (WrkIsrc wrkIsrc : mustIsrcList) {
                    String isrc = wrkIsrc.getIsrc();
                    if (isrcList.contains(isrc)) {
                        ids.add(wrkIsrc.getId());
                    }
                }
            } else {
                // orcalIsrcList都是需要新增的
                for (OrcalWrkIsrc orcalWrkIsrc : orcalWrkIsrcList) {
                    WrkIsrc wrkIsrc = new WrkIsrc();
                    WrkIsrc wi = createOrUpdateWrkIsrc(wrkIsrc, orcalWrkIsrc);
                    newWrkIsrc.add(wi);
                }
            }

            if (newWrkIsrc.size() > 0)
                wrkIsrcService.addList(newWrkIsrc);
            if (updateWrkIsrc.size() > 0)
                wrkIsrcService.updateList(workId, workNumSociety, updateWrkIsrc);
            if (ids.size() > 0)
                wrkIsrcService.delete(ids);
        }
    }

    private WrkWorkRight createWrkWorkRight(WrkWorkRight wrkWorkRight, OrcalWrkWorkRight orcalWrkWorkRight) {
        if (null == wrkWorkRight) {
            wrkWorkRight = new WrkWorkRight();
            wrkWorkRight.init();
        }
        String distributable = orcalWrkWorkRight.getDISTRIBUTABLE();
        Integer dist = null;
        if (StringUtils.isNotBlank(distributable)) {
            if (distributable.equalsIgnoreCase("N")) {
                dist = 0;
            }
            if (distributable.equalsIgnoreCase("Y")) {
                dist = 1;
            }
        }
        wrkWorkRight.setDistributable(dist);
        wrkWorkRight.setRightType(orcalWrkWorkRight.getRIGHT_TYPE());
        wrkWorkRight.setShareType(orcalWrkWorkRight.getSHARE_TYPE());
        wrkWorkRight.setSynIndicator(orcalWrkWorkRight.getSYN_INDICATOR());
        wrkWorkRight.setWorkDp(orcalWrkWorkRight.getWORK_DP());
        wrkWorkRight.setWorkSd(orcalWrkWorkRight.getWORK_SD());
        wrkWorkRight.setWorkId(Long.valueOf(orcalWrkWorkRight.getWORKNUM()));
        wrkWorkRight.setWorkSociety(orcalWrkWorkRight.getWORKNUM_SOCIETY());
        wrkWorkRight.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkRight.getWORKNUM_SOCIETY(), Long.valueOf(orcalWrkWorkRight.getWORKNUM())));
        return wrkWorkRight;
    }

    private AgrWrkSip createOrUpdateAgrWrkSip(AgrWrkSip agrWrkSip, OrcalAgrWrkSip oas) {
        if (agrWrkSip.getId() != null) {
            agrWrkSip.setAmendTime(new Date());
        } else {
            agrWrkSip.init();
        }
        agrWrkSip.setIpNameNo(oas.getIP_NAME_NO());
        agrWrkSip.setCoverTisN(oas.getCOVER_TIS_N());
        agrWrkSip.setIpShare(oas.getIP_SHARE());
        agrWrkSip.setLinkId(Long.valueOf(oas.getLINK_ID()));
        agrWrkSip.setSipRole(oas.getSIP_ROLE());
        return agrWrkSip;
    }

    private WrkWorkIpShare createOrUpdateWrkWorkIpShare(WrkWorkIpShare wrkWorkIpShare, OrcalWrkWorkIpShare orcalWrkWorkIpShare) {
        if (wrkWorkIpShare.getId() == null) {
            wrkWorkIpShare.init();
        } else {
            wrkWorkIpShare.setAmendTime(new Date());
        }
        wrkWorkIpShare.setAgrNo(orcalWrkWorkIpShare.getAGR_NO());
        wrkWorkIpShare.setWorkId(orcalWrkWorkIpShare.getWORKNUM().longValue());
        wrkWorkIpShare.setWorkSocietyCode(orcalWrkWorkIpShare.getWORKNUM_SOCIETY());
        String ip_NAME_NO = orcalWrkWorkIpShare.getIP_NAME_NO();
        if (StringUtils.isNotBlank(ip_NAME_NO)) {
            wrkWorkIpShare.setIpNameNo(orcalWrkWorkIpShare.getIP_NAME_NO());
            // 根据ipNameNo去数据库中查询ipBaseNo
            MbrIpNameMerge ipNameMergeByNameNo = mbrIpNameMergeService.getIpNameMergeByNameNo(ip_NAME_NO);
            if (null != ipNameMergeByNameNo)
                wrkWorkIpShare.setIpBaseNo(ipNameMergeByNameNo.getIpBaseNo());
        }
        wrkWorkIpShare.setRightType(orcalWrkWorkIpShare.getRIGHT_TYPE());
        wrkWorkIpShare.setIpShare(orcalWrkWorkIpShare.getIP_SHARE());
        wrkWorkIpShare.setWorkIpRole(orcalWrkWorkIpShare.getWORK_IP_ROLE());
        wrkWorkIpShare.setSd(orcalWrkWorkIpShare.getSD());
        wrkWorkIpShare.setGroupIndicator(orcalWrkWorkIpShare.getGROUP_INDICATOR());
        wrkWorkIpShare.setRefIndicator(orcalWrkWorkIpShare.getREF_INDICATOR());
        Integer sipLinkId = orcalWrkWorkIpShare.getSIP_LINK_ID();
        if (null != sipLinkId)
            wrkWorkIpShare.setSipLinkId(orcalWrkWorkIpShare.getSIP_LINK_ID().longValue());
        else
            wrkWorkIpShare.setSipLinkId(0l);
        wrkWorkIpShare.setDummyNameRoman(orcalWrkWorkIpShare.getDUMMY_NAME_ROMAN());
        wrkWorkIpShare.setOipLinkId(orcalWrkWorkIpShare.getOIP_LINK_ID() == null ? 0l : orcalWrkWorkIpShare.getOIP_LINK_ID().longValue());
        wrkWorkIpShare.setIpType(orcalWrkWorkIpShare.getIP_TYPE());
        wrkWorkIpShare.setOrgWriterShare(orcalWrkWorkIpShare.getORG_WRITER_SHARE());
        wrkWorkIpShare.setCreateTime(new Date());
        wrkWorkIpShare.setAmendTime(new Date());
        wrkWorkIpShare.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkIpShare.getWORKNUM_SOCIETY(), orcalWrkWorkIpShare.getWORKNUM().longValue()));
        return wrkWorkIpShare;
    }

    private WrkWorkSource createNewWrkWorkSource(OrcalWrkWorkSource orcalWrkWorkSource) {
        WrkWorkSource wws = new WrkWorkSource();
        wws.setAmendTime(new Date());
        wws.setCreateTime(new Date());
        wws.setWorkId(orcalWrkWorkSource.getWORKNUM().longValue());
        wws.setWorkSocietyCode(orcalWrkWorkSource.getWORKNUM_SOCIETY());
        wws.setSourceType(orcalWrkWorkSource.getSOURCE_TYPE());
        wws.setNotifyDate(orcalWrkWorkSource.getNOTIFY_DATE());
        wws.setNotifySouceId(orcalWrkWorkSource.getNOTIFY_SOURCE());
        wws.setInputSoc(orcalWrkWorkSource.getINPUT_SOC());
        wws.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkSource.getWORKNUM_SOCIETY(), orcalWrkWorkSource.getWORKNUM().longValue()));
        return wws;
    }

    private WrkWorkRemark createNewWrkWorkRemark(OrcalWrkWorkRemark orcalWrkWorkRemark) {
        WrkWorkRemark wwr = new WrkWorkRemark();
        wwr.setAmendTime(new Date());
        wwr.setCreateTime(new Date());
        wwr.setInputSoc(orcalWrkWorkRemark.getINPUT_SOC());
        wwr.setRemark(orcalWrkWorkRemark.getREMARK());
        wwr.setWorkId(orcalWrkWorkRemark.getWORKNUM().longValue());
        wwr.setWorkSocietyCode(orcalWrkWorkRemark.getWORKNUM_SOCIETY());
        wwr.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkRemark.getWORKNUM_SOCIETY(), orcalWrkWorkRemark.getWORKNUM().longValue()));
        return wwr;
    }

    private WrkWorkComponent createOrUpdateWrkWorkComponent(WrkWorkComponent wrkWorkComponent, OrcalWrkWorkComponent orcalWkrWorkComponenet) {
        if (wrkWorkComponent.getId() != null) {
            wrkWorkComponent.setAmendTime(new Date());
        } else {
            wrkWorkComponent.init();
        }
        wrkWorkComponent.setWorkId(orcalWkrWorkComponenet.getWORKNUM().longValue());
        wrkWorkComponent.setWorkSocietyCode(orcalWkrWorkComponenet.getWORKNUM_SOCIETY());
        wrkWorkComponent.setSubTitleId(orcalWkrWorkComponenet.getSUB_TITLE_ID().longValue());
        wrkWorkComponent.setComponentWorkId(orcalWkrWorkComponenet.getCOMPONENT_WORKNUM().longValue());
        wrkWorkComponent.setComWorkSociety(orcalWkrWorkComponenet.getCOMP_WORKNUM_SOCIETY());
        wrkWorkComponent.setDurationM(orcalWkrWorkComponenet.getDURATION_MIN());
        wrkWorkComponent.setDurationS(orcalWkrWorkComponenet.getDURATION_SEC());
        wrkWorkComponent.setUsageType(orcalWkrWorkComponenet.getUSAGE());
        wrkWorkComponent.setAdjStatusFlag(orcalWkrWorkComponenet.getADJ_STATUS_FLAG());
        wrkWorkComponent.setCreateTime(new Date());
        wrkWorkComponent.setAmendTime(new Date());
        wrkWorkComponent.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWkrWorkComponenet.getWORKNUM_SOCIETY(), orcalWkrWorkComponenet.getWORKNUM().longValue()));
        return wrkWorkComponent;
    }

    private WrkIsrc createOrUpdateWrkIsrc(WrkIsrc wrkIsrc, OrcalWrkIsrc OrcalwrkIsrc) {
        if (wrkIsrc.getId() != null) {
            wrkIsrc.setAmendTime(new Date());
        } else {
            wrkIsrc.init();
        }
        wrkIsrc.setInputSoc(OrcalwrkIsrc.getINPUT_SOC());
        wrkIsrc.setIsrc(OrcalwrkIsrc.getISRC());
        wrkIsrc.setWorkId(OrcalwrkIsrc.getWORKNUM().longValue());
        wrkIsrc.setWorkSociety(OrcalwrkIsrc.getWORKNUM_SOCIETY());
        wrkIsrc.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(OrcalwrkIsrc.getWORKNUM_SOCIETY(), OrcalwrkIsrc.getWORKNUM().longValue()));
        return wrkIsrc;
    }

    // 5、将新增或者修改的数据 插入es  title 表数据插入es

    private WrkWorkTitle createOrUpdateWrkWorkTitle(WrkWorkTitle wwt, OrcalWrkWorkTitle orcalWrkWorkTitle) {
        if (null == wwt) {
            wwt = new WrkWorkTitle();
            wwt.init();
        }
        wwt.setSubTitleId(orcalWrkWorkTitle.getSUB_TITLE_ID().longValue());
        wwt.setTitleEn(orcalWrkWorkTitle.getE_TITLE());
        wwt.setTitle(orcalWrkWorkTitle.getC_TITLE());
        wwt.setTitleType(orcalWrkWorkTitle.getTITLE_CODE());
        wwt.setGenreCode(orcalWrkWorkTitle.getGENRE_DETAIL());
        wwt.setLanguageCode(orcalWrkWorkTitle.getLANGUAGE_CODE());
        wwt.setDurationM(orcalWrkWorkTitle.getDURATION_MIN());
        wwt.setDurationS(orcalWrkWorkTitle.getDURATION_SEC());
        wwt.setGenerDtlFlag(orcalWrkWorkTitle.getGENRE_DTL_FLAG());
        wwt.setTransfer(orcalWrkWorkTitle.getTRANSFER());
        wwt.setInputSoc(orcalWrkWorkTitle.getINPUT_SOC());
        wwt.setAmendTime(new Date());
        wwt.setWorkUniqueKey(CommonUtil.getWorkUniqueKeyByWrkSocAndNum(orcalWrkWorkTitle.getWORKNUM_SOCIETY(), orcalWrkWorkTitle.getWORKNUM().longValue()));
        return wwt;
    }

    // agr_agreement_extend 、 agr_agreement_remark 、 agr_agreement_source 、 agr_agreement_territory 、 agr_assignee 、agr_assignor 、 agr_op_chain 、 agr_op_link 、 agr_wrk_chain 、 agr_wrk_link 、 agr_wrk_sip

    //select * from wrk_work w where w.LAST_AMEND_DATE >= to_date('2019-12-03 00:00:00','YYYY-MM-DD HH24:MI:SS')

    /**
     * oracle同步增量数据
     * 0/30 * * * * ?
     *
     * @throws JobExecutionException
     */
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        this.transferAddWrkWork();
    }

    public boolean transferAddWrkWorkTask() {
        logger.info("transferAddWrkWork 进行下一次循环,当前redis中时间为："+redisService.getValue(TRANSFER_ADD_WRK_WORK_KEY)+";");
        this.transferAddWrkWork();
        String value = redisService.getValue(TRANSFER_ADD_WRK_WORK_FLAG);
        if(StringUtils.isNotBlank(value) && "0".equalsIgnoreCase(value)){
            return false;
        }
        return true;
    }

    private class OrcalWrkWorkBulder {
        private OrcalWrkWork orcalWrkWork;
        private List<OrcalWrkArtist> orcalWrkArtistList;
        private Integer workNum;
        private Long workId;
        private Integer workNumSociety;
        private List<OrcalWrkWorkArtistMerge> orcalWrkArtistMergeList;
        private List<OrcalWrkIsrc> orcalWrkIsrcList;
        private List<OrcalWrkTvSeries> orcalWrkTvSeriesList;
        private List<OrcalWrkWorkComponent> orcalWrkWorkComponentList;
        private List<OrcalWrkWorkIpShare> orcalWrkWorkIpShareList;
        private List<OrcalWrkWorkRemark> orcalWrkWorkRemarkList;
        private List<OrcalWrkWorkSource> orcalWrkWorkSourceList;
        private List<OrcalWrkWorkTitle> orcalWrkWorkTitleList;
        private List<OrcalWrkWorkRight> orcalWrkWorkRightList;
        private List<OrcalWrkIswc> orcalWrkIswcList;
        private List<OrcalAgrWrkSip> oasList;
        private List<Integer> sipLinkIdList;

        public OrcalWrkWorkBulder(OrcalWrkWork orcalWrkWork) {
            this.orcalWrkWork = orcalWrkWork;
        }

        public List<OrcalWrkArtist> getOrcalWrkArtistList() {
            return orcalWrkArtistList;
        }

        public Integer getWorkNum() {
            return workNum;
        }

        public Long getWorkId() {
            return workId;
        }

        public Integer getWorkNumSociety() {
            return workNumSociety;
        }

        public List<OrcalWrkWorkArtistMerge> getOrcalWrkArtistMergeList() {
            return orcalWrkArtistMergeList;
        }

        public List<OrcalWrkIsrc> getOrcalWrkIsrcList() {
            return orcalWrkIsrcList;
        }

        public List<OrcalWrkTvSeries> getOrcalWrkTvSeriesList() {
            return orcalWrkTvSeriesList;
        }

        public List<OrcalWrkWorkComponent> getOrcalWrkWorkComponentList() {
            return orcalWrkWorkComponentList;
        }

        public List<OrcalWrkWorkIpShare> getOrcalWrkWorkIpShareList() {
            return orcalWrkWorkIpShareList;
        }

        public List<OrcalWrkWorkRemark> getOrcalWrkWorkRemarkList() {
            return orcalWrkWorkRemarkList;
        }

        public List<OrcalWrkWorkSource> getOrcalWrkWorkSourceList() {
            return orcalWrkWorkSourceList;
        }

        public List<OrcalWrkWorkTitle> getOrcalWrkWorkTitleList() {
            return orcalWrkWorkTitleList;
        }

        public List<OrcalWrkWorkRight> getOrcalWrkWorkRightList() {
            return orcalWrkWorkRightList;
        }

        public List<OrcalWrkIswc> getOrcalWrkIswcList() {
            return orcalWrkIswcList;
        }

        public List<OrcalAgrWrkSip> getOasList() {
            return oasList;
        }

        public List<Integer> getSipLinkIdList() {
            return sipLinkIdList;
        }

        public OrcalWrkWorkBulder invoke() {
            orcalWrkArtistList = new ArrayList<OrcalWrkArtist>();
            DBContextHolder.set(DBTypeEnum.ORCAL);
            workNum = orcalWrkWork.getWORKNUM();
            workId = workNum.longValue();
            workNumSociety = orcalWrkWork.getWORKNUM_SOCIETY();
            // 先查询merge
            orcalWrkArtistMergeList = orcalWrkArtistMergeService.getWrkArtistByWorkNumAndSociety(workNum, workNumSociety);
            if (null != orcalWrkArtistMergeList && orcalWrkArtistMergeList.size() > 0) {
                orcalWrkArtistMergeList.forEach(owwam -> {
                    // 根据merge获取到对应的artistId
                    OrcalWrkArtist owa = orcalWrkArtistService.getWrkArtistByArtistID(owwam.getARTIST_ID());
                    orcalWrkArtistList.add(owa);
                });
            }

            // 根据 workNum和society 去查询wrk_isrc
            orcalWrkIsrcList = orcalWrkIsrcService.getWrkIsrc(workNum, workNumSociety);

            // 根据 workNum和society 去查询wrk_tv_series
            orcalWrkTvSeriesList = orcalWrkTvSeriesService.getWrkTvSeries(workNum, workNumSociety);

            // 根据 workNum和society 去查詢wrk_work_component
            orcalWrkWorkComponentList = orcalWrkWorkComponentService.getWrkWorkComponent(workNum, workNumSociety);

            // 根据 workNum和society 去查詢wrk_work_ip_share
            orcalWrkWorkIpShareList = orcalWrkWorkIpShareService.getWrkWorkIpShare(workNum, workNumSociety);

            // 根据 workNum和society 去查詢wrk_work_remark
            orcalWrkWorkRemarkList = orcalWrkWorkRemarkService.getWrkWorkRemark(workNum, workNumSociety);

            // 根据 workNum和society 去查詢wrk_work_source
            orcalWrkWorkSourceList = orcalWrkWorkSourceService.getWrkWorkSource(workNum, workNumSociety);

            // 根据 workNum和society 去查詢wrk_title
            orcalWrkWorkTitleList = orcalWrkWorkTitleService.getWrkWorkTitle(workNum, workNumSociety);

            // 根据 lastAmendTime 去查詢wrk_work_transfer  TODO 此处需要单独同步因为作品的变更对transfer没影响，wrkworktransfer应根据processDate和transferTime  迁移至 TransferAddDataFromOrcalTask
//        List<OrcalWrkWorkTransfer> orcalWrkWorkTransferList = orcalWrkWorkTransferService.getWrkWorkTransferBySourceNoAndSoc(workNum, workNumSociety);

            // FIXME 20200716 新增wrk_work_right
            orcalWrkWorkRightList = orcalWrkWorkRightService.getWrkWorkRightByWorkIdAndSoc(workNum, workNumSociety);

            // FIXME 20200728 新增 wrk_iswc 信息
            orcalWrkIswcList = orcalWrkIswcService.getWrkIswc(workNum, workNumSociety);

            // 根据workIpshare的sipLinkId 去agr_wrk_sip查询
            oasList = new ArrayList<OrcalAgrWrkSip>();
            sipLinkIdList = new ArrayList<Integer>();

            if (orcalWrkWorkIpShareList != null && orcalWrkWorkIpShareList.size() > 0) {
                sipLinkIdList = orcalWrkWorkIpShareList.stream().map(OrcalWrkWorkIpShare::getSIP_LINK_ID).filter(it -> it != null).collect(Collectors.toList());
                if (null != sipLinkIdList && sipLinkIdList.size() > 0)
                    oasList = orcalAgrWrkSipService.getAgrWrkSipBySipLinkId(sipLinkIdList);
            }

            DBContextHolder.clear();
            return this;
        }
    }
}
