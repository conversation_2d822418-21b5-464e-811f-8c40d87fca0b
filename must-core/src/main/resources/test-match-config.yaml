list:
  file:
    # Used in ListFileInfoPServiceImpl for uploading only; can be empty here
    path: !!null null
spring:
  datasource:
    master:
      url: **************************************************************************
      username: must
      password: firstbrave
    slave1:
      url: **************************************************************************
      username: must
      password: firstbrave
    slave2:
      url: **************************************************************************
      username: must
      password: firstbrave
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      filters: stat
      initial-size: 10
      min-idle: 20
      max-active: 500
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 'x'
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      pool-prepared-statements: false
      max-pool-prepared-statement-per-connection-size: 20
  cache:
    type: redis
  redis:
    host: ***********
    port: 6379
    password: firstbrave
    timeout: 10000
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
    database: 1
  mvc:
    favicon:
      enabled: false

logging:
  level:
    root: info
    tw.org.must.must.core.service.list: info
    tw.org.must.must.common.vcp.TitleUtils: info
    tw.org.must.must.core.MatchTesting: info
pagehelper:
  page-size-zero: true
  reasonable: true
swagger2:
  enable: true
elasticSearch:
  host: ***********
  port: 9200
  client:
    connectNum: 10
    connectPerRoute: 50
async-xml-parser:
  core-pool-size: 5
  max-pool-size: 10
queue:
  size:
    ddex:
      resource: 10000
      release: 10000
      release-transaction: 10000
  consume:
    core-pool-size: 10
    max-pool-size: 20
matching-list:
  api: http://***********:9998
