management:
  health:
    mail:
      enabled: false
  server:
    servlet:
      context-path: /actuator
mybatis:
  mapper-locations: classpath:/mybatis-mapper/*Mapper.xml
server:
  port: 8081
  servlet:
    context-path: /must-job-admin
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      auto-commit: true
      connection-test-query: SELECT 1
      connection-timeout: 10000
      idle-timeout: 30000
      max-lifetime: 900000
      maximum-pool-size: 30
      minimum-idle: 10
      pool-name: HikariCP
      validation-timeout: 1000
    password: Mdb1933
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:mysql://**************:3306/must?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&serverTimezone=Asia/Shanghai
    username: must
  freemarker:
    charset: UTF-8
    request-context-attribute: request
    settings:
      number_format: 0.##########
    suffix: .ftl
    templateLoaderPath: classpath:/templates/
  mail:
    from: <EMAIL>
    host: smtp.qq.com
    password: xxx
    port: 25
    properties:
      mail:
        smtp:
          auth: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
          starttls:
            enable: true
            required: true
    username: <EMAIL>
  mvc:
    servlet:
      load-on-startup: 0
    static-path-pattern: /static/**
  resources:
    static-locations: classpath:/static/
xxl:
  job:
    accessToken: pLKCDJwyk2XDljYT0zTDBY7X0ePWuDeA
    i18n: zh_CN
    logretentiondays: 30
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
