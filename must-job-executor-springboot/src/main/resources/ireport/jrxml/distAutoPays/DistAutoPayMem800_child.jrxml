<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports"
			  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
			  xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd"
			  name="730_sub" pageWidth="800" pageHeight="600" columnWidth="800" leftMargin="0" rightMargin="0"
			  topMargin="0" bottomMargin="0" uuid="6a5a2365-15da-4757-a88a-845c985b874a">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="distNo" class="java.lang.String"/>
	<field name="royalties" class="java.lang.String"/>
	<field name="payAbleAmount" class="java.lang.String"/>
	<field name="admissionRate" class="java.lang.String"/>
	<field name="admissionAmount" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="47" splitType="Stretch">
			<textField>
				<reportElement x="112" y="0" width="110" height="20" uuid="c9be0270-6d3d-41b5-b520-57cc69c8ef73"/>
				<textFieldExpression><![CDATA[$F{distNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="326" y="0" width="60" height="20" uuid="b35ce080-1999-496a-815d-ae286bc48dab"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{royalties}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="487" y="0" width="90" height="20" uuid="60450310-ddfa-4456-ba8d-204848f93cd0"/>
				<textFieldExpression><![CDATA[$F{payAbleAmount}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="112" y="20" width="128" height="20" uuid="6203620c-8306-4572-8f79-283c4a667c21"/>
				<textElement textAlignment="Left"/>
				<text><![CDATA[ADMINISTRATION FEE @]]></text>
			</staticText>
			<textField>
				<reportElement x="240" y="20" width="70" height="20" uuid="7d9406bc-fa68-4562-9348-cd7dcf9724c2"/>
				<textFieldExpression><![CDATA[$F{admissionRate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="326" y="20" width="57" height="20" uuid="34d64d91-54b6-42d5-8069-2a00fd076311"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$F{admissionAmount}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
