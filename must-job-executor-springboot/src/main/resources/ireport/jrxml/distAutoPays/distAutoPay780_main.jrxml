<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.5.1.final using JasperReports Library version 6.5.1  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd"
              name="780_main" pageWidth="1000" pageHeight="842" columnWidth="980" leftMargin="20" rightMargin="0"
              topMargin="0" bottomMargin="0" uuid="d57ad4fa-37e4-4a42-88fa-f33f7cab3946">
    <parameter name="date" class="java.lang.String"/>
    <parameter name="societyName" class="java.lang.String"/>
    <parameter name="societyCode" class="java.lang.String"/>
    <parameter name="SUBREPORT_DIR" class="java.lang.String">
        <defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
    </parameter>
    <parameter name="payCurrency" class="java.lang.String"/>
    <parameter name="feeInError" class="java.lang.String"/>
    <parameter name="adjTotalAmount" class="java.lang.String"/>
    <parameter name="adjSendAmount" class="java.lang.String"/>
	<parameter name="affiliated" class="java.lang.String"/>
	<parameter name="bankCharge" class="java.lang.String"/>
	<parameter name="netPayment" class="java.lang.String"/>
	<parameter name="exchangeRate" class="java.lang.String"/>
	<parameter name="totalPayment" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="distAutoPayReceipt" class="java.lang.Object"/>
	<field name="prsAffiliated" class="java.lang.Object"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<columnHeader>
		<band height="163" splitType="Stretch">
			<staticText>
				<reportElement x="280" y="30" width="380" height="20" uuid="cb080dfc-4074-43d7-b44e-d890790a6bd1"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[MUSIC COPYRIGHT SOCIETY OF CHINESE TAIPEI 社團法人中華音樂著作權協會]]></text>
			</staticText>
			<staticText>
				<reportElement x="280" y="70" width="390" height="20" uuid="********-0cf5-44f2-8b17-861dbaf90ca9"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[STATEMENT OF ROYALTY DISTRIBUTION ACCOUNT 年度使用報酬分配明細表]]></text>
			</staticText>
			<staticText>
				<reportElement x="780" y="30" width="70" height="20" uuid="2662da32-1c40-4996-8631-3e0d5b868f72"/>
				<text><![CDATA[DATE:]]></text>
			</staticText>
			<textField>
				<reportElement x="850" y="30" width="90" height="20" uuid="b8f8babc-c475-4e4c-a3b2-17d5db799cb6"/>
				<textFieldExpression><![CDATA[$P{date}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="101" width="80" height="20" uuid="60867f1e-170c-4e9e-8233-8b83a17a8e91"/>
				<text><![CDATA[SOCIETY NAME:]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="121" width="80" height="20" uuid="f2b1b8a0-dceb-45f4-aeee-79ca2462d616"/>
				<text><![CDATA[SOCIETY CODE:]]></text>
			</staticText>
			<textField>
				<reportElement x="90" y="101" width="90" height="20" uuid="8f9e1dfc-227a-4182-9473-4271d1ce9795"/>
				<textFieldExpression><![CDATA[$P{societyName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="90" y="121" width="90" height="20" uuid="a7ca9e7b-518f-4137-a723-d92f9fd8273d"/>
				<textFieldExpression><![CDATA[$P{societyCode}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="850" y="150" width="90" height="13" uuid="71a4b7f2-4c00-462c-b8c9-5960f47df759"/>
				<text><![CDATA[---------------------------------------------]]></text>
			</staticText>
			<textField>
				<reportElement x="860" y="130" width="70" height="20" uuid="3ffbcd6c-3dc6-4955-8391-8cb35a71cf36"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{payCurrency}]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="32" splitType="Stretch">
            <subreport>
                <reportElement x="0" y="1" width="950" height="30" uuid="6fd34d50-e996-4726-a5a3-5da62c1f757c"/>
                <dataSourceExpression>
                    <![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("distAutoPayReceipts")]]></dataSourceExpression>
                <subreportExpression>
<!--                    <![CDATA[$P{SUBREPORT_DIR}+"distAutoPays/DistAutoPay780_sub.jasper"]]></subreportExpression>-->
                    <![CDATA[Thread.currentThread().getContextClassLoader().getResource("ireport/jrxml/distAutoPays/DistAutoPay780_sub.jasper")]]></subreportExpression>
            </subreport>
		</band>
	</detail>
	<pageFooter>
		<band height="41" splitType="Stretch">
			<textField>
				<reportElement x="380" y="7" width="100" height="30" uuid="cc78ea94-72bc-4182-8db5-da024153b265"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="480" y="7" width="100" height="30" uuid="e6a49d19-a828-4b81-b411-3d0d40813b4b"/>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="446" splitType="Stretch">
			<staticText>
				<reportElement x="180" y="2" width="260" height="20" uuid="7aa88e91-e162-40e6-abf6-2bbef759d3e1"/>
				<text><![CDATA[BREAKDOWN OF 2019 PERFORMING ROYALTIES:]]></text>
			</staticText>
			<staticText>
				<reportElement x="180" y="30" width="80" height="20" uuid="df178451-c717-4a61-8448-1b4783f86ac4"/>
				<text><![CDATA[FEE IN ERROR]]></text>
			</staticText>
			<textField>
				<reportElement x="530" y="30" width="80" height="20" uuid="8ab116ca-0903-4e43-a133-1f9a5d3ee759"/>
				<textFieldExpression><![CDATA[$P{feeInError}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="681" y="63" width="119" height="17" uuid="5c428474-caea-437a-802a-4f7978425b0d"/>
				<text><![CDATA[-----------------------------------]]></text>
			</staticText>
			<staticText>
				<reportElement x="470" y="80" width="190" height="20" uuid="cced0ffb-6690-4285-9de3-d6c813702592"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[GRAND TOTAL ROYALTIES 權利金總計]]></text>
			</staticText>
			<staticText>
				<reportElement x="6" y="101" width="334" height="19" uuid="59def264-03cf-43cc-8665-4e787d3d0c8e"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[PAYMENT SETTLED BY TELEGRAPHIC TRANSFER 以電匯方式付款]]></text>
			</staticText>
			<staticText>
				<reportElement x="580" y="106" width="80" height="14" uuid="b4d0e45a-1796-446e-b68c-e5acd51459d8"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[權利金分發金額]]></text>
			</staticText>
			<line>
				<reportElement x="681" y="130" width="119" height="1" uuid="71a72ce1-5abd-4cc0-a068-15f2559df91c"/>
			</line>
			<line>
				<reportElement x="681" y="132" width="119" height="1" uuid="9225b480-be34-4a72-a1c1-e0d4f6b9d604"/>
			</line>
			<line>
				<reportElement x="820" y="198" width="119" height="1" uuid="5f5670ab-0973-4bc6-acac-3cc5d6e6c367"/>
			</line>
			<staticText>
				<reportElement x="130" y="140" width="250" height="20" uuid="af558d9f-d7a1-440c-8b92-67fc40e4bd88"/>
				<text><![CDATA[ADD : ROYALTIES FOR PRS-AFFILIATED SOCIETIES]]></text>
			</staticText>
			<staticText>
				<reportElement x="130" y="170" width="250" height="20" uuid="b6e27b9b-65a0-4434-a5f5-f8b86d3b411f"/>
				<text><![CDATA[LESS : BANK CHARGES]]></text>
			</staticText>
			<staticText>
				<reportElement x="130" y="196" width="250" height="20" uuid="54de4269-ec9e-42fe-8a42-08df34a157f7"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[NET PAYMENT 總計]]></text>
			</staticText>
			<line>
				<reportElement x="820" y="235" width="119" height="1" uuid="051e87d9-27d4-46b2-9b25-e11e1d00ef8b"/>
			</line>
			<staticText>
				<reportElement x="680" y="250" width="119" height="20" uuid="04c0de30-af96-45b9-ae0e-ecf181b712d7"/>
				<text><![CDATA[USD EXCHANGE RATE]]></text>
			</staticText>
			<line>
				<reportElement x="820" y="280" width="119" height="1" uuid="f0c45c47-fde2-47be-8bf4-f58268d02364"/>
			</line>
			<line>
				<reportElement x="818" y="318" width="119" height="1" uuid="7e04c0de-6029-4a6d-837b-9d461ba0ee4a"/>
			</line>
			<line>
				<reportElement x="818" y="320" width="119" height="1" uuid="********-2dd7-4557-8aa1-8157ef172461"/>
			</line>
			<staticText>
				<reportElement x="130" y="300" width="260" height="20" uuid="9cba5b32-0e23-4a87-92b7-5677b1701b86"/>
				<text><![CDATA[BREAKDOWN BY PRS-AFFILIATED SOCIETIES]]></text>
			</staticText>
			<staticText>
				<reportElement x="130" y="335" width="62" height="15" uuid="86b0a8c5-b3f9-4007-b2b0-e047d2896a56"/>
				<text><![CDATA[DIST NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="230" y="335" width="61" height="15" uuid="b6a87ff9-5ba8-488b-89a1-4da05ef0d9ff"/>
				<text><![CDATA[SOCIETIES]]></text>
			</staticText>
			<staticText>
				<reportElement x="330" y="335" width="60" height="15" uuid="93f01f8a-b0d1-482c-97ee-75afec51cac2"/>
				<text><![CDATA[ROYALTIES]]></text>
			</staticText>
			<staticText>
				<reportElement x="465" y="335" width="30" height="15" uuid="b39db85d-dff9-44ce-a117-6528d065ae6a"/>
				<text><![CDATA[TAX]]></text>
			</staticText>
			<staticText>
				<reportElement x="530" y="335" width="80" height="15" uuid="6ac78bce-16da-4d24-8270-abce078a3275"/>
				<text><![CDATA[COMMISSION]]></text>
			</staticText>
			<staticText>
				<reportElement x="640" y="335" width="100" height="15" uuid="360e2f52-95df-418e-885e-cae963f58369"/>
				<text><![CDATA[NET ROYALTIES]]></text>
			</staticText>
			<textField>
				<reportElement x="700" y="82" width="90" height="24" uuid="a0545cf0-341a-4df9-9204-e462727979f8"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{adjTotalAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="830" y="106" width="100" height="22" uuid="ad56a7fa-e895-47a3-af6b-bf0f42aa1020"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{adjSendAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="860" y="140" width="70" height="20" uuid="f6c44bc2-e94d-4334-a118-0faa3b037277"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{affiliated}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="850" y="176" width="80" height="20" uuid="c340cd52-6de0-4dfc-a94d-a193f507295f"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{bankCharge}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="843" y="250" width="90" height="20" uuid="cf007c38-952a-4c46-b41c-84a7dbdf2592"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{exchangeRate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="843" y="298" width="91" height="20" uuid="15b63f30-e92a-4951-ad3b-c5d5cfecb609"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{totalPayment}]]></textFieldExpression>
			</textField>
            <subreport>
                <reportElement x="136" y="360" width="604" height="30" uuid="d0cac003-e565-42ee-b43b-2ee13513a2b0"/>
                <dataSourceExpression>
                    <![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("prsAffiliated")]]></dataSourceExpression>
                <subreportExpression>
<!--                    <![CDATA[$P{SUBREPORT_DIR1}+"distAutoPays/DistAutoPay780_subTwo.jasper"]]></subreportExpression>-->
                    <![CDATA[Thread.currentThread().getContextClassLoader().getResource("ireport/jrxml/distAutoPays/DistAutoPay780_subTwo.jasper")]]></subreportExpression>
            </subreport>
			<textField>
				<reportElement x="626" y="405" width="90" height="20" uuid="82154bea-d32a-48ee-a6a6-2451ddbb9b6a"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{affiliated}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="620" y="400" width="102" height="1" uuid="633e0e74-**************-a8b537bf34d8"/>
			</line>
			<line>
				<reportElement x="620" y="430" width="102" height="1" uuid="350f17c8-a9b1-41cd-a49e-909c78e9b6a1"/>
			</line>
			<textField>
				<reportElement x="848" y="210" width="81" height="20" uuid="b21a3f36-8658-4180-a5a4-1ab395b9e7d4"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{netPayment}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="825" y="132" width="119" height="1" uuid="73b393c6-b937-4796-8712-4682231dadd5"/>
			</line>
			<line>
				<reportElement x="825" y="130" width="119" height="1" uuid="8b8791f0-da13-4a6f-a860-ce098394462a"/>
			</line>
		</band>
	</summary>
</jasperReport>
