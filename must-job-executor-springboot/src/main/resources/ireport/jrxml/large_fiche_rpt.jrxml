<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.16.0.final using JasperReports Library version 6.16.0-48579d909b7943b64690c65c71e07e0b80981928  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="large_fiche_rpt" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isFloatColumnFooter="true" uuid="dff31b8a-2e45-4050-8c42-7d9573379f97">
	<property name="ireport.zoom" value="1.7715610000000066"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<subDataset name="contractList" uuid="7e03c01e-ba4e-4e95-ae19-55cbf5a91fe0">
		<queryString>
			<![CDATA[]]>
		</queryString>
	</subDataset>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<field name="no" class="java.lang.String"/>
	<field name="title" class="java.lang.String"/>
	<field name="gi" class="java.lang.String"/>
	<field name="workNo" class="java.lang.Long"/>
	<field name="otherTitleList" class="java.lang.Object"/>
	<field name="duration" class="java.lang.String"/>
	<field name="iswc" class="java.lang.String"/>
	<field name="createTime" class="java.lang.String"/>
	<field name="socCountry" class="java.lang.String"/>
	<field name="contractList" class="java.lang.Object"/>
	<field name="art" class="java.lang.String"/>
	<field name="ipShareList" class="java.lang.Object"/>
	<field name="ot" class="java.lang.String"/>
	<field name="hiddenAt" class="java.lang.Boolean"/>
	<group name="work_group">
		<groupExpression><![CDATA[$F{no}]]></groupExpression>
	</group>
	<title>
		<band height="43">
			<line>
				<reportElement x="78" y="20" width="400" height="1" forecolor="#FF0000" uuid="ac8f7ce0-497a-48b7-8498-8b36f29d819b">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="83" y="23" width="17" height="10" uuid="6ee32b60-1dc1-4960-b9f2-944dcfcd8a6d"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[T  :]]></text>
			</staticText>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="100" y="21" width="358" height="14" uuid="837ab3fd-39be-4a4f-8dc7-10bccf022186"/>
				<textElement>
					<font fontName="华文宋体" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{title}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="80" y="10" width="36" height="10" uuid="aa02127a-18d6-453e-a52e-811b858364f4"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[Remark:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="110" y="10" width="51" height="10" uuid="84c34205-affa-4ead-b9ff-3fdda40b165a"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{no}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement stretchType="ContainerHeight" x="477" y="20" width="1" height="23" forecolor="#FF0000" uuid="3e44cb9d-0710-497c-9525-4f8d27cd412d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="ContainerHeight" x="77" y="20" width="1" height="23" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="98491ecf-c38f-4415-8dee-c23b9acf497d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
		</band>
	</title>
	<detail>
		<band height="21">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<printWhenExpression><![CDATA[!$F{hiddenAt} ? true : false]]></printWhenExpression>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="83" y="3" width="17" height="10" uuid="38448785-9a36-4262-a0f2-9ba767f2cfd8"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[AT:]]></text>
			</staticText>
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="100" y="2" width="358" height="13" uuid="0c93b6c8-dd62-4173-9edd-90fefe1552a6"/>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("otherTitleList")]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "large_fiche_rpt_subreport_otherTitle.jasper"]]></subreportExpression>
			</subreport>
			<line>
				<reportElement stretchType="ContainerHeight" x="477" y="0" width="1" height="21" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="0a423ee6-2d93-4ff9-8c98-45661212ab53">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="ContainerHeight" x="77" y="0" width="1" height="21" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="d0485f1b-d418-4c37-b366-f1e1bffd9473">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="77" y="0" width="400" height="1" forecolor="#FF0000" uuid="8db25b26-6bfe-4501-ac1c-6f6127d44749">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
		</band>
		<band height="21">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<printWhenExpression><![CDATA[$F{ot} != null ? true : false]]></printWhenExpression>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="83" y="5" width="17" height="10" uuid="2ec0fe88-9dc4-41e9-860a-474ef21d34d6"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[OT:]]></text>
			</staticText>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" stretchType="RelativeToTallestObject" x="100" y="4" width="358" height="14" uuid="38314123-5fcd-4cb9-8b7f-b8fa6754c20c"/>
				<textElement>
					<font fontName="华文宋体" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{ot}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement stretchType="ContainerHeight" x="477" y="0" width="1" height="21" forecolor="#FF0000" uuid="1f59cb8f-a1e0-4729-943f-35614b9027aa">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="ContainerHeight" x="77" y="0" width="1" height="21" forecolor="#FF0000" uuid="cdd2aadb-d5c2-4aa0-9712-53196c590517">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="77" y="0" width="400" height="1" forecolor="#FF0000" uuid="e49c009d-81a6-4716-8586-fb54277fcf43"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
		</band>
		<band height="116" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout"/>
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<line>
				<reportElement stretchType="ContainerHeight" x="77" y="0" width="1" height="116" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="bd5aa514-e822-494c-9e7a-db452ebd7751"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="ContainerHeight" x="477" y="0" width="1" height="116" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="56b04796-fa2d-441d-87eb-dc5fdb683964"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement stretchType="RelativeToTallestObject" x="83" y="10" width="17" height="10" uuid="a04ec1be-02ba-4f01-8ae7-e1fb9eaa2c60"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[G/I:]]></text>
			</staticText>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="100" y="10" width="60" height="10" uuid="0551568e-70d3-4f8a-84d1-d406b61260ef"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{gi}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="78" y="30" width="400" height="1" forecolor="#FF0000" uuid="e13b395e-3537-4b1b-9d44-75df5a8dcc38"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="389" y="10" width="17" height="10" uuid="e6c22873-8c1c-4327-b9eb-35eb5a83dac9"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[D:  ]]></text>
			</staticText>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" x="406" y="10" width="44" height="10" uuid="c486d775-083e-4d95-ae59-1ed6ca0c84e0"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{duration}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="121" y="44" width="39" height="10" isPrintWhenDetailOverflows="true" uuid="b18774da-af86-4cda-9a04-392433020d28"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[Ayants droit
]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="83" y="44" width="30" height="10" isPrintWhenDetailOverflows="true" uuid="d04e20cb-20e0-4e9a-bc66-ae184c160ab1"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[Status]]></text>
			</staticText>
			<subreport>
				<reportElement x="78" y="60" width="399" height="24" uuid="58932508-5a2f-439f-a8cd-29dd70071ebf"/>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("ipShareList")]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"large_fiche_rpt_subreport_ipshare.jasper"]]></subreportExpression>
			</subreport>
			<line>
				<reportElement stretchType="ContainerHeight" x="116" y="30" width="1" height="55" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="fb065961-6e02-408d-8e4c-7a0cc63b1591"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="ContainerHeight" x="231" y="30" width="1" height="55" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="05490d7e-b2cd-4ace-964f-edb75cfa08fd"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="ContainerHeight" x="301" y="30" width="1" height="55" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="85c6faa8-bbb7-4da7-9ecb-f905988f0a1b"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="ContainerHeight" x="337" y="44" width="1" height="40" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="e0d6dc3e-9c89-4be4-9e90-2738031b7bd6"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="239" y="44" width="49" height="10" isPrintWhenDetailOverflows="true" uuid="ac6c5309-07b3-4327-a751-7aaa759482c1"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[IP Name No.]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="306" y="34" width="62" height="10" isPrintWhenDetailOverflows="true" uuid="d3fada43-38b0-448d-97e2-28b47328531d"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[Droits d execution]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="306" y="45" width="19" height="10" isPrintWhenDetailOverflows="true" uuid="8d8d86da-bfe5-44f0-9788-db9bde20f9f4"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[Soc.]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="341" y="45" width="22" height="10" isPrintWhenDetailOverflows="true" uuid="43dd7ce9-8333-413c-8a22-a732f5c80fcc"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[Part %]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="396" y="45" width="19" height="10" isPrintWhenDetailOverflows="true" uuid="73cc9ee3-1317-45c3-af9d-6486395b21af"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[Soc.]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="425" y="45" width="27" height="10" isPrintWhenDetailOverflows="true" uuid="93ee437b-a0b2-41da-869d-f0b197a4695e"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[Part %]]></text>
			</staticText>
			<staticText>
				<reportElement stretchType="RelativeToBandHeight" x="396" y="34" width="62" height="10" isPrintWhenDetailOverflows="true" uuid="cfeb9f81-ba06-4235-a34f-9274f00b6731"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[Droits mecaniques]]></text>
			</staticText>
			<line>
				<reportElement stretchType="ContainerHeight" x="391" y="30" width="1" height="55" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="384b86fe-9fc7-436e-a590-747d2a93ddfe"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="ContainerHeight" x="421" y="44" width="1" height="40" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="fb4a9e90-8448-4508-b7fa-12ef5d1f91f2"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement positionType="Float" x="78" y="85" width="400" height="1" forecolor="#FF0000" uuid="39264dc6-239a-4fef-803a-9279e8e5764e"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement positionType="Float" x="83" y="95" width="30" height="10" isPrintWhenDetailOverflows="true" uuid="d9b36e76-79e9-4d55-aadd-eb2d7367d347"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[ART:]]></text>
			</staticText>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="110" y="94" width="330" height="12" uuid="ccfbc726-971f-47b2-bbef-b5eafc1fc5e5"/>
				<textElement>
					<font fontName="华文宋体" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{art}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="77" y="0" width="400" height="1" forecolor="#FF0000" uuid="cf890d6b-09c0-4d9e-94c7-eefb50d20516">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
		</band>
		<band height="125">
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="352" y="69" width="80" height="10" uuid="f16078ec-630d-4397-8b79-6a73b41f9932"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{iswc}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight" isBlankWhenNull="true">
				<reportElement positionType="Float" x="117" y="49" width="80" height="10" uuid="16fa6cbb-c11f-476e-9e16-0615bd35470f"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{createTime}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="Float" x="161" y="59" width="100" height="20" uuid="24e2190f-c44e-4b89-b5ee-0f630f169ed1"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{socCountry}]]></textFieldExpression>
			</textField>
			<textField textAdjust="StretchHeight">
				<reportElement positionType="Float" x="352" y="59" width="100" height="10" uuid="a09190a1-fd21-424e-adde-a28d0e764856"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{workNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="83" y="10" width="87" height="10" isPrintWhenDetailOverflows="true" uuid="988f9887-b908-421d-82c8-b4d24c678fd5"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[Duration of Contract:]]></text>
			</staticText>
			<line>
				<reportElement positionType="Float" x="77" y="40" width="400" height="1" forecolor="#FF0000" uuid="91582dc9-14aa-4fec-a851-c05af23ffcce"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement positionType="Float" x="83" y="49" width="34" height="10" isPrintWhenDetailOverflows="true" uuid="c4f8b309-0b40-4455-a8ff-3222ba3b5851"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[MUST]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="83" y="59" width="78" height="10" isPrintWhenDetailOverflows="true" uuid="f17f9294-6016-40f9-9ff8-3b772a02ae16"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[DATA FOR TERRITORY:]]></text>
			</staticText>
			<staticText>
				<reportElement positionType="Float" x="352" y="49" width="78" height="10" isPrintWhenDetailOverflows="true" uuid="2cb29661-475a-4352-9c7a-4436747bca4c"/>
				<textElement>
					<font fontName="华文宋体" size="7"/>
				</textElement>
				<text><![CDATA[DIVA Work  No/ISWC]]></text>
			</staticText>
			<line>
				<reportElement positionType="Float" x="78" y="85" width="400" height="1" forecolor="#FF0000" uuid="78962a26-321a-410c-ab7e-6ce4f621d37d"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<subreport>
				<reportElement x="83" y="20" width="394" height="14" uuid="3427c90f-4d7b-4bb3-8560-f0eca27f8590"/>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("contractList")]]></dataSourceExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} +"large_fiche_rpt_subreport_contract.jasper"]]></subreportExpression>
			</subreport>
			<line>
				<reportElement positionType="Float" x="75" y="100" width="408" height="1" forecolor="#000000" uuid="4863848c-689f-4b05-a9cf-d47095b2a3e0">
					<printWhenExpression><![CDATA[!$F{gi}.equals( "" )]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.4" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="ContainerHeight" x="477" y="0" width="1" height="85" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="1c80cf6c-87d8-4054-9d0b-6092f9814bdc"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement stretchType="ContainerHeight" x="77" y="0" width="1" height="85" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="d1f11636-14c5-4c62-8d99-09a9e7477562"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement positionType="Float" x="344" y="40" width="1" height="45" isPrintWhenDetailOverflows="true" forecolor="#FF0000" uuid="deb6e8a7-25de-45db-9dbd-3939897c5118">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement x="78" y="1" width="400" height="1" forecolor="#FF0000" uuid="2842968f-e524-4a42-8561-cd964554eb98"/>
				<graphicElement>
					<pen lineWidth="0.2" lineStyle="Solid"/>
				</graphicElement>
			</line>
		</band>
	</detail>
</jasperReport>
