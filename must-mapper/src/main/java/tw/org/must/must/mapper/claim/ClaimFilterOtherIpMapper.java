package tw.org.must.must.mapper.claim;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.claim.ClaimFilterOtherIp;
import tw.org.must.must.model.claim.ClaimFilterOtherWork;

import java.util.List;

@Repository
public interface ClaimFilterOtherIpMapper extends BaseMapper<ClaimFilterOtherIp> {


//    void truncateTable();

    @Select("delete from claim_filter_other_ip where company = #{company}")
    void deleteByCompany(@Param("company") String company);

    @Select("select id,work_unique_key from claim_filter_other_ip where company = #{company} and id > #{startId} order by id asc limit 100000")
    List<ClaimFilterOtherIp> getWorkUniqueKeyByCompany(@Param("compay") String company, @Param("startId") Long startId);
}