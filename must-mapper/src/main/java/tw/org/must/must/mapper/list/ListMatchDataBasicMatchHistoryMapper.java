package tw.org.must.must.mapper.list;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.list.ListMatchDataBasicMatchHistory;

import java.util.List;

@Repository
public interface ListMatchDataBasicMatchHistoryMapper extends BaseMapper<ListMatchDataBasicMatchHistory> {

    void insertDuplicate(List<ListMatchDataBasicMatchHistory> list);

    void insertListMatchDataBasicMatchHistoryForTitleUpdate(List<ListMatchDataBasicMatchHistory> list);

}