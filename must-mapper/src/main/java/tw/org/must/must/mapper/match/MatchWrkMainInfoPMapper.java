package tw.org.must.must.mapper.match;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.match.MatchWrkMainInfoP;
import tw.org.must.must.model.match.vo.MatchWrkMainInfoPVo;

import java.util.List;

@Repository
public interface MatchWrkMainInfoPMapper extends BaseMapper<MatchWrkMainInfoP> {

	Integer insertOrUpdateByList(List<MatchWrkMainInfoP> list);

	List<MatchWrkMainInfoPVo> getMatchWrkMainInfoPList(MatchWrkMainInfoPVo matchWrkMainInfoVo);


}