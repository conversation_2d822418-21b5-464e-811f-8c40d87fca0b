package tw.org.must.must.mapper.sys;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.sys.SysRoleMenu;

import java.util.List;

@Repository
public interface SysRoleMenuMapper extends BaseMapper<SysRoleMenu> {

    /**
     * @description: 根据角色id获取菜单id列表
     * @param roleId
     * @return
     */
    public List<Long> selectMenusByRoleId(@Param("roleId") Long roleId);

    public Long deleteRoleMenuList(@Param("roleId") Long roleId, @Param("list") List<Long> MenuIds);


}