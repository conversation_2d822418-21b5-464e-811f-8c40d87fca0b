package tw.org.must.must.mapper.wrk;

import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.wrk.WrkIswc;

import java.util.List;
import java.util.Set;

@Repository
public interface WrkIswcMapper extends BaseMapper<WrkIswc> {


    @Select("select id,iswc,worknum,worknum_society,work_unique_key from wrk_iswc where id > #{id} AND iswc <> '' AND iswc is not NULL  order by id asc limit #{size}")
    List<WrkIswc> selectIswcGreater(Long id, int size);

    @Select("SELECT iswc FROM wrk_iswc wi WHERE iswc is NOT NULL AND iswc <> '' GROUP  BY iswc HAVING COUNT(1) > 1")
    Set<String> selectDuplicateIswcList();

}