package tw.org.must.must.mapper.wrk;

import org.springframework.stereotype.Repository;
import tw.org.must.must.common.base.BaseMapper;
import tw.org.must.must.model.wrk.WrkWorkTransfer;

import java.util.List;

@Repository
public interface WrkWorkTransferMapper extends BaseMapper<WrkWorkTransfer> {

	Long saveWrkWorkTransferReceiveId(WrkWorkTransfer wwt);

	List<WrkWorkTransfer> getWorkTransferByIdAndSociety(Long workSourceNo , Integer workSourceNoSociety);


}