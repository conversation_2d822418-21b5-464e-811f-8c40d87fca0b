<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tw.org.must.must.mapper.distdata.DistDataCalcWorkPointMapper">
    <resultMap id="BaseResultMap" type="tw.org.must.must.model.distdata.DistDataCalcWorkPoint">

    </resultMap>
    <select id="getDistRoyltiesOverNumberByWork"
            resultType="tw.org.must.must.model.report.DistRoyaltiesOverNumberByWork">
        SELECT
			t1.id,
			t1.dist_no as distNo,
			t1.work_id as workNo,
			t1.work_society_code as workSoc,
			t1.work_title_id,
			IF(t2.title is null or t2.title = '', t2.title_en, t2.title) as workTitle,
			sum(t1.dist_roy) as totalPerformanceRoyatlies
		FROM
			`dist_data_calc_work_point` t1
		LEFT JOIN wrk_work_title t2 on t1.work_title_id = t2.id
		WHERE t1.dist_no = #{distNo}
		GROUP BY
			t1.dist_no,
			t1.work_unique_key
		HAVING
			totalPerformanceRoyatlies > #{number};
    </select>
    <select id="getOverseasDistributionPaymentWorkDetail"
            resultType="tw.org.must.must.model.report.OverseasDistributionPaymentWorkDetail">
		SELECT DISTINCT
		t1.ip_base_no as accountId,
		IFNULL(t3.chinese_name, t3.name) as accountName,
		t4.remit_society_code as societyCode,
		(case when t4.remit_society_code <![CDATA[ < ]]> 100 THEN CONCAT('0',t4.remit_society_code,t5.dist_order_number)
	    ELSE CONCAT(t4.remit_society_code,t5.dist_order_number) END ) as adjDistNo,
		t1.work_unique_key as workUniqueKey,
		IFNULL(t6.title, t6.title_en) as workTitle,
		t1.dist_roy as royalties
		FROM
		(
		SELECT
		file_base_id,
		work_unique_key,
		ip_base_no,
		SUM(dist_roy) as dist_roy ,
		MIN(ip_name_no) as ip_name_no ,
		MIN(ip_society_code) as ip_society_code ,
		MIN(title_id) as title_id
		FROM
		dist_data_calc_work_ip_roy
		WHERE
		dist_no = #{distNo}
		AND ip_society_code = 161
		AND dist_roy != 0
		group by
		file_base_id,
		work_unique_key,
		ip_base_no) t1
		left join mbr_ip_name t3 on
		t1.ip_name_no = t3.ip_name_no
		left join list_overseas_file_base t4 on
		t1.file_base_id = t4.id
		left JOIN list_overseas_receipt_details t5 on
		t4.receipt_id = t5.receipt_id
		left join wrk_work_title t6 on
		t1.title_id = t6.id
		ORDER BY t1.ip_base_no

	</select>

	<select id="getOverseasDistributionPaymentWorkDetailSoc"
			resultType="tw.org.must.must.model.report.OverseasDistributionPaymentWorkDetail">
		SELECT
		t1.ip_society_code as accountId,
		t3.society_name as accountName,
		t4.remit_society_code as societyCode,
		CONCAT((case when t4.remit_society_code <![CDATA[ < ]]> 100 THEN LPAD(t4.remit_society_code, 3, '0')
		ELSE t4.remit_society_code
		END ), t5.dist_order_number) as adjDistNo,
		t1.work_unique_key as workUniqueKey,
		IFNULL(t6.title, t6.title_en) as workTitle,
		t1.dist_roy as royalties
		FROM
		(
		SELECT

		file_base_id,
		work_unique_key,
		ip_society_code ,
		SUM(dist_roy) as dist_roy,
		MIN(title_id) as title_id
		FROM
			dist_data_calc_work_ip_roy
		WHERE
			dist_no = #{distNo}
		AND ip_society_code != 161
		AND dist_roy != 0
		group by
		file_base_id,
		work_unique_key,
		ip_base_no) t1
		left join ref_society t3 on
		t1.ip_society_code = t3.society_code
		left join list_overseas_file_base t4 on
		t1.file_base_id = t4.id
		left JOIN list_overseas_receipt_details t5 on
		t4.receipt_id = t5.receipt_id
		left join wrk_work_title t6 on
		t1.title_id = t6.id

	</select>

    <select id="selectFileBaseIdList" resultType="java.lang.String">
		SELECT DISTINCT file_base_id
		FROM `dist_data_calc_work_point`
		WHERE dist_no = #{distNo}
	</select>
</mapper>