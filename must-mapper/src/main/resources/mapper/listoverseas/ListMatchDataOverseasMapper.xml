<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tw.org.must.must.mapper.listoverseas.ListMatchDataOverseasMapper">
    <resultMap id="BaseResultMap" type="tw.org.must.must.model.listoverseas.ListMatchDataOverseas">

    </resultMap>
    <select id="getCountByFidAndDataUniqueKey" resultType="java.lang.Integer">
        select count(1) from list_match_data_overseas
        where file_base_id = #{fileBaseId} and data_unique_key  = #{dataUniqueKey}
    </select>
    <select id="getCountDataUniqueKey" resultType="java.lang.Integer">
        select count(1) from list_match_data_overseas
        where  data_unique_key  = #{dataUniqueKey}
    </select>
    <select id="listRemittedWork" resultType="tw.org.must.must.model.listoverseas.ListMatchDataOverseas">
        select       lo.*,lm.file_type as fileType,
                     case when
		              (SELECT COUNT(1) FROM list_match_data_overseas_mapping lm WHERE lm.file_base_id = lo.file_base_id AND lm.data_unique_key = lo.data_unique_key and lm.status = 0) > 0
                       then 1 ELSE 0 END
                     AS hasUnCheck
        from         list_match_data_overseas lo
        inner join   list_match_data_overseas_uniq lu on lo.data_unique_key = lu.data_unique_key  and lu.status in (1,3)
        inner join   list_overseas_file_work_mapping lm on lm.id = lo.file_mapping_id
        where        lu.match_work_unique_key = #{workUniqueKey}
    </select>

    <select id="getDataUniqueKeySetWithDataUniqueKeySet" resultType="java.lang.String">
        SELECT data_unique_key  FROM list_match_data_overseas
        where data_unique_key in
            <foreach collection="dataUniqueKeySet" item="item" open="(" close=")"  separator=",">
                #{item}
            </foreach>
        GROUP BY data_unique_key
    </select>

    <select id="getDataUniqueKeys" resultType="java.lang.String">
        select distinct t1.data_unique_key from list_match_data_overseas_uniq t1
        <if test="fid != null or (distNo != null and distNo != '')">
            inner join list_match_data_overseas t2 on t1.data_unique_key = t2.data_unique_key
        </if>
        <if test="(matchIpSoc != null) or (distributedIp != null and distributedIp != '') or (distributedIpName != null and distributedIpName != '') or (rejectCode != null) or (remitIpName != null and remitIpName != '')" >
            inner join list_match_data_overseas_mapping t3 on t1.data_unique_key = t3.data_unique_key
        </if>
<!--        <if test="(remitIpName != null and remitIpName != '') or (remitWorkTitle != null and remitWorkTitle != '')" >-->
<!--            inner join list_overseas_file_work_mapping fm-->
<!--        </if>-->
        where 1=1
        <if test="dataUniqueKey != null and dataUniqueKey != ''">
            and t1.data_unique_key = #{dataUniqueKey}
        </if>
        <if test="fid != null">
            and t2.file_base_id = #{fid}
        </if>
        <if test="matchWorkId != null">
            and t1.match_work_id = #{matchWorkId}
        </if>
        <if test="batch != null">
            and t1.batch = #{batch}
        </if>
        <if test="matchWorkSoc != null">
            and t1.match_work_society_code = #{matchWorkSoc}
        </if>
        <if test="matchIpSoc != null">
            and t3.match_ip_soc = #{matchIpSoc}
        </if>
        <if test="matchWorkTitle != null and matchWorkTitle != ''">
            and t1.match_work_title like CONCAT('%',#{matchWorkTitle},'%')
        </if>
        <if test="distributedIp != null and distributedIp !=''">
            and t3.match_ip_name_no = #{distributedIp}
        </if>
        <if test="distributedIpName != null and distributedIpName !=''">
            and t3.match_ip_name like CONCAT('%',#{distributedIpName},'%')
        </if>
        <if test="remitSoc != null">
            and t1.remit_society = #{remitSoc}
        </if>
        <if test="remitWorkTitle != null and remitWorkTitle != ''">
            and t1.original_title like CONCAT('%',#{remitWorkTitle},'%')
        </if>
        <if test="remitIpName != null and remitIpName !=''">
            and t3.ip_name like  CONCAT('%',#{remitIpName},'%')
        </if>
        <if test="status != null">
            and t1.status = #{status}
        </if>
        <if test="status == null">
            and t1.status in(1,3)
        </if>
        <if test="rejectCode != null and rejectCode != ''">
            <choose>
                <when test="rejectCode = 'Y'">
                    and t3.reject_code is not null
                </when>
                <otherwise>
                    and t3.reject_code is null
                </otherwise>
            </choose>
        </if>
    </select>

    <select id = "getListMatchDataOverseaRemittedWork" resultType="tw.org.must.must.model.listoverseas.ListMatchDataOverseas">
        select t1.*,t2.file_type as fileType from list_match_data_overseas t1 inner join list_overseas_file_base t2
        on t1.file_base_id = t2.id
        where t1.data_unique_key = #{dataUniqueKey} and t1.status in(1,3)
    </select>

    <select id="getListMatchDataOverseasList" resultType="tw.org.must.must.model.listoverseas.ListMatchDataOverseas" >
        select ta.*,tb.overseas_dist_no as remitDistNo from list_match_data_overseas ta inner join list_overseas_file_base tb on ta.file_base_id = tb.id
        where 1=1
        <if test="fileBaseId != null">
            and ta.file_base_id = #{fileBaseId}
        </if>
        <if test="remitSociety != null">
            and ta.remit_society = #{remitSociety}
        </if>
        <if test="sourceWorkCode != null and sourceWorkCode != ''">
            and ta.source_work_code = #{sourceWorkCode}
        </if>
        <if test="originalTitle != null and originalTitle != ''">
            and ta.original_title like CONCAT('%',#{originalTitle},'%')
        </if>
        <if test="dataUniqueKey != null and dataUniqueKey != ''">
            and ta.data_unique_key = #{dataUniqueKey}
        </if>
        <if test="status != null">
            and ta.status = #{status}
        </if>

    </select>

    <insert id="insertOnDuplicateKeyUpdate" useGeneratedKeys="true" keyProperty="id" parameterType="tw.org.must.must.model.listoverseas.ListMatchDataOverseas">
        insert into list_match_data_overseas (
            file_base_id,
            file_mapping_id,
            remit_society,
            receipt_society,
            source_work_code,
            iswc,
            isrc,
            original_title,
            sub_title,
            composer_name,
            artist_name,
            author_name,
            create_time,
            author_composer,
            amend_time,
            status,
            match_work_id,
            match_work_society_code,
            match_work_title_id,
            match_work_title,
            match_work_type,
            match_score,
            data_unique_key,
            data_unique_key_str,
            upload_user_id,
            upload_user_name,
            reference_number,
            batch,
            match_work_unique_key,
            match_type,
            match_sub_title_id,
            dist_no
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.fileBaseId},
            #{item.fileMappingId},
            #{item.remitSociety},
            #{item.receiptSociety},
            #{item.sourceWorkCode},
            #{item.iswc},
            #{item.isrc},
            #{item.originalTitle},
            #{item.subTitle},
            #{item.composerName},
            #{item.artistName},
            #{item.authorName},
            #{item.createTime},
            #{item.authorComposer},
            #{item.amendTime},
            #{item.status},
            #{item.matchWorkId},
            #{item.matchWorkSocietyCode},
            #{item.matchWorkTitleId},
            #{item.matchWorkTitle},
            #{item.matchWorkType},
            #{item.matchScore},
            #{item.dataUniqueKey},
            #{item.dataUniqueKeyStr},
            #{item.uploadUserId},
            #{item.uploadUserName},
            #{item.referenceNumber},
            #{item.batch},
            #{item.matchWorkUniqueKey},
            #{item.matchType},
            #{item.matchSubTitleId},
            #{item.distNo}
            )
        </foreach>
        status = values(status),
        match_work_id = values(match_work_id),
        match_work_society_code = values(match_work_society_code),
        match_work_title_id = values(match_work_title_id),
        match_work_title = values(match_work_title),
        match_work_type = values(match_work_type),
        match_score = values(match_score),
        match_work_unique_key = values(match_work_unique_key),
        match_type = values(match_type),
        match_sub_title_id = values(match_sub_title_id),
        amend_time = values(amend_time)
    </insert>

    <!-- 根据条件查询去重后的数据（按指定字段分组，取每组中ID最小的记录） -->
    <select id="getDedupListMatchDataOverseas" resultType="tw.org.must.must.model.listoverseas.ListMatchDataOverseas">
        SELECT ta.*, tb.overseas_dist_no as remitDistNo
        FROM list_match_data_overseas ta
        INNER JOIN list_overseas_file_base tb ON ta.file_base_id = tb.id
        WHERE ta.id IN (
            SELECT MIN(id)
            FROM list_match_data_overseas
            WHERE file_base_id IN
            <foreach collection="fileBaseIds" item="fileBaseId" open="(" close=")" separator=",">
                #{fileBaseId}
            </foreach>
            <if test="status != null">
                AND status = #{status}
            </if>
            GROUP BY
            <trim prefixOverrides=",">
                <if test="groupByDataUniqueKey">
                    data_unique_key
                </if>
                <if test="groupByOriginalTitle">
                    <if test="groupByDataUniqueKey">, </if>
                    original_title
                </if>
                <if test="groupByAuthorComposer">
                    <if test="groupByDataUniqueKey or groupByOriginalTitle">, </if>
                    author_composer
                </if>
            </trim>
        )
        ORDER BY ta.original_title ASC
    </select>
</mapper>