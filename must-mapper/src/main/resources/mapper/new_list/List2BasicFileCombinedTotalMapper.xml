<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tw.org.must.must.mapper.new_list.List2BasicFileCombinedTotalMapper">
    <resultMap id="BaseResultMap" type="tw.org.must.must.model.new_list.List2BasicFileCombinedTotal">
    </resultMap>

    <!-- 检查指定folder和日期范围是否与现有数据重合 -->
    <select id="checkDateRangeOverlap" resultType="tw.org.must.must.model.new_list.List2BasicFileCombinedTotal">
        SELECT * FROM list2_basic_file_combined_total 
        WHERE folder = #{folder}
        AND is_deleted = 0
        AND (
            (#{startDate} &lt;= combined_end_date AND #{endDate} &gt;= combined_start_date)
        )
    </select>
</mapper>
