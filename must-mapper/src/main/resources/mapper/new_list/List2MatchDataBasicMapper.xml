<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tw.org.must.must.mapper.new_list.List2MatchDataBasicMapper">
    <resultMap id="BaseResultMap" type="tw.org.must.must.model.new_list.List2MatchDataBasic">
    </resultMap>
    <select id="getCountGroupByFileBaseId" resultType="java.util.Map">
        SELECT
            file_base_id AS fileId,
            COUNT(CASE WHEN STATUS = 0 or STATUS is null THEN 0 END) AS unmatchedCount,
            COUNT(CASE WHEN STATUS > 0 THEN 1 END) AS matchCount
        FROM
            list2_match_data_basic
        GROUP BY
            file_base_id having COUNT(file_base_id) > matchCount


    </select>

    <select id="getBasicCountGroupByStatus" resultType="java.util.Map">
        SELECT
            status,
            count(1) as num
        FROM
            list2_match_data_basic
        WHERE
            file_base_id = #{baseId}
        GROUP BY
            status


    </select>

    <update id="updateListMatchDataBasicFromHistory">
        update list2_match_data_basic set
        match_work_id	 = #{listMatchDataBasic.matchWorkId},
        match_work_society_code	 =#{listMatchDataBasic.matchWorkSocietyCode},
        unique_key_md5	 =#{listMatchDataBasic.uniqueKeyMd5},
        match_work_type	 =#{listMatchDataBasic.matchWorkType},
        match_work_unique_key = #{listMatchDataBasic.matchWorkUniqueKey},
        match_work_title	 =#{listMatchDataBasic.matchWorkTitle},
        match_work_composers	 =#{listMatchDataBasic.matchWorkComposers},
        match_work_authors	 =#{listMatchDataBasic.matchWorkAuthors},
        isrc	 =#{listMatchDataBasic.isrc},
        iswc	 =#{listMatchDataBasic.iswc},
        match_score	 =#{listMatchDataBasic.matchScore},
        amend_time=#{listMatchDataBasic.amendTime},
        match_sub_title_id = #{listMatchDataBasic.matchSubTitleId}
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

    <insert id="insertDuplicate" useGeneratedKeys="true" keyProperty="id"
            parameterType="tw.org.must.must.model.new_list.List2MatchDataBasic">
        insert into list2_match_data_basic
        (
        title,
        artists,
        authors,
        composers,
        publisher,
        episode_no,
        upload_type,
        click_number,
        duration_m,
        duration_s,
        perform_time,
        file_mapping_id,
        file_base_id,
        batch_id,
        isrc,
        iswc,
        tv_name,
        channel_name,
        ext_json,
        match_work_id,
        match_work_society_code,
        match_work_title_id,
        match_work_title,
        match_work_authors,
        match_work_composers,
        match_work_type,
        match_work_unique_key,
        status,
        auto_match,
        match_score,
        source_code,
        unique_key_md5,
        album_title,
        pool_code,
        pool_right,
        create_time,
        amend_time,
        category_code,
        upload_time,
        upload_user_id,
        upload_user_name,
        match_sub_title_id
        )
        values
        <foreach collection="list" item="item" index="index"
                 separator=",">
            (
            #{item.title},
            #{item.artists},
            #{item.authors},
            #{item.composers},
            #{item.publisher},
            #{item.episodeNo},
            #{item.uploadType},
            #{item.clickNumber},
            #{item.durationM},
            #{item.durationS},
            #{item.performTime},
            #{item.fileMappingId},
            #{item.fileBaseId},
            #{item.batchId},
            #{item.isrc},
            #{item.iswc},
            #{item.tvName},
            #{item.channelName},
            #{item.extJson},
            #{item.matchWorkId},
            #{item.matchWorkSocietyCode},
            #{item.matchWorkTitleId},
            #{item.matchWorkTitle},
            #{item.matchWorkAuthors},
            #{item.matchWorkComposers},
            #{item.matchWorkType},
            #{item.matchWorkUniqueKey},
            #{item.status},
            #{item.autoMatch},
            #{item.matchScore},
            #{item.sourceCode},
            #{item.uniqueKeyMd5},
            #{item.albumTitle},
            #{item.poolCode},
            #{item.poolRight},
            #{item.createTime},
            #{item.amendTime},
            #{item.categoryCode},
            #{item.uploadTime},
            #{item.uploadUserId},
            #{item.uploadUserName},
            #{item.matchSubTitleId}
            )
        </foreach>
        ON DUPLICATE KEY
        update  match_work_id    = values( match_work_id),
        match_work_society_code     = values( match_work_society_code),
        match_work_unique_key   = values( match_work_unique_key),
        match_work_type     = values( match_work_type),
        match_work_title    = values( match_work_title),
        match_work_title_id     = values( match_work_title_id),
        match_work_composers     = values( match_work_composers),
        match_work_authors   = values( match_work_authors),
        match_sub_title_id   = values( match_sub_title_id),
        isrc  = values( isrc),
        iswc  = values( iswc),
        match_score     = values( match_score),
        source_code     = values( source_code),
        album_title     = values( album_title),
        status  = values( status),
        auto_match =  values (auto_match),
        upload_type = values(upload_type),
        amend_time=values(amend_time)
    </insert>

    <!-- 根据条件查询去重后的数据（按指定字段分组，取每组中ID最小的记录） -->
    <select id="getDedupList2MatchDataBasic" resultType="tw.org.must.must.model.new_list.List2MatchDataBasic">
        SELECT *
        FROM list2_match_data_basic
        WHERE id IN (
            SELECT MIN(id)
            FROM list2_match_data_basic
            WHERE file_base_id IN
            <foreach collection="fileBaseIds" item="fileBaseId" open="(" close=")" separator=",">
                #{fileBaseId}
            </foreach>
            <if test="status != null">
                AND status = #{status}
            </if>
            GROUP BY
            <trim prefixOverrides=",">
                <if test="groupByUniqueKeyMd5">
                    unique_key_md5
                </if>
                <if test="groupByTitleArtistCname">
                    <if test="groupByUniqueKeyMd5">, </if>
                    title, artists, CONCAT(IFNULL(authors, ''), IFNULL(composers, ''))
                </if>
                <if test="groupByTitleArtistCnameDuration">
                    <if test="groupByUniqueKeyMd5 or groupByTitleArtistCname">, </if>
                    title, artists, CONCAT(IFNULL(authors, ''), IFNULL(composers, '')), duration_m, duration_s
                </if>
            </trim>
        )
        ORDER BY id ASC
    </select>

    <!-- 分批查询P2类型匹配数据（用于性能优化） -->
    <select id="getBatchList2MatchDataBasic" resultType="tw.org.must.must.model.new_list.List2MatchDataBasic">
        SELECT
            upload_type,
            category_code,
            perform_time,
            title,
            artists,
            authors,
            duration_m,
            duration_s,
            click_number,
            ext_json,
            id
        FROM list2_match_data_basic
        WHERE file_base_id IN
        <foreach collection="fileBaseIds" item="fileBaseId" open="(" close=")" separator=",">
            #{fileBaseId}
        </foreach>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="startId != null">
            AND id > #{startId}
        </if>
        ORDER BY id ASC
        LIMIT #{batchSize}
    </select>
</mapper>