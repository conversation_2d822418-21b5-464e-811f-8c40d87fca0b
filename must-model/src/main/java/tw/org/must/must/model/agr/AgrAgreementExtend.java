package tw.org.must.must.model.agr;

import java.text.MessageFormat;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.apache.commons.lang3.StringUtils;

import tw.org.must.must.common.base.BaseEntity;
import tw.org.must.must.common.constants.Constants;

@Table(name = "`agr_agreement_extend`")
public class AgrAgreementExtend extends BaseEntity {

    /**
     * 合约编号
     */
    @Column(name = "agr_no")
    private String agrNo;

    /**
     * 
     */
    @Column(name = "worksnum")
    private Long worksnum;

    /**
     * 
     */
    @Column(name = "worknum_society")
    private Integer worknumSociety;
    
    
    
    
    @Column(name = "work_unique_key")
    private String workUniqueKey;

    /**
     * 
     */
    @Column(name = "ip_base_no")
    private String ipBaseNo;

    /**
     * 
     */
    @Column(name = "ip_name_no")
    private String ipNameNo;

    /**
     * Work Genre Detail
     */
    @Column(name = "genre_detail")
    private String genreDetail;

    /**
     * 语言
     */
    @Column(name = "language_code")
    private String languageCode;

    /**
     * 
     */
    @Column(name = "work_writer_role")
    private String workWriterRole;

    /**
     * 
     */
    @Column(name = "indicator")
    private String indicator;

    /**
     * 
     */
    @Column(name = "per")
    private String per;

    /**
     * 
     */
    @Column(name = "mec")
    private String mec;

    /**
     * 
     */
    @Column(name = "zyn")
    private String zyn;

    /**
     * 
     */
    @Column(name = "ori_valid_to")
    private Date oriValidTo;

    /**
     * 
     */
    @Column(name = "cov_valid_to")
    private Date covValidTo;

    /**
     * 
     */
    @Column(name = "relate_agr_to")
    private String relateAgrTo;

    /**
     * 
     */
    @Column(name = "input_soc")
    private Integer inputSoc;

    /**
     * 
     */
    @Column(name = "sd")
    private String sd;

    /**
     * 
     */
    @Column(name = "od")
    private String od;

    /**
     * 
     */
    @Column(name = "db")
    private String db;
    
    
  	@Transient
    private String workTitle;
    
    @Transient
	private String ipName;
    
    @Transient
    private String ipChineseName;
    
    
    /**
     * 前端提交的操作标识，ADD\UPDATE\DELETE
     */
    @Transient
    private String action;
    
    
    

    
    
    public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getIpChineseName() {
		return ipChineseName;
	}

	public void setIpChineseName(String ipChineseName) {
		this.ipChineseName = ipChineseName;
	}

	public String getWorkTitle() {
		return workTitle;
	}

	public void setWorkTitle(String workTitle) {
		this.workTitle = workTitle;
	}

	public String getIpName() {
		return ipName;
	}

	public void setIpName(String ipName) {
		this.ipName = ipName;
	}

  
	public String getAgrNo() {
        return agrNo;
    }

    public void setAgrNo(String agrNo) {
        this.agrNo = agrNo;
    }

    public Long getWorksnum() {
        return worksnum;
    }

    public void setWorksnum(Long worksnum) {
        this.worksnum = worksnum;
    }

    public Integer getWorknumSociety() {
        return worknumSociety;
    }

    public void setWorknumSociety(Integer worknumSociety) {
        this.worknumSociety = worknumSociety;
    }

    public String getIpBaseNo() {
        return ipBaseNo;
    }

    public void setIpBaseNo(String ipBaseNo) {
        this.ipBaseNo = ipBaseNo;
    }

    public String getIpNameNo() {
        return ipNameNo;
    }

    public void setIpNameNo(String ipNameNo) {
        this.ipNameNo = ipNameNo;
    }


    public String getGenreDetail() {
		return genreDetail;
	}

	public void setGenreDetail(String genreDetail) {
		this.genreDetail = genreDetail;
	}

	public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public String getWorkWriterRole() {
        return workWriterRole;
    }

    public void setWorkWriterRole(String workWriterRole) {
        this.workWriterRole = workWriterRole;
    }

    public String getIndicator() {
        return indicator;
    }

    public void setIndicator(String indicator) {
        this.indicator = indicator;
    }

    public String getPer() {
        return per;
    }

    public void setPer(String per) {
        this.per = per;
    }

    public String getMec() {
        return mec;
    }

    public void setMec(String mec) {
        this.mec = mec;
    }

    public String getZyn() {
        return zyn;
    }

    public void setZyn(String zyn) {
        this.zyn = zyn;
    }

    public Date getOriValidTo() {
        return oriValidTo;
    }

    public void setOriValidTo(Date oriValidTo) {
        this.oriValidTo = oriValidTo;
    }

    public Date getCovValidTo() {
        return covValidTo;
    }

    public void setCovValidTo(Date covValidTo) {
        this.covValidTo = covValidTo;
    }

    public String getRelateAgrTo() {
        return relateAgrTo;
    }

    public void setRelateAgrTo(String relateAgrTo) {
        this.relateAgrTo = relateAgrTo;
    }

    public Integer getInputSoc() {
        return inputSoc;
    }

    public void setInputSoc(Integer inputSoc) {
        this.inputSoc = inputSoc;
    }

    public String getSd() {
        return sd;
    }

    public void setSd(String sd) {
        this.sd = sd;
    }

    public String getOd() {
        return od;
    }

    public void setOd(String od) {
        this.od = od;
    }

    public String getDb() {
        return db;
    }

    public void setDb(String db) {
        this.db = db;
    }

	public String getWorkUniqueKey() {
		if(workUniqueKey == null || workUniqueKey.isEmpty()) {
			if (worksnum !=null && worknumSociety != null && !worksnum.equals(0L) && !worknumSociety.equals(0)) {
				
				workUniqueKey = Constants.getWorkUniqueKey(worknumSociety, worksnum);
			}
		}
	
		
		return workUniqueKey;
	}

	public void setWorkUniqueKey(String workUniqueKey) {
		this.workUniqueKey = workUniqueKey;
	}

    


}