package tw.org.must.must.model.agr;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;

import tw.org.must.must.common.base.BaseEntity;
import tw.org.must.must.model.util.UniqUtils;
import tw.org.must.must.model.util.UniqUtils.ModelUniqSets;

@Table(name = "`agr_content`")
public class AgrContent extends BaseEntity {
	
	public AgrContent() {}

    /**
     * 合约编号
     */
    @Column(name = "agr_no")
    private String agrNo;

    /**
     * 合约类别 PG\PS\OG\OS
     */
    @Column(name = "agr_type")
    private String agrType;

    /**
     * 合约内容类别id，对应ref_agr_type表的值
     */
    @Column(name = "agr_type_id")
    private Integer agrTypeId;

    /**
     * pef 比例
     */
    @Column(name = "oip_pshare")
    private BigDecimal oipPshare;
    public BigDecimal getOipPshare() { return oipPshare; }
	public void setOipPshare(BigDecimal oipPshare) { this.oipPshare = UniqUtils.uniqShareO(oipPshare); }

    /**
     * mec比例
     */
    @Column(name = "oip_mshare")
    private BigDecimal oipMshare;
	public BigDecimal getOipMshare() { return oipMshare; }
	public void setOipMshare(BigDecimal oipMshare) { this.oipMshare = UniqUtils.uniqShareO(oipMshare); }

    /**
     * syn比例
     */
    @Column(name = "oip_sshare")
    private BigDecimal oipSshare;
	public BigDecimal getOipSshare() { return oipSshare; }
	public void setOipSshare(BigDecimal oipSshare) { this.oipSshare = UniqUtils.uniqShareO(oipSshare); }

    /**
     * 
     */
    @Column(name = "oip_odshare")
    private BigDecimal oipOdshare;
	public BigDecimal getOipOdshare() { return oipOdshare; }
	public void setOipOdshare(BigDecimal oipOdshare) { this.oipOdshare = UniqUtils.uniqShareO(oipOdshare); }

    /**
     * 合约起始时间
     */
    @Column(name = "agr_sdate")
    private Date agrSdate;

    /**
     * 合约结束时间
     */
    @Column(name = "agr_edate")
    private Date agrEdate;

    /**
     * 自动续约
     */
    @Column(name = "auto_extension_ind")
    private String autoExtensionInd;
    public String getAutoExtensionInd() { return autoExtensionInd; }
    // We uniquify the field value in the setter since ibatis calls the setter when deserializing from the JDBC ResultSet
    public void setAutoExtensionInd(String autoExtensionInd) { this.autoExtensionInd = UniqUtils.uniqYnO(autoExtensionInd); }

    /**
     * 是否过期，Y  N
     */
    @Column(name = "expired")
    private String expired;
    public String getExpired() { return expired; }
    public void setExpired(String expired) { this.expired = UniqUtils.uniqYnO(expired); }

    /**
     * 是否有争议 ，Y N
     */
    @Column(name = "sd")
    private String sd;
    public String getSd() { return sd; }
    public void setSd(String sd) { this.sd = UniqUtils.uniqYnO(sd); }

    /**
     * 
     */
    @Column(name = "direct_deal")
    private String directDeal;
    public String getDirectDeal() { return directDeal; }
    public void setDirectDeal(String directDeal) { this.directDeal = UniqUtils.uniqYnO(directDeal); }

    /**
     * 
     */
    @Column(name = "pre_term")
    private String preTerm;
    public String getPreTerm() { return preTerm; }
    public void setPreTerm(String preTerm) { this.preTerm = UniqUtils.uniqYnO(preTerm); }

    /**
     * 
     */
    @Column(name = "sm_clause")
    private String smClause;

    /**
     * 
     */
    @Column(name = "advance_paid")
    private String advancePaid;
    public String getAdvancePaid() { return advancePaid; }
    public void setAdvancePaid(String advancePaid) { this.advancePaid = UniqUtils.uniqYnO(advancePaid); }

    /**
     * 档案编号，一般指向原始合约的合约编号
     */
    @Column(name = "doc_no")
    private String docNo;

    /**
     * 
     */
    @Column(name = "psac")
    private String psac;

    /**
     * 国际标准协议代码
     */
    @Column(name = "isac")
    private String isac;

    /**
     * TS表示总合约比例，SA表示部分合约比例
     */
    @Column(name = "proportion_ind")
    private String proportionInd;

    /**
     * 
     */
    @Column(name = "remain_share_payable_ind")
    private String remainSharePayableInd;

    /**
     * 
     */
    @Column(name = "ori_retention_period")
    private Integer oriRetentionPeriod;

    /**
     * 
     */
    @Column(name = "ori_retention_edate")
    private Date oriRetentionEdate;

    /**
     * 
     */
    @Column(name = "ori_post_term_period")
    private Integer oriPostTermPeriod;

    /**
     * 
     */
    @Column(name = "ori_post_term_date")
    private Date oriPostTermDate;

    /**
     * 
     */
    @Column(name = "cov_retention_period")
    private Integer covRetentionPeriod;

    /**
     * 
     */
    @Column(name = "cov_retention_edate")
    private Date covRetentionEdate;

    /**
     * 
     */
    @Column(name = "cov_post_term_period")
    private Integer covPostTermPeriod;

    /**
     * 
     */
    @Column(name = "cov_post_term_date")
    private Date covPostTermDate;

    /**
     * 
     */
    @Column(name = "further_assign")
    private String furtherAssign;

    /**
     * 合约是否有收到
     */
    @Column(name = "agr_received")
    private String agrReceived;

    /**
     * 
     */
    @Column(name = "user_id")
    private Long userId;
    
    @Column(name = "user_name")
    private String userName;

    /**
     * 
     */
    @Column(name = "dx_amend_time")
    private Date dxAmendTime;

    /**
     * 
     */
    @Column(name = "inv_wrk_link")
    private String invWrkLink;

    /**
     * 自动生成op：Writer Agreement apply only
     */
    @Column(name = "auto_gen_op")
    private String autoGenOp;

    /**
     * 
     */
    @Column(name = "owner_task_id")
    private Integer ownerTaskId;

    /**
     * 
     */
    @Column(name = "oip_dbshare")
    private BigDecimal oipDbshare;
    public BigDecimal getOipDbshare() { return oipDbshare; }
	public void setOipDbshare(BigDecimal oipDbshare) { this.oipDbshare = UniqUtils.uniqShareO(oipDbshare); }

    /**
     * 合约签订日期
     */
    @Column(name = "agr_date")
    private Date agrDate;
    
    @Column(name = "overlap")
    private String overlap;

    @Column(name = "oracle_amend_time")
    private Date oracleAmendTime ;
    
	// 计算比例使用月份
	@Transient
	private Double month;
	
	//合约编号校验死循环
	@Transient
	private String agrNoStr;
    

    public String getAgrNo() {
        return agrNo;
    }

    public void setAgrNo(String agrNo) {
        this.agrNo = agrNo;
    }

    public String getAgrType() {
        return agrType;
    }

    public void setAgrType(String agrType) {
        this.agrType = agrType;
    }

    public Integer getAgrTypeId() {
        return agrTypeId;
    }

    public void setAgrTypeId(Integer agrTypeId) {
        this.agrTypeId = agrTypeId;
    }

	public Date getAgrSdate() {
        return agrSdate;
    }

    public void setAgrSdate(Date agrSdate) {
        this.agrSdate = agrSdate;
    }

    public Date getAgrEdate() {
        return agrEdate;
    }

    public void setAgrEdate(Date agrEdate) {
        this.agrEdate = agrEdate;
    }

    public String getSmClause() {
        return smClause;
    }

    public void setSmClause(String smClause) {
        this.smClause = smClause;
    }

    public String getDocNo() {
		return docNo;
	}

	public void setDocNo(String docNo) {
		this.docNo = docNo;
	}

	public String getPsac() {
        return psac;
    }

    public void setPsac(String psac) {
        this.psac = psac;
    }

    public String getIsac() {
        return isac;
    }

    public void setIsac(String isac) {
        this.isac = isac;
    }

    public String getProportionInd() {
        return proportionInd;
    }

    public void setProportionInd(String proportionInd) {
        this.proportionInd = proportionInd;
    }

    public String getRemainSharePayableInd() {
        return remainSharePayableInd;
    }

    public void setRemainSharePayableInd(String remainSharePayableInd) {
        this.remainSharePayableInd = remainSharePayableInd;
    }

    public Integer getOriRetentionPeriod() {
        return oriRetentionPeriod;
    }

    public void setOriRetentionPeriod(Integer oriRetentionPeriod) {
        this.oriRetentionPeriod = oriRetentionPeriod;
    }

    public Date getOriRetentionEdate() {
        return oriRetentionEdate;
    }

    public void setOriRetentionEdate(Date oriRetentionEdate) {
        this.oriRetentionEdate = oriRetentionEdate;
    }

    public Integer getOriPostTermPeriod() {
        return oriPostTermPeriod;
    }

    public void setOriPostTermPeriod(Integer oriPostTermPeriod) {
        this.oriPostTermPeriod = oriPostTermPeriod;
    }

    public Date getOriPostTermDate() {
        return oriPostTermDate;
    }

    public void setOriPostTermDate(Date oriPostTermDate) {
        this.oriPostTermDate = oriPostTermDate;
    }

    public Integer getCovRetentionPeriod() {
        return covRetentionPeriod;
    }

    public void setCovRetentionPeriod(Integer covRetentionPeriod) {
        this.covRetentionPeriod = covRetentionPeriod;
    }

    public Date getCovRetentionEdate() {
        return covRetentionEdate;
    }

    public void setCovRetentionEdate(Date covRetentionEdate) {
        this.covRetentionEdate = covRetentionEdate;
    }

    public Integer getCovPostTermPeriod() {
        return covPostTermPeriod;
    }

    public void setCovPostTermPeriod(Integer covPostTermPeriod) {
        this.covPostTermPeriod = covPostTermPeriod;
    }

    public Date getCovPostTermDate() {
        return covPostTermDate;
    }

    public void setCovPostTermDate(Date covPostTermDate) {
        this.covPostTermDate = covPostTermDate;
    }

    public String getFurtherAssign() {
        return furtherAssign;
    }

    public void setFurtherAssign(String furtherAssign) {
        this.furtherAssign = furtherAssign;
    }

    public String getAgrReceived() {
        return agrReceived;
    }

    public void setAgrReceived(String agrReceived) {
        this.agrReceived = agrReceived;
    }

 

    public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Date getDxAmendTime() {
        return dxAmendTime;
    }

    public void setDxAmendTime(Date dxAmendTime) {
        this.dxAmendTime = dxAmendTime;
    }

    public String getInvWrkLink() {
        return invWrkLink;
    }

    public void setInvWrkLink(String invWrkLink) {
        this.invWrkLink = invWrkLink;
    }

    public String getAutoGenOp() {
        return autoGenOp;
    }

    public void setAutoGenOp(String autoGenOp) {
        this.autoGenOp = autoGenOp;
    }

    public Integer getOwnerTaskId() {
        return ownerTaskId;
    }

    public void setOwnerTaskId(Integer ownerTaskId) {
        this.ownerTaskId = ownerTaskId;
    }

	public Date getAgrDate() {
        return agrDate;
    }

    public void setAgrDate(Date agrDate) {
        this.agrDate = agrDate;
    }

	public String getOverlap() {
		return overlap;
	}

	public void setOverlap(String overlap) {
		this.overlap = overlap;
	}

	public Double getMonth() {
		return month;
	}

	public void setMonth(Double month) {
		this.month = month;
	}

	public String getAgrNoStr() {
		return agrNoStr;
	}

	public void setAgrNoStr(String agrNoStr) {
		this.agrNoStr = agrNoStr;
	}

    public Date getOracleAmendTime() {
        return oracleAmendTime;
    }

    public void setOracleAmendTime(Date oracleAmendTime) {
        this.oracleAmendTime = oracleAmendTime;
    }
}