package tw.org.must.must.model.crd;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.util.Date;

@Table(name = "`CRD2_AHP_HDR`")
public class Crd2AhpHdr extends CrdBaseEntity {

    /**
     * 
     */
    @Column(name = "`group_id`")
    private Integer groupId;

    /**
     * 
     */
    @Column(name = "`transaction_seq`")
    private Integer transactionSeq;

    /**
     * 
     */
    @Column(name = "`record_seq`")
    private Integer recordSeq;

    /**
     * 
     */
    @Column(name = "`ip_no`")
    private String ipNo;

    /**
     * 
     */
    @Column(name = "`ip_soc`")
    private String ipSoc;

    /**
     * 
     */
    @Column(name = "`currency`")
    private String currency;

    /**
     * 
     */
    @Column(name = "`payment_amount`")
    private Long paymentAmount;

    /**
     * 
     */
    @Column(name = "`payment_sign`")
    private String paymentSign;

    /**
     * 
     */
    @Column(name = "`tax_rate`")
    private Integer taxRate;

    /**
     * 
     */
    @Column(name = "`tax_amount`")
    private Long taxAmount;

    /**
     * 
     */
    @Column(name = "`commission_amount`")
    private Long commissionAmount;

    /**
     * 
     */
    @Column(name = "`remit_royalty_amount`")
    private Long remitRoyaltyAmount;

    /**
     * 
     */
    @Column(name = "`comments`")
    private String comments;

    /**
     * 
     */

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getTransactionSeq() {
        return transactionSeq;
    }

    public void setTransactionSeq(Integer transactionSeq) {
        this.transactionSeq = transactionSeq;
    }

    public Integer getRecordSeq() {
        return recordSeq;
    }

    public void setRecordSeq(Integer recordSeq) {
        this.recordSeq = recordSeq;
    }

    public String getIpNo() {
        return ipNo;
    }

    public void setIpNo(String ipNo) {
        this.ipNo = ipNo;
    }

    public String getIpSoc() {
        return ipSoc;
    }

    public void setIpSoc(String ipSoc) {
        this.ipSoc = ipSoc;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Long getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(Long paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getPaymentSign() {
        return paymentSign;
    }

    public void setPaymentSign(String paymentSign) {
        this.paymentSign = paymentSign;
    }

    public Integer getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(Integer taxRate) {
        this.taxRate = taxRate;
    }

    public Long getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(Long taxAmount) {
        this.taxAmount = taxAmount;
    }

    public Long getCommissionAmount() {
        return commissionAmount;
    }

    public void setCommissionAmount(Long commissionAmount) {
        this.commissionAmount = commissionAmount;
    }

    public Long getRemitRoyaltyAmount() {
        return remitRoyaltyAmount;
    }

    public void setRemitRoyaltyAmount(Long remitRoyaltyAmount) {
        this.remitRoyaltyAmount = remitRoyaltyAmount;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }


}