package tw.org.must.must.model.crd;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`CRD2_DIST_MWN_DTL_MDT`")
public class Crd2DistMwnDtlMdt extends BaseEntity {

    /**
     * list_overseas_file_base.id
     */
    @Column(name = "`file_id`")
    private Long fileId;

    /**
     * 
     */
    @Column(name = "`group_id`")
    private Integer groupId;

    /**
     * MDT
     */
    @Column(name = "`record_type`")
    private String recordType;

    /**
     * 
     */
    @Column(name = "`transaction_seq`")
    private Integer transactionSeq;

    /**
     * 
     */
    @Column(name = "`record_seq`")
    private Integer recordSeq;

    /**
     * 
     */
    @Column(name = "`territory_code`")
    private String territoryCode;

    /**
     * 
     */
    @Column(name = "`territory_code_from`")
    private String territoryCodeFrom;

    /**
     * 
     */
    @Column(name = "`territory_tisan`")
    private String territoryTisan;

    /**
     * 
     */
    @Column(name = "`territory_tisan_from`")
    private String territoryTisanFrom;

    /**
     * 
     */
    @Column(name = "`indicator`")
    private String indicator;

    /**
     * 
     */
    @Column(name = "`file_line_no`")
    private Integer fileLineNo;

    /**
     * 
     */
    @Column(name = "`file_line`")
    private String fileLine;

    /**
     * 
     */
    @Column(name = "`last_export_time`")
    private Date lastExportTime;

    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public Integer getTransactionSeq() {
        return transactionSeq;
    }

    public void setTransactionSeq(Integer transactionSeq) {
        this.transactionSeq = transactionSeq;
    }

    public Integer getRecordSeq() {
        return recordSeq;
    }

    public void setRecordSeq(Integer recordSeq) {
        this.recordSeq = recordSeq;
    }

    public String getTerritoryCode() {
        return territoryCode;
    }

    public void setTerritoryCode(String territoryCode) {
        this.territoryCode = territoryCode;
    }

    public String getTerritoryCodeFrom() {
        return territoryCodeFrom;
    }

    public void setTerritoryCodeFrom(String territoryCodeFrom) {
        this.territoryCodeFrom = territoryCodeFrom;
    }

    public String getTerritoryTisan() {
        return territoryTisan;
    }

    public void setTerritoryTisan(String territoryTisan) {
        this.territoryTisan = territoryTisan;
    }

    public String getTerritoryTisanFrom() {
        return territoryTisanFrom;
    }

    public void setTerritoryTisanFrom(String territoryTisanFrom) {
        this.territoryTisanFrom = territoryTisanFrom;
    }

    public String getIndicator() {
        return indicator;
    }

    public void setIndicator(String indicator) {
        this.indicator = indicator;
    }

    public Integer getFileLineNo() {
        return fileLineNo;
    }

    public void setFileLineNo(Integer fileLineNo) {
        this.fileLineNo = fileLineNo;
    }

    public String getFileLine() {
        return fileLine;
    }

    public void setFileLine(String fileLine) {
        this.fileLine = fileLine;
    }

    public Date getLastExportTime() {
        return lastExportTime;
    }

    public void setLastExportTime(Date lastExportTime) {
        this.lastExportTime = lastExportTime;
    }



}