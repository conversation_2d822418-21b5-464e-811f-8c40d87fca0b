package tw.org.must.must.model.crd;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`CRD2_SDN_HDR`")
public class Crd2SdnHdr extends CrdBaseEntity {

    /**
     * CRD2_CTL_GRH.id
     */
    @Column(name = "`group_id`")
    private Integer groupId;

    /**
     * 
     */
    @Column(name = "`transaction_seq`")
    private Integer transactionSeq;

    /**
     * 
     */
    @Column(name = "`record_seq`")
    private Integer recordSeq;

    /**
     * 
     */
    @Column(name = "`remit_soc_dist_id`")
    private String remitSocDistId;

    /**
     * PER,MEC,SYN
     */
    @Column(name = "`right_type`")
    private String rightType;

    /**
     * 
     */
    @Column(name = "`account_period_start`")
    private String accountPeriodStart;

    /**
     * 
     */
    @Column(name = "`account_period_end`")
    private String accountPeriodEnd;

    /**
     * Society number, or publisher IP Name Number
     */
    @Column(name = "`recipient_code`")
    private Long recipientCode;

    /**
     * 
     */
    @Column(name = "`recipient_name`")
    private String recipientName;

    /**
     * 
     */
    @Column(name = "`bank_pay_date`")
    private String bankPayDate;

    /**
     * 
     */
    @Column(name = "`amount_decimal_place`")
    private Integer amountDecimalPlace;

    /**
     * 
     */
    @Column(name = "`percent_decimal_place`")
    private Integer percentDecimalPlace;

    /**
     * 
     */
    @Column(name = "`version_no`")
    private String versionNo;

    /**
     * 
     */
    @Column(name = "`remit_currency`")
    private String remitCurrency;

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getTransactionSeq() {
        return transactionSeq;
    }

    public void setTransactionSeq(Integer transactionSeq) {
        this.transactionSeq = transactionSeq;
    }

    public Integer getRecordSeq() {
        return recordSeq;
    }

    public void setRecordSeq(Integer recordSeq) {
        this.recordSeq = recordSeq;
    }

    public String getRemitSocDistId() {
        return remitSocDistId;
    }

    public void setRemitSocDistId(String remitSocDistId) {
        this.remitSocDistId = remitSocDistId;
    }

    public String getRightType() {
        return rightType;
    }

    public void setRightType(String rightType) {
        this.rightType = rightType;
    }

    public String getAccountPeriodStart() {
        return accountPeriodStart;
    }

    public void setAccountPeriodStart(String accountPeriodStart) {
        this.accountPeriodStart = accountPeriodStart;
    }

    public String getAccountPeriodEnd() {
        return accountPeriodEnd;
    }

    public void setAccountPeriodEnd(String accountPeriodEnd) {
        this.accountPeriodEnd = accountPeriodEnd;
    }

    public Long getRecipientCode() {
        return recipientCode;
    }

    public void setRecipientCode(Long recipientCode) {
        this.recipientCode = recipientCode;
    }

    public String getRecipientName() {
        return recipientName;
    }

    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }

    public String getBankPayDate() {
        return bankPayDate;
    }

    public void setBankPayDate(String bankPayDate) {
        this.bankPayDate = bankPayDate;
    }

    public Integer getAmountDecimalPlace() {
        return amountDecimalPlace;
    }

    public void setAmountDecimalPlace(Integer amountDecimalPlace) {
        this.amountDecimalPlace = amountDecimalPlace;
    }

    public Integer getPercentDecimalPlace() {
        return percentDecimalPlace;
    }

    public void setPercentDecimalPlace(Integer percentDecimalPlace) {
        this.percentDecimalPlace = percentDecimalPlace;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getRemitCurrency() {
        return remitCurrency;
    }

    public void setRemitCurrency(String remitCurrency) {
        this.remitCurrency = remitCurrency;
    }
}