package tw.org.must.must.model.crd;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`CRD3_ACK_DTL_SPW`")
public class Crd3AckDtlSpw extends CrdBaseEntity {

    /**
     * 
     */
    @Column(name = "`group_id`")
    private Integer groupId;

    /**
     * 
     */
    @Column(name = "`transaction_seq`")
    private Integer transactionSeq;

    /**
     * 
     */
    @Column(name = "`record_seq`")
    private Integer recordSeq;

    /**
     * 
     */
    @Column(name = "`soc_work_id`")
    private String socWorkId;

    /**
     * 
     */
    @Column(name = "`iswc`")
    private String iswc;

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getTransactionSeq() {
        return transactionSeq;
    }

    public void setTransactionSeq(Integer transactionSeq) {
        this.transactionSeq = transactionSeq;
    }

    public Integer getRecordSeq() {
        return recordSeq;
    }

    public void setRecordSeq(Integer recordSeq) {
        this.recordSeq = recordSeq;
    }

    public String getSocWorkId() {
        return socWorkId;
    }

    public void setSocWorkId(String socWorkId) {
        this.socWorkId = socWorkId;
    }

    public String getIswc() {
        return iswc;
    }

    public void setIswc(String iswc) {
        this.iswc = iswc;
    }
}