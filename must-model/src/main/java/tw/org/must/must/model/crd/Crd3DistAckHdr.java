package tw.org.must.must.model.crd;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`CRD3_DIST_ACK_HDR`")
public class Crd3DistAckHdr extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`file_id`")
    private Integer fileId;

    /**
     * 
     */
    @Column(name = "`group_id`")
    private Integer groupId;

    /**
     * 
     */
    @Column(name = "`record_type`")
    private String recordType;

    /**
     * 
     */
    @Column(name = "`transaction_seq`")
    private Integer transactionSeq;

    /**
     * 
     */
    @Column(name = "`record_seq`")
    private Integer recordSeq;

    /**
     * 
     */
    @Column(name = "`remit_soc_dist_id`")
    private String remitSocDistId;

    /**
     * 
     */
    @Column(name = "`account_period_start`")
    private String accountPeriodStart;

    /**
     * 
     */
    @Column(name = "`account_period_end`")
    private String accountPeriodEnd;

    /**
     * 
     */
    @Column(name = "`filename`")
    private String filename;

    /**
     * 
     */
    @Column(name = "`file_line_no`")
    private Integer fileLineNo;

    /**
     * 
     */
    @Column(name = "`file_line`")
    private String fileLine;

    /**
     * 
     */
    @Column(name = "`last_export_time`")
    private Date lastExportTime;

    public Integer getFileId() {
        return fileId;
    }

    public void setFileId(Integer fileId) {
        this.fileId = fileId;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public Integer getTransactionSeq() {
        return transactionSeq;
    }

    public void setTransactionSeq(Integer transactionSeq) {
        this.transactionSeq = transactionSeq;
    }

    public Integer getRecordSeq() {
        return recordSeq;
    }

    public void setRecordSeq(Integer recordSeq) {
        this.recordSeq = recordSeq;
    }

    public String getRemitSocDistId() {
        return remitSocDistId;
    }

    public void setRemitSocDistId(String remitSocDistId) {
        this.remitSocDistId = remitSocDistId;
    }

    public String getAccountPeriodStart() {
        return accountPeriodStart;
    }

    public void setAccountPeriodStart(String accountPeriodStart) {
        this.accountPeriodStart = accountPeriodStart;
    }

    public String getAccountPeriodEnd() {
        return accountPeriodEnd;
    }

    public void setAccountPeriodEnd(String accountPeriodEnd) {
        this.accountPeriodEnd = accountPeriodEnd;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public Integer getFileLineNo() {
        return fileLineNo;
    }

    public void setFileLineNo(Integer fileLineNo) {
        this.fileLineNo = fileLineNo;
    }

    public String getFileLine() {
        return fileLine;
    }

    public void setFileLine(String fileLine) {
        this.fileLine = fileLine;
    }

    public Date getLastExportTime() {
        return lastExportTime;
    }

    public void setLastExportTime(Date lastExportTime) {
        this.lastExportTime = lastExportTime;
    }



}