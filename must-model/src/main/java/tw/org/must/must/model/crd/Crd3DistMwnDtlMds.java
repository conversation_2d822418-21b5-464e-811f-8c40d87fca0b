package tw.org.must.must.model.crd;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`CRD3_DIST_MWN_DTL_MDS`")
public class Crd3DistMwnDtlMds extends BaseEntity {

    /**
     * list_overseas_file_base.id
     */
    @Column(name = "`file_id`")
    private Integer fileId;

    /**
     * 
     */
    @Column(name = "`group_id`")
    private Integer groupId;

    /**
     * MDS
     */
    @Column(name = "`record_type`")
    private String recordType;

    /**
     * 
     */
    @Column(name = "`transaction_seq`")
    private Integer transactionSeq;

    /**
     * 
     */
    @Column(name = "`record_seq`")
    private Integer recordSeq;

    /**
     * 
     */
    @Column(name = "`mds_id`")
    private String mdsId;

    /**
     * Indicates the presence of interested parties records in the musical work. 1=All right owners are individually identified, 2=Some, but not all, right owners are individually identified. 0 =Unknown.
     */
    @Column(name = "`share_type`")
    private Integer shareType;

    /**
     * 
     */
    @Column(name = "`pay_rule_indicator`")
    private String payRuleIndicator;

    /**
     * 
     */
    @Column(name = "`work_status_start`")
    private String workStatusStart;

    /**
     * 
     */
    @Column(name = "`work_status_end`")
    private String workStatusEnd;

    /**
     * 
     */
    @Column(name = "`file_line_no`")
    private Integer fileLineNo;

    /**
     * 
     */
    @Column(name = "`file_line`")
    private String fileLine;

    /**
     * 
     */
    @Column(name = "`last_export_time`")
    private Date lastExportTime;

    public Integer getFileId() {
        return fileId;
    }

    public void setFileId(Integer fileId) {
        this.fileId = fileId;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public Integer getTransactionSeq() {
        return transactionSeq;
    }

    public void setTransactionSeq(Integer transactionSeq) {
        this.transactionSeq = transactionSeq;
    }

    public Integer getRecordSeq() {
        return recordSeq;
    }

    public void setRecordSeq(Integer recordSeq) {
        this.recordSeq = recordSeq;
    }

    public String getMdsId() {
        return mdsId;
    }

    public void setMdsId(String mdsId) {
        this.mdsId = mdsId;
    }

    public Integer getShareType() {
        return shareType;
    }

    public void setShareType(Integer shareType) {
        this.shareType = shareType;
    }

    public String getPayRuleIndicator() {
        return payRuleIndicator;
    }

    public void setPayRuleIndicator(String payRuleIndicator) {
        this.payRuleIndicator = payRuleIndicator;
    }

    public String getWorkStatusStart() {
        return workStatusStart;
    }

    public void setWorkStatusStart(String workStatusStart) {
        this.workStatusStart = workStatusStart;
    }

    public String getWorkStatusEnd() {
        return workStatusEnd;
    }

    public void setWorkStatusEnd(String workStatusEnd) {
        this.workStatusEnd = workStatusEnd;
    }

    public Integer getFileLineNo() {
        return fileLineNo;
    }

    public void setFileLineNo(Integer fileLineNo) {
        this.fileLineNo = fileLineNo;
    }

    public String getFileLine() {
        return fileLine;
    }

    public void setFileLine(String fileLine) {
        this.fileLine = fileLine;
    }

    public Date getLastExportTime() {
        return lastExportTime;
    }

    public void setLastExportTime(Date lastExportTime) {
        this.lastExportTime = lastExportTime;
    }



}