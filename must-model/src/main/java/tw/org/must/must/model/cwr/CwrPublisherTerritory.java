package tw.org.must.must.model.cwr;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`cwr_publisher_territory`")
public class CwrPublisherTerritory extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`work_no`")
    private String workNo;

    /**
     * 
     */
    @Column(name = "`soc_code`")
    private String socCode;

    /**
     * 
     */
    @Column(name = "`interested_party_no`")
    private String interestedPartyNo;

    /**
     * 
     */
    @Column(name = "`pr_share`")
    private Integer prShare;

    /**
     * 
     */
    @Column(name = "`mr_share`")
    private Integer mrShare;

    /**
     * 
     */
    @Column(name = "`sr_share`")
    private Integer srShare;

    /**
     * 
     */
    @Column(name = "`in_or_ex`")
    private String inOrEx;

    /**
     * 
     */
    @Column(name = "`tis_code`")
    private String tisCode;

    /**
     * 
     */
    @Column(name = "`shares_change`")
    private String sharesChange;

    /**
     * 
     */
    @Column(name = "`sequence_no`")
    private Integer sequenceNo;

    /**
     * 0:待转换，1已转换
     */
    @Column(name = "`status`")
    private Integer status;

    @Column(name="f_id")
    private Long fId;

    public Long getfId() {
        return fId;
    }

    public void setfId(Long fId) {
        this.fId = fId;
    }

    public String getWorkNo() {
        return workNo;
    }

    public void setWorkNo(String workNo) {
        this.workNo = workNo;
    }

    public String getSocCode() {
        return socCode;
    }

    public void setSocCode(String socCode) {
        this.socCode = socCode;
    }

    public String getInterestedPartyNo() {
        return interestedPartyNo;
    }

    public void setInterestedPartyNo(String interestedPartyNo) {
        this.interestedPartyNo = interestedPartyNo;
    }

    public Integer getPrShare() {
        return prShare;
    }

    public void setPrShare(Integer prShare) {
        this.prShare = prShare;
    }

    public Integer getMrShare() {
        return mrShare;
    }

    public void setMrShare(Integer mrShare) {
        this.mrShare = mrShare;
    }

    public Integer getSrShare() {
        return srShare;
    }

    public void setSrShare(Integer srShare) {
        this.srShare = srShare;
    }

    public String getInOrEx() {
        return inOrEx;
    }

    public void setInOrEx(String inOrEx) {
        this.inOrEx = inOrEx;
    }

    public String getTisCode() {
        return tisCode;
    }

    public void setTisCode(String tisCode) {
        this.tisCode = tisCode;
    }

    public String getSharesChange() {
        return sharesChange;
    }

    public void setSharesChange(String sharesChange) {
        this.sharesChange = sharesChange;
    }

    public Integer getSequenceNo() {
        return sequenceNo;
    }

    public void setSequenceNo(Integer sequenceNo) {
        this.sequenceNo = sequenceNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}