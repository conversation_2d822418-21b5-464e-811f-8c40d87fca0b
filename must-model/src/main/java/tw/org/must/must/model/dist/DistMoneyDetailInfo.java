package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`dist_money_detail_info`")
public class DistMoneyDetailInfo extends BaseEntity {

    /**
     * 分配金额设置id
     */
    @Column(name = "dist_money_set_info_id")
    private Long distMoneySetInfoId;

    /**
     * 分配清单id
     */
    @Column(name = "list_file_info_id")
    private Long listFileInfoId;

    /**
     * 分配清单名称
     */
    @Column(name = "list_file_name")
    private String listFileName;

    /**
     * 清单分配占比
     */
    @Column(name = "radio")
    private BigDecimal radio;

    /**
     * 总分配的钱
     */
    @Column(name = "dist_total_money")
    private BigDecimal distTotalMoney;

    /**
     * 保留款比例
     */
    @Column(name = "retention_rate")
    private BigDecimal retentionRate;

    /**
     * 保留款金额
     */
    @Column(name = "retention_money")
    private BigDecimal retentionMoney;
    
    @Column(name = "dist_type")
    private String distType;

    /**
     * 参与分配的金额
     */
    @Column(name = "disted_money")
    private BigDecimal distedMoney;

    public Long getDistMoneySetInfoId() {
        return distMoneySetInfoId;
    }

    public void setDistMoneySetInfoId(Long distMoneySetInfoId) {
        this.distMoneySetInfoId = distMoneySetInfoId;
    }

    public Long getListFileInfoId() {
        return listFileInfoId;
    }

    public void setListFileInfoId(Long listFileInfoId) {
        this.listFileInfoId = listFileInfoId;
    }

    public String getListFileName() {
        return listFileName;
    }

    public void setListFileName(String listFileName) {
        this.listFileName = listFileName;
    }

    
	public BigDecimal getRadio() {
		return radio;
	}

	public void setRadio(BigDecimal radio) {
		this.radio = radio;
	}

	public BigDecimal getDistTotalMoney() {
		return distTotalMoney;
	}

	public void setDistTotalMoney(BigDecimal distTotalMoney) {
		this.distTotalMoney = distTotalMoney;
	}

	public BigDecimal getRetentionRate() {
		return retentionRate;
	}

	public void setRetentionRate(BigDecimal retentionRate) {
		this.retentionRate = retentionRate;
	}

	public BigDecimal getRetentionMoney() {
		return retentionMoney;
	}

	public void setRetentionMoney(BigDecimal retentionMoney) {
		this.retentionMoney = retentionMoney;
	}

	public BigDecimal getDistedMoney() {
		return distedMoney;
	}

	public void setDistedMoney(BigDecimal distedMoney) {
		this.distedMoney = distedMoney;
	}

	public String getDistType() {
		return distType;
	}

	public void setDistType(String distType) {
		this.distType = distType;
	}



}