package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`dist_money_set_info`")
public class DistMoneySetInfo extends BaseEntity {

    /**
     * 
     */
    @Column(name = "list_type_info_id")
    private Long listTypeInfoId;

    /**
     * 
     */
    @Column(name = "list_name")
    private String listName;

    /**
     * 
     */
    @Column(name = "indvance")
    private String indvance;

    /**
     * 当前清单类别准备要分的钱
     */
    @Column(name = "unexpired")
    private BigDecimal unexpired;

    /**
     * 其他收入
     */
    @Column(name = "other_income")
    private BigDecimal otherIncome;

    /**
     * 当前要分的钱+other income
     */
    @Column(name = "per_other_income")
    private BigDecimal perOtherIncome;

    /**
     * 准备要分配的钱+other+income
     */
    @Column(name = "net_dist_income")
    private BigDecimal netDistIncome;

    /**
     * Y:平均分配，按照章程来分配；N:按照清单输入的价格
     */
    @Column(name = "source_distribute")
    private String sourceDistribute;

    public Long getListTypeInfoId() {
        return listTypeInfoId;
    }

    public void setListTypeInfoId(Long listTypeInfoId) {
        this.listTypeInfoId = listTypeInfoId;
    }

    public String getListName() {
        return listName;
    }

    public void setListName(String listName) {
        this.listName = listName;
    }

    public String getIndvance() {
        return indvance;
    }

    public void setIndvance(String indvance) {
        this.indvance = indvance;
    }


    public BigDecimal getUnexpired() {
		return unexpired;
	}

	public void setUnexpired(BigDecimal unexpired) {
		this.unexpired = unexpired;
	}

	public BigDecimal getOtherIncome() {
		return otherIncome;
	}

	public void setOtherIncome(BigDecimal otherIncome) {
		this.otherIncome = otherIncome;
	}

	public BigDecimal getPerOtherIncome() {
		return perOtherIncome;
	}

	public void setPerOtherIncome(BigDecimal perOtherIncome) {
		this.perOtherIncome = perOtherIncome;
	}

	public BigDecimal getNetDistIncome() {
		return netDistIncome;
	}

	public void setNetDistIncome(BigDecimal netDistIncome) {
		this.netDistIncome = netDistIncome;
	}

	public String getSourceDistribute() {
        return sourceDistribute;
    }

    public void setSourceDistribute(String sourceDistribute) {
        this.sourceDistribute = sourceDistribute;
    }



}