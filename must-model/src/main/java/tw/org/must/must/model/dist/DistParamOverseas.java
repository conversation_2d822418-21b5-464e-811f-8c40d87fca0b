package tw.org.must.must.model.dist;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`dist_param_overseas`")
public class DistParamOverseas extends BaseEntity {
 
    private static final long serialVersionUID = -5050069885604902895L;
    /**
     * 
     */
    @Column(name = "`receipt_id`")
    private Long receiptId;

    /**
     * 
     */
    @Column(name = "`receipt_details_id`")
    private Long receiptDetailsId;

    /**
     * 
     */
    @Column(name = "`receipt_date`")
    private Date receiptDate;

    /**
     * 
     */
    @Column(name = "`source_society_code`")
    private Integer sourceSocietyCode;

    @Column(name = "source_society_name")
    private String sourceSocietyName;

    /**
     * 
     */
    @Column(name = "`dist_order_number`")
    private Integer distOrderNumber;

    /**
     *
     */
    @Column(name = "`roy_no`")
    private String royNo;

    /**
     * 0，没有明细数据，1有明细数据
     */
    @Column(name = "`is_record_data`")
    private Integer recorddata;

    /**
     * 0：非FIE金額，1FIE金額
     * statementAmount > 0 0
     * NoStatementAmount > 0 1
     * fieTotalAmount > 0 2
     */
    @Column(name = "`record_type`")
    private Integer recordType;

    /**
     * 删除，未确定怎么使用，
     */
    @Column(name = "`right_type`")
    private String rightType;

    /**
     * 分配编号
     */
    @Column(name = "`dist_no`")
    private String distNo;

    /**
     * 
     */
    @Column(name = "`remark`")
    private String remark;

    /**
     * 
     */
    @Column(name = "`category_code`")
    private String categoryCode;

    /**
     * 保留款比例
     */
    @Column(name = "`retain_rate`")
    private BigDecimal retainRate;

    /**
     * 保留款金额
     */
    @Column(name = "`retain_amount`")
    private BigDecimal retainAmount;

    /**
     * 汇率
     */
    @Column(name = "`exchange_rate`")
    private BigDecimal exchangeRate;

    /**
     * 
     */
    @Column(name = "`source_dist_no`")
    private String sourceDistNo;

    /**
     * 分配年份，对应界面Period
     */
    @Column(name = "`source_dist_year`")
    private String sourceDistYear;

    /**
     * 收款货币代码
     */
    @Column(name = "`receipt_currency_code`")
    private String receiptCurrencyCode;

    /**
     * 收款货币金额
     */
    @Column(name = "`receipt_currency_name`")
    private String receiptCurrencyName;

    /**
     * 总金额
     */
    @Column(name = "`source_total_amount`")
    private BigDecimal sourceTotalAmount;

    /**
     * 收款金额
     */
    @Column(name = "`receipt_amount`")
    private BigDecimal receiptAmount;

    /**
     * 有明细金额
     */
    @Column(name = "`source_data_amount`")
    private BigDecimal sourceDataAmount;

    /**
     * 无明细金额
     */
    @Column(name = "`source_no_data_amount`")
    private BigDecimal sourceNoDataAmount;

    /**
     * 手动添加数据金额
     */
    @Column(name = "`source_manual_amount`")
    private BigDecimal sourceManualAmount;

    /**
     * 管理费
     */
    @Column(name = "`source_deduction_amount`")
    private BigDecimal sourceDeductionAmount;

    /**
     * 税费
     */
    @Column(name = "`source_tax`")
    private BigDecimal sourceTax;

    /**
     * 原始银行手续费
     */
    @Column(name = "`source_bank_charge`")
    private BigDecimal sourceBankCharge;

    /**
     * 总金额 - 管理费 - 手续费
     */
    @Column(name = "`net_roy`")
    private BigDecimal netRoy;

    /**
     * 汇款币别金额
     */
    @Column(name = "`draft_gross_roy`")
    private BigDecimal draftGrossRoy;

    /**
     * 清單明細中匯款幣別的貨幣，如果該筆資料是FIE則寫死USD
     */
    @Column(name = "`draft_curr`")
    private String draftCurr;

    /**
     * 清單明細中的銀行手續費
     */
    @Column(name = "`less_draft_charge`")
    private BigDecimal lessDraftCharge;

    /**
     * 從清單明細中的【水單匯款金额】獲取
     */
    @Column(name = "`net_draft_roy`")
    private BigDecimal netDraftRoy;

    /**
     * 
     */
    @Column(name = "`local_total_amount`")
    private BigDecimal localTotalAmount;

    /**
     * 本地可分配金额
     */
    @Column(name = "`local_net_amount`")
    private BigDecimal localNetAmount;

    /**
     * 
     */
    @Column(name = "`local_bank_charge`")
    private BigDecimal localBankCharge;


    @Column(name = "`local_net_roy`")
    private BigDecimal localNetRoy;

    @Column(name = "`is_latest`")
    private  Byte isLatest;

    public String getSourceSocietyName() {
        return sourceSocietyName;
    }

    public void setSourceSocietyName(String sourceSocietyName) {
        this.sourceSocietyName = sourceSocietyName;
    }

    public Long getReceiptId() {
        return receiptId;
    }

    public void setReceiptId(Long receiptId) {
        this.receiptId = receiptId;
    }

    public Long getReceiptDetailsId() {
        return receiptDetailsId;
    }

    public void setReceiptDetailsId(Long receiptDetailsId) {
        this.receiptDetailsId = receiptDetailsId;
    }

    public Date getReceiptDate() {
        return receiptDate;
    }

    public void setReceiptDate(Date receiptDate) {
        this.receiptDate = receiptDate;
    }

    public Integer getSourceSocietyCode() {
        return sourceSocietyCode;
    }

    public void setSourceSocietyCode(Integer sourceSocietyCode) {
        this.sourceSocietyCode = sourceSocietyCode;
    }

    public Integer getDistOrderNumber() {
        return distOrderNumber;
    }

    public void setDistOrderNumber(Integer distOrderNumber) {
        this.distOrderNumber = distOrderNumber;
    }

    public Integer getRecorddata() {
        return recorddata;
    }

    public void setRecorddata(Integer recorddata) {
        this.recorddata = recorddata;
    }

    public Integer getRecordType() {
        return recordType;
    }

    public void setRecordType(Integer recordType) {
        this.recordType = recordType;
    }

    public String getRightType() {
        return rightType;
    }

    public void setRightType(String rightType) {
        this.rightType = rightType;
    }

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public BigDecimal getRetainRate() {
        return retainRate;
    }

    public void setRetainRate(BigDecimal retainRate) {
        this.retainRate = retainRate;
    }

    public BigDecimal getRetainAmount() {
        return retainAmount;
    }

    public void setRetainAmount(BigDecimal retainAmount) {
        this.retainAmount = retainAmount;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getSourceDistNo() {
        return sourceDistNo;
    }

    public void setSourceDistNo(String sourceDistNo) {
        this.sourceDistNo = sourceDistNo;
    }

    public String getSourceDistYear() {
        return sourceDistYear;
    }

    public void setSourceDistYear(String sourceDistYear) {
        this.sourceDistYear = sourceDistYear;
    }

    public String getReceiptCurrencyCode() {
        return receiptCurrencyCode;
    }

    public void setReceiptCurrencyCode(String receiptCurrencyCode) {
        this.receiptCurrencyCode = receiptCurrencyCode;
    }

    public String getReceiptCurrencyName() {
        return receiptCurrencyName;
    }

    public void setReceiptCurrencyName(String receiptCurrencyName) {
        this.receiptCurrencyName = receiptCurrencyName;
    }

    public BigDecimal getSourceTotalAmount() {
        if(sourceTotalAmount == null){
            return BigDecimal.ZERO;
        }
        return sourceTotalAmount;
    }

    public void setSourceTotalAmount(BigDecimal sourceTotalAmount) {
        this.sourceTotalAmount = sourceTotalAmount;
    }

    public BigDecimal getReceiptAmount() {
        if (receiptAmount == null){
            return BigDecimal.ZERO;
        }
        return receiptAmount;
    }

    public void setReceiptAmount(BigDecimal receiptAmount) {
        this.receiptAmount = receiptAmount;
    }

    public BigDecimal getSourceDataAmount() {
        if (sourceDataAmount == null){
            return BigDecimal.ZERO;
        }
        return sourceDataAmount;
    }

    public void setSourceDataAmount(BigDecimal sourceDataAmount) {
        this.sourceDataAmount = sourceDataAmount;
    }

    public BigDecimal getSourceNoDataAmount() {
        if (sourceNoDataAmount == null){
            return BigDecimal.ZERO;
        }
        return sourceNoDataAmount;
    }

    public void setSourceNoDataAmount(BigDecimal sourceNoDataAmount) {
        this.sourceNoDataAmount = sourceNoDataAmount;
    }

    public BigDecimal getSourceManualAmount() {
        return sourceManualAmount;
    }

    public void setSourceManualAmount(BigDecimal sourceManualAmount) {
        this.sourceManualAmount = sourceManualAmount;
    }

    public BigDecimal getSourceDeductionAmount() {
        return sourceDeductionAmount;
    }

    public void setSourceDeductionAmount(BigDecimal sourceDeductionAmount) {
        this.sourceDeductionAmount = sourceDeductionAmount;
    }

    public BigDecimal getSourceTax() {
        return sourceTax;
    }

    public void setSourceTax(BigDecimal sourceTax) {
        this.sourceTax = sourceTax;
    }

    public BigDecimal getSourceBankCharge() {
        return sourceBankCharge;
    }

    public void setSourceBankCharge(BigDecimal sourceBankCharge) {
        this.sourceBankCharge = sourceBankCharge;
    }

    public BigDecimal getLocalTotalAmount() {
        return localTotalAmount;
    }

    public void setLocalTotalAmount(BigDecimal localTotalAmount) {
        this.localTotalAmount = localTotalAmount;
    }

    public BigDecimal getLocalNetAmount() {
        return localNetAmount;
    }

    public void setLocalNetAmount(BigDecimal localNetAmount) {
        this.localNetAmount = localNetAmount;
    }

    public BigDecimal getLocalBankCharge() {
        return localBankCharge;
    }

    public void setLocalBankCharge(BigDecimal localBankCharge) {
        this.localBankCharge = localBankCharge;
    }

    public BigDecimal getNetRoy() {
        return netRoy;
    }

    public void setNetRoy(BigDecimal netRoy) {
        this.netRoy = netRoy;
    }

    public BigDecimal getDraftGrossRoy() {
        return draftGrossRoy;
    }

    public void setDraftGrossRoy(BigDecimal draftGrossRoy) {
        this.draftGrossRoy = draftGrossRoy;
    }

    public String getDraftCurr() {
        return draftCurr;
    }

    public void setDraftCurr(String draftCurr) {
        this.draftCurr = draftCurr;
    }

    public BigDecimal getLessDraftCharge() {
        return lessDraftCharge;
    }

    public void setLessDraftCharge(BigDecimal lessDraftCharge) {
        this.lessDraftCharge = lessDraftCharge;
    }

    public BigDecimal getNetDraftRoy() {
        return netDraftRoy;
    }

    public void setNetDraftRoy(BigDecimal netDraftRoy) {
        this.netDraftRoy = netDraftRoy;
    }

    public Byte getIsLatest() {
        return isLatest;
    }

    public void setIsLatest(Byte isLatest) {
        this.isLatest = isLatest;
    }

    public BigDecimal getLocalNetRoy() {
        return localNetRoy;
    }

    public void setLocalNetRoy(BigDecimal localNetRoy) {
        this.localNetRoy = localNetRoy;
    }

    public String getRoyNo() {
        return royNo;
    }

    public void setRoyNo(String royNo) {
        this.royNo = royNo;
    }
}