package tw.org.must.must.model.listoverseas;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Objects;

@Table(name = "`list_match_data_overseas`")
public class ListMatchDataOverseas extends BaseEntity {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
     * list_oversears_file.id
     */
    @Column(name = "`file_base_id`")
    private Long fileBaseId;
    
    @Column(name = "`file_mapping_id`")
    private Long fileMappingId;

    /**
     * 数据来源协会
     */
    @Column(name = "`remit_society`")
    private Integer remitSociety;

    /**
     * 
     */
    @Column(name = "`receipt_society`")
    private Integer receiptSociety;

    /**
     * 来源作品编码
     */
    @Column(name = "`source_work_code`")
    private String sourceWorkCode;

    /**
     * 
     */
    @Column(name = "`iswc`")
    private String iswc;

    /**
     * 
     */
    @Column(name = "`isrc`")
    private String isrc;

    /**
     * 
     */
    @Column(name = "`original_title`")
    private String originalTitle;

    /**
     * 
     */
    @Column(name = "`sub_title`")
    private String subTitle;

    /**
     * 作曲，多个;分隔
     */
    @Column(name = "`composer_name`")
    private String composerName;

    /**
     * 表演,多个;分隔
     */
    @Column(name = "`artist_name`")
    private String artistName;

    /**
     * 作词，多个;分隔
     */
    @Column(name = "`author_name`")
    private String authorName;

    /**
     * 如果无法区分作词作曲，那将值填入author_composer，比对时要同时与作词作曲者比对
     */
    @Column(name = "`author_composer`")
    private String authorComposer;

    /**
     * 0待匹配 .1.作品匹配 ,2.不匹配 3.ip匹配完毕
     */
    @Column(name = "`status`")
    private Integer status;

    /**
     * 
     */
    @Column(name = "`match_work_id`")
    private Long matchWorkId;

    /**
     * 
     */
    @Column(name = "`match_work_society_code`")
    private Integer matchWorkSocietyCode;

    /**
     * 
     */
    @Column(name = "`match_work_title_id`")
    private Long matchWorkTitleId;

    /**
     * 
     */
    @Column(name = "`match_work_title`")
    private String matchWorkTitle;

    /**
     * 
     */
    @Column(name = "`match_work_type`")
    private String matchWorkType;

    /**
     * 
     */
    @Column(name = "`match_score`")
    private BigDecimal matchScore;

    /**
     * 去重md5值，source_work_code+iswc+isrc+receipt_society+reference_number，字段之间用_分隔
     */
    @Column(name = "`data_unique_key`")
    private String dataUniqueKey;

    /**
     * 用以md5加密的字符串
     */
    @Column(name = "`data_unique_key_str`")
    private String dataUniqueKeyStr;

    /**
     * 
     */
    @Column(name = "`upload_user_id`")
    private Long uploadUserId;

    /**
     * 
     */
    @Column(name = "`upload_user_name`")
    private String uploadUserName;

    /**
     * 来源分配编号
     */
//    @Column(name = "`remit_dist_no`")
    private String remitDistNo;

    /**
     * 
     */
    @Column(name = "`reference_number`")
    private String referenceNumber;

    @Column(name = "`batch`")
    private Integer batch;

    @Column(name = "`match_work_unique_key`")
    private String matchWorkUniqueKey;

    @Column(name = "`match_type`")
    private String matchType;

    @Column(name = "`match_sub_title_id`")
    private Long matchSubTitleId;

    /**
     * 分配编号
     */
    @Column(name = "`dist_no`")
    private String distNo;

    @Transient
    private Boolean hasUnCheck;

    @Transient
    private String fileType;

    @Transient
    private String matchIswc;

    @Transient
    private String matchWorkgenre;

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public Boolean getHasUnCheck() {
        return hasUnCheck;
    }

    public void setHasUnCheck(Boolean hasUnCheck) {
        this.hasUnCheck = hasUnCheck;
    }

    public Long getFileBaseId() {
        return fileBaseId;
    }

    public void setFileBaseId(Long fileBaseId) {
        this.fileBaseId = fileBaseId;
    }

    
    
    public Long getFileMappingId() {
		return fileMappingId;
	}

	public void setFileMappingId(Long fileMappingId) {
		this.fileMappingId = fileMappingId;
	}

	public Integer getRemitSociety() {
        return remitSociety;
    }

    public void setRemitSociety(Integer remitSociety) {
        this.remitSociety = remitSociety;
    }

    public Integer getReceiptSociety() {
        return receiptSociety;
    }

    public void setReceiptSociety(Integer receiptSociety) {
        this.receiptSociety = receiptSociety;
    }

    public String getSourceWorkCode() {
        return sourceWorkCode;
    }

    public void setSourceWorkCode(String sourceWorkCode) {
        this.sourceWorkCode = sourceWorkCode;
    }

    public String getIswc() {
        return iswc;
    }

    public void setIswc(String iswc) {
        this.iswc = iswc;
    }

    public String getIsrc() {
        return isrc;
    }

    public void setIsrc(String isrc) {
        this.isrc = isrc;
    }

    public String getOriginalTitle() {
        return originalTitle;
    }

    public void setOriginalTitle(String originalTitle) {
        this.originalTitle = originalTitle;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getComposerName() {
        return composerName;
    }

    public void setComposerName(String composerName) {
        this.composerName = composerName;
    }

    public String getArtistName() {
        return artistName;
    }

    public void setArtistName(String artistName) {
        this.artistName = artistName;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public String getAuthorComposer() {
        return authorComposer;
    }

    public void setAuthorComposer(String authorComposer) {
        this.authorComposer = authorComposer;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getMatchWorkId() {
        return matchWorkId;
    }

    public void setMatchWorkId(Long matchWorkId) {
        this.matchWorkId = matchWorkId;
    }

    public Integer getMatchWorkSocietyCode() {
        return matchWorkSocietyCode;
    }

    public void setMatchWorkSocietyCode(Integer matchWorkSocietyCode) {
        this.matchWorkSocietyCode = matchWorkSocietyCode;
    }

    public Long getMatchWorkTitleId() {
        return matchWorkTitleId;
    }

    public void setMatchWorkTitleId(Long matchWorkTitleId) {
        this.matchWorkTitleId = matchWorkTitleId;
    }

    public String getMatchWorkTitle() {
        return matchWorkTitle;
    }

    public void setMatchWorkTitle(String matchWorkTitle) {
        this.matchWorkTitle = matchWorkTitle;
    }

    public String getMatchWorkType() {
        return matchWorkType;
    }

    public void setMatchWorkType(String matchWorkType) {
        this.matchWorkType = matchWorkType;
    }

    public BigDecimal getMatchScore() {
        return matchScore;
    }

    public void setMatchScore(BigDecimal matchScore) {
        this.matchScore = matchScore;
    }

    public String getDataUniqueKey() {
        return dataUniqueKey;
    }

    public void setDataUniqueKey(String dataUniqueKey) {
        this.dataUniqueKey = dataUniqueKey;
    }

    public String getDataUniqueKeyStr() {
        return dataUniqueKeyStr;
    }

    public void setDataUniqueKeyStr(String dataUniqueKeyStr) {
        this.dataUniqueKeyStr = dataUniqueKeyStr;
    }

    public Long getUploadUserId() {
        return uploadUserId;
    }

    public void setUploadUserId(Long uploadUserId) {
        this.uploadUserId = uploadUserId;
    }

    public String getUploadUserName() {
        return uploadUserName;
    }

    public void setUploadUserName(String uploadUserName) {
        this.uploadUserName = uploadUserName;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ListMatchDataOverseas that = (ListMatchDataOverseas) o;
        return fileBaseId.equals(that.fileBaseId) &&
                dataUniqueKey.equals(that.dataUniqueKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fileBaseId, dataUniqueKey);
    }

    public Integer getBatch() {
        return batch;
    }

    public void setBatch(Integer batch) {
        this.batch = batch;
    }

    public String getMatchWorkUniqueKey() {
        return matchWorkUniqueKey;
    }

    public void setMatchWorkUniqueKey(String matchWorkUniqueKey) {
        this.matchWorkUniqueKey = matchWorkUniqueKey;
    }

    public String getMatchIswc() {
        return matchIswc;
    }

    public void setMatchIswc(String matchIswc) {
        this.matchIswc = matchIswc;
    }

    public String getMatchWorkgenre() {
        return matchWorkgenre;
    }

    public void setMatchWorkgenre(String matchWorkgenre) {
        this.matchWorkgenre = matchWorkgenre;
    }

    public String getMatchType() {
        return matchType;
    }

    public void setMatchType(String matchType) {
        this.matchType = matchType;
    }

    public Long getMatchSubTitleId() {
        return matchSubTitleId;
    }

    public void setMatchSubTitleId(Long matchSubTitleId) {
        this.matchSubTitleId = matchSubTitleId;
    }

    public String getDistNo() {
        return distNo;
    }

    public void setDistNo(String distNo) {
        this.distNo = distNo;
    }

    public String getRemitDistNo() {
        return remitDistNo;
    }

    public void setRemitDistNo(String remitDistNo) {
        this.remitDistNo = remitDistNo;
    }
}