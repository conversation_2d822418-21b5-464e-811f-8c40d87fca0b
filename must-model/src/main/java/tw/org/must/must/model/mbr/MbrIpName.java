package tw.org.must.must.model.mbr;

import org.apache.commons.lang3.StringUtils;
import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`mbr_ip_name`")
public class MbrIpName extends BaseEntity {

    private static final long serialVersionUID = -8660695952484597448L;

    /**
     * ip 名词编号
     */
    @Column(name = "ip_name_no")
    private String ipNameNo;

    /**
     * PA（本名）、PP（笔名）、MO（曾用名）、DF(个人会员别名、外国籍会员的护照名、非华人会员的中文名)、OR（团体会员别名-指的英文名称）、PG（乐团）、HR（Holiding reference）
     */
    @Column(name = "name_type")
    private String nameType;

    /**
     * 名称
     */
    @Column(name = "first_name")
    private String firstName;

    /**
     * 姓
     */
    @Column(name = "last_name")
    private String lastName;

    /**
     * lastName + first_name
     */
    @Column(name = "name")
    private String name;
    
    /**
     * chineseLastName + chineseFirstName
     */
    @Column(name = "chinese_name")
    private String chineseName;

	/**
     * 这表示属于哪一个的ST或DF的IP，指向PA
     */
    @Column(name = "ip_name_no_ref")
    private String ipNameNoRef;

    /**
     * 是否转换Y、N
     */
    @Column(name = "transfer")
    private String transfer;

    /**
     * 是否有争议 Y、N
     */
    @Column(name = "sd")
    private String sd;

    /**
     * IPIEDI uploading only; ''D'' while deleted, ''U'' while update.
     */
    @Column(name = "ipi_action")
    private String ipiAction;

    /**
     * 中文名
     */
    @Column(name = "chinese_first_name")
    private String chineseFirstName;

    /**
     * 中文姓
     */
    @Column(name = "chinese_last_name")
    private String chineseLastName;

    /**
     * 中文拼音
     */
    @Column(name = "chinese_roma_name")
    private String chineseRomaName;


    public String getName() {
    	if(StringUtils.isBlank(name)) {
    		name = (lastName==null?"":lastName) + " "+(firstName==null?"":firstName);
    		name = name.trim();
            if (StringUtils.isBlank(name)) {
                name = "";
            }
    	}
		return name;
	}

    public void setName(String name) {
        this.name = name;
    }

    public String getChineseName() {
		if(StringUtils.isBlank(chineseName)) {
			chineseName = (chineseLastName==null?"":chineseLastName) + (chineseFirstName==null?"":chineseFirstName);
    		if (StringUtils.isBlank(chineseName)) {
    			chineseName = "";
			}
    	}
		return chineseName;
	}

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getIpNameNo() {

        return ipNameNo;
    }

    public void setIpNameNo(String ipNameNo) {
        this.ipNameNo = ipNameNo;
    }

    public String getNameType() {
        return nameType;
    }

    public void setNameType(String nameType) {
        this.nameType = nameType;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getIpNameNoRef() {
        return ipNameNoRef;
    }

    public void setIpNameNoRef(String ipNameNoRef) {
        this.ipNameNoRef = ipNameNoRef;
    }

    public String getTransfer() {
        return transfer;
    }

    public void setTransfer(String transfer) {
        this.transfer = transfer;
    }

    public String getSd() {
        return sd;
    }

    public void setSd(String sd) {
        this.sd = sd;
    }

    public String getIpiAction() {
        return ipiAction;
    }

    public void setIpiAction(String ipiAction) {
        this.ipiAction = ipiAction;
    }

    public String getChineseFirstName() {
        return chineseFirstName;
    }

    public void setChineseFirstName(String chineseFirstName) {
        this.chineseFirstName = chineseFirstName;
    }

    public String getChineseLastName() {
        return chineseLastName;
    }

    public void setChineseLastName(String chineseLastName) {
        this.chineseLastName = chineseLastName;
    }

    public String getChineseRomaName() {
        return chineseRomaName;
    }

    public void setChineseRomaName(String chineseRomaName) {
        this.chineseRomaName = chineseRomaName;
    }
    public static void main(String[] args) {
    	
    	MbrIpName mbr = new MbrIpName();
    	
    	mbr.setFirstName("li hong");
    	
    	mbr.setChineseFirstName("力宏");
    	System.out.println(mbr.getName());
    	System.out.println(mbr.getChineseName());
	}
}
