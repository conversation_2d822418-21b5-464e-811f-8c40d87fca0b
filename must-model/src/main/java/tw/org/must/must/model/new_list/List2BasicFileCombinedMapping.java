package tw.org.must.must.model.new_list;

import com.alibaba.fastjson.annotation.JSONField;
import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * list2_basic_file_combined_mapping
 * <AUTHOR>
@Table(name = "list2_basic_file_combined_mapping")
public class List2BasicFileCombinedMapping extends BaseEntity {

    private static final long serialVersionUID = -6398371407501400547L;
    /**
     * list2_basic_file_combined_total.id
     */
    private Long baseId;

    /**
     * 标题
     */
    private String title;

    /**
     * 表演者多个用;分隔
     */
    private String artists;

    /**
     * 作词多个用;分隔
     */
    private String authors;

    /**
     * 作曲多个用;分隔
     */
    private String composers;

    /**
     * 专辑
     */
    private String albumTitle;

    /**
     * 发行公司
     */
    private String publisher;

    /**
     * MS\PG\FW\CJ
     */
    private String uploadType;

    /**
     * 剧集
     */
    private String episodeNo;

    /**
     * AV(PG单作品)\NM
     */
    private String listWorkType;

    /**
     * 点播次数
     */
    private BigDecimal clickNumber;

    /**
     * 时间字符串格式
     */
    private String durationStr;

    /**
     * 分钟
     */
    private Integer durationM;

    /**
     * 秒
     */
    private Integer durationS;

    /**
     * 节目名称
     */
    private String tvName;

    /**
     * 频道名称
     */
    private String channelName;

    /**
     * 音乐语言
     */
    private String musicLang;

    private Long workId;

    private Integer workSocietyCode;

    /**
     * 扩展属性，用于存储无法识别列数据
     */
    private String extJson;

    /**
     * 业务id
     */
    private String bussionId;

    /**
     * 使用日期
     */
    @JSONField(format = "yyyyMMdd")
    private Date performTime;

    /**
     * Y  or N 。Y标识抽样要的数据，N不需要参与分配数据，也不需要match
     */
    private String isSampleDate;

    private String isrc;

    private String iswc;

    private String categoryCode;

    private String poolRight;

    private String poolCode;

    @Column(name = "`usage`")
    private String usage;

    private Integer startHour;

    private Integer startMin;

    private String assetId;

    /**
     * 用来合并加总判断的key
     */
    private String dataUniqueKey;

    /**
     * 用户唯一标识码
     */
    private String userUniqueCode;

    public Long getBaseId() {
        return baseId;
    }

    public void setBaseId(Long baseId) {
        this.baseId = baseId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getArtists() {
        return artists;
    }

    public void setArtists(String artists) {
        this.artists = artists;
    }

    public String getAuthors() {
        return authors;
    }

    public void setAuthors(String authors) {
        this.authors = authors;
    }

    public String getComposers() {
        return composers;
    }

    public void setComposers(String composers) {
        this.composers = composers;
    }

    public String getAlbumTitle() {
        return albumTitle;
    }

    public void setAlbumTitle(String albumTitle) {
        this.albumTitle = albumTitle;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public String getUploadType() {
        return uploadType;
    }

    public void setUploadType(String uploadType) {
        this.uploadType = uploadType;
    }

    public String getEpisodeNo() {
        return episodeNo;
    }

    public void setEpisodeNo(String episodeNo) {
        this.episodeNo = episodeNo;
    }

    public String getListWorkType() {
        return listWorkType;
    }

    public void setListWorkType(String listWorkType) {
        this.listWorkType = listWorkType;
    }

    public BigDecimal getClickNumber() {
        return clickNumber;
    }

    public void setClickNumber(BigDecimal clickNumber) {
        this.clickNumber = clickNumber;
    }

    public String getDurationStr() {
        return durationStr;
    }

    public void setDurationStr(String durationStr) {
        this.durationStr = durationStr;
    }

    public Integer getDurationM() {
        return durationM;
    }

    public void setDurationM(Integer durationM) {
        this.durationM = durationM;
    }

    public Integer getDurationS() {
        return durationS;
    }

    public void setDurationS(Integer durationS) {
        this.durationS = durationS;
    }

    public String getTvName() {
        return tvName;
    }

    public void setTvName(String tvName) {
        this.tvName = tvName;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getMusicLang() {
        return musicLang;
    }

    public void setMusicLang(String musicLang) {
        this.musicLang = musicLang;
    }

    public Long getWorkId() {
        return workId;
    }

    public void setWorkId(Long workId) {
        this.workId = workId;
    }

    public Integer getWorkSocietyCode() {
        return workSocietyCode;
    }

    public void setWorkSocietyCode(Integer workSocietyCode) {
        this.workSocietyCode = workSocietyCode;
    }

    public String getExtJson() {
        return extJson;
    }

    public void setExtJson(String extJson) {
        this.extJson = extJson;
    }

    public String getBussionId() {
        return bussionId;
    }

    public void setBussionId(String bussionId) {
        this.bussionId = bussionId;
    }

    public Date getPerformTime() {
        return performTime;
    }

    public void setPerformTime(Date performTime) {
        this.performTime = performTime;
    }

    public String getIsSampleDate() {
        return isSampleDate;
    }

    public void setIsSampleDate(String isSampleDate) {
        this.isSampleDate = isSampleDate;
    }

    public String getIsrc() {
        return isrc;
    }

    public void setIsrc(String isrc) {
        this.isrc = isrc;
    }

    public String getIswc() {
        return iswc;
    }

    public void setIswc(String iswc) {
        this.iswc = iswc;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getPoolRight() {
        return poolRight;
    }

    public void setPoolRight(String poolRight) {
        this.poolRight = poolRight;
    }

    public String getPoolCode() {
        return poolCode;
    }

    public void setPoolCode(String poolCode) {
        this.poolCode = poolCode;
    }

    public String getUsage() {
        return usage;
    }

    public void setUsage(String usage) {
        this.usage = usage;
    }

    public Integer getStartHour() {
        return startHour;
    }

    public void setStartHour(Integer startHour) {
        this.startHour = startHour;
    }

    public Integer getStartMin() {
        return startMin;
    }

    public void setStartMin(Integer startMin) {
        this.startMin = startMin;
    }

    public String getAssetId() {
        return assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    public String getDataUniqueKey() {
        return dataUniqueKey;
    }

    public void setDataUniqueKey(String uniqueKeyMd5) {
        this.dataUniqueKey = uniqueKeyMd5;
    }

    public String getUserUniqueCode() {
        return userUniqueCode;
    }

    public void setUserUniqueCode(String userUniqueCode) {
        this.userUniqueCode = userUniqueCode;
    }
}
