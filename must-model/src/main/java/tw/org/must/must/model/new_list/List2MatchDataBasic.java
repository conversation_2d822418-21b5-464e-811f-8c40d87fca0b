package tw.org.must.must.model.new_list;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Table(name = "`list2_match_data_basic`")
public class List2MatchDataBasic extends BaseEntity {

    @Transient
    private String year;
    @Transient
    private String searchText;

    public String getSearchText() {
        return searchText;
    }

    public void setSearchText(String searchText) {
        this.searchText = searchText;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public List<Long> getFileBaseIdList() {
        return fileBaseIdList;
    }

    public void setFileBaseIdList(List<Long> fileBaseIdList) {
        this.fileBaseIdList = fileBaseIdList;
    }

    @Transient
    private List<Long> fileBaseIdList;


    @Column(name = "`batch_seq`")
    private Integer batchSeq;

    public Integer getBatchSeq() {
        return batchSeq;
    }

    public void setBatchSeq(Integer batchSeq) {
        this.batchSeq = batchSeq;
    }

    /**
     * 标题
     */
    @Column(name = "`title`")
    private String title;

    /**
     * 表演者
     */
    @Column(name = "`artists`")
    private String artists;

    /**
     * 作词
     */
    @Column(name = "`authors`")
    private String authors;

    /**
     * 作曲
     */
    @Column(name = "`composers`")
    private String composers;

    /**
     * 发行公司
     */
    @Column(name = "`publisher`")
    private String publisher;

    /**
     * 剧集
     */
    @Column(name = "`episode_no`")
    private String episodeNo;

    /**
     * PG\\FW\\MS\\CJ
     */
    @Column(name = "`upload_type`")
    private String uploadType;

    /**
     *
     */
    @Column(name = "`click_number`")
    private BigDecimal clickNumber;

    /**
     *
     */
    @Column(name = "`duration_m`")
    private Integer durationM;

    /**
     *
     */
    @Column(name = "`duration_s`")
    private Integer durationS;

    /**
     * 使用日期
     */
    @Column(name = "`perform_time`")
    private Date performTime;

    /**
     * list_basic_file_data_mapping.id
     */
    @Column(name = "`file_mapping_id`")
    private Long fileMappingId;

    /**
     * list_basic_file_base.id
     */
    @Column(name = "`file_base_id`")
    private Long fileBaseId;

    /**
     * 批次号
     */
    @Column(name = "`batch_id`")
    private Long batchId;

    /**
     * 节目名称
     */
    @Column(name = "`tv_name`")
    private String tvName;

    /**
     * 频道名称
     */
    @Column(name = "`channel_name`")
    private String channelName;

    /**
     *
     */
    @Column(name = "`ext_json`")
    private String extJson;

    /**
     *
     */
    @Column(name = "`match_work_id`")
    private Long matchWorkId;

    /**
     *
     */
    @Column(name = "`match_work_society_code`")
    private Integer matchWorkSocietyCode;

    @Column(name = "`match_work_unique_key`")
    private String matchWorkUniqueKey;

    /**
     * 匹配上的标题id
     */

    @Column(name = "`match_work_title_id`")
    private Long matchWorkTitleId;

    /**
     * 匹配作品标题
     */
    @Column(name = "`match_work_title`")
    private String matchWorkTitle;

    @Column(name = "`match_work_authors`")
    private String matchWorkAuthors;


    @Column(name = "`match_work_composers`")
    private String matchWorkComposers;

    @Column(name = "`match_work_type`")
    private String matchWorkType;

    /**
     * 0:待审核，1已匹配，2不匹配，3认作者
     */
    @Column(name = "`status`")
    private Integer status;

    /**
     * 0，未自动匹配，1自动匹配
     */
    @Column(name = "`auto_match`")
    private Integer autoMatch;

    /**
     *
     */
    @Column(name = "`match_score`")
    private BigDecimal matchScore;

    @Column(name = "`unique_key_md5`")
    private String uniqueKeyMd5;

    /**
     * 数据来源，
     */
    @Column(name = "`source_code`")
    private Integer sourceCode;

    @Column(name = "`album_title`")
    private String albumTitle;

    @Column(name = "`pool_code`")
    private String poolCode;
    @Column(name = "`pool_right`")
    private String poolRight;

    @Column(name = "`isrc`")
    private String isrc;

    @Column(name = "`iswc`")
    private String iswc;

    @Column(name = "`category_code`")
    private String categoryCode;

    @Column(name = "`upload_time`")
    private Date uploadTime;

    @Column(name = "`upload_user_id`")
    private Long uploadUserId;

    @Column(name = "`upload_user_name`")
    private String uploadUserName;

    @Column(name = "`match_sub_title_id`")
    private Long matchSubTitleId;

    @Transient
    private Integer pageNum ;

    @Transient
    private String statusStr ;

    @Transient
    private String remark;


    public String getMatchWorkAuthors() {
        return matchWorkAuthors;
    }

    public void setMatchWorkAuthors(String matchWorkAuthors) {
        this.matchWorkAuthors = matchWorkAuthors;
    }

    public String getMatchWorkComposers() {
        return matchWorkComposers;
    }

    public void setMatchWorkComposers(String matchWorkComposers) {
        this.matchWorkComposers = matchWorkComposers;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getArtists() {
        return artists;
    }

    public void setArtists(String artists) {
        this.artists = artists;
    }

    public String getAuthors() {
        return authors;
    }

    public void setAuthors(String authors) {
        this.authors = authors;
    }

    public String getComposers() {
        return composers;
    }

    public void setComposers(String composers) {
        this.composers = composers;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public String getEpisodeNo() {
        return episodeNo;
    }

    public void setEpisodeNo(String episodeNo) {
        this.episodeNo = episodeNo;
    }

    public String getUploadType() {
        return uploadType;
    }

    public void setUploadType(String uploadType) {
        this.uploadType = uploadType;
    }

    public BigDecimal getClickNumber() {
        return clickNumber;
    }

    public void setClickNumber(BigDecimal clickNumber) {
        this.clickNumber = clickNumber;
    }

    public Integer getDurationM() {
        return durationM;
    }

    public void setDurationM(Integer durationM) {
        this.durationM = durationM;
    }

    public Integer getDurationS() {
        return durationS;
    }

    public void setDurationS(Integer durationS) {
        this.durationS = durationS;
    }

    public Date getPerformTime() {
        return performTime;
    }

    public void setPerformTime(Date performTime) {
        this.performTime = performTime;
    }

    public Long getFileMappingId() {
        return fileMappingId;
    }

    public void setFileMappingId(Long fileMappingId) {
        this.fileMappingId = fileMappingId;
    }

    public Long getFileBaseId() {
        return fileBaseId;
    }

    public void setFileBaseId(Long fileBaseId) {
        this.fileBaseId = fileBaseId;
    }

    public String getTvName() {
        return tvName;
    }

    public void setTvName(String tvName) {
        this.tvName = tvName;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getExtJson() {
        return extJson;
    }

    public void setExtJson(String extJson) {
        this.extJson = extJson;
    }

    public Long getMatchWorkId() {
        return matchWorkId;
    }

    public void setMatchWorkId(Long matchWorkId) {
        this.matchWorkId = matchWorkId;
    }

    public Integer getMatchWorkSocietyCode() {
        return matchWorkSocietyCode;
    }

    public void setMatchWorkSocietyCode(Integer matchWorkSocietyCode) {
        this.matchWorkSocietyCode = matchWorkSocietyCode;
    }

    public String getMatchWorkTitle() {
        return matchWorkTitle;
    }

    public void setMatchWorkTitle(String matchWorkTitle) {
        this.matchWorkTitle = matchWorkTitle;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getAutoMatch() {
        return autoMatch;
    }

    public void setAutoMatch(Integer autoMatch) {
        this.autoMatch = autoMatch;
    }

    public BigDecimal getMatchScore() {
        return matchScore;
    }

    public void setMatchScore(BigDecimal matchScore) {
        this.matchScore = matchScore;
    }

    public Integer getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(Integer sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getUniqueKeyMd5() {
        return uniqueKeyMd5;
    }

    public void setUniqueKeyMd5(String uniqueKeyMd5) {
        this.uniqueKeyMd5 = uniqueKeyMd5;
    }

    public String getAlbumTitle() {
        return albumTitle;
    }

    public void setAlbumTitle(String albumTitle) {
        this.albumTitle = albumTitle;
    }

    public String getIsrc() {
        return isrc;
    }

    public void setIsrc(String isrc) {
        this.isrc = isrc;
    }

    public String getIswc() {
        return iswc;
    }

    public void setIswc(String iswc) {
        this.iswc = iswc;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Long getUploadUserId() {
        return uploadUserId;
    }

    public void setUploadUserId(Long uploadUserId) {
        this.uploadUserId = uploadUserId;
    }

    public String getUploadUserName() {
        return uploadUserName;
    }

    public void setUploadUserName(String uploadUserName) {
        this.uploadUserName = uploadUserName;
    }

    public String getMatchWorkUniqueKey() {
        return matchWorkUniqueKey;
    }

    public void setMatchWorkUniqueKey(String matchWorkUniqueKey) {
        this.matchWorkUniqueKey = matchWorkUniqueKey;
    }

    public String getPoolCode() {
        return poolCode;
    }

    public void setPoolCode(String poolCode) {
        this.poolCode = poolCode;
    }

    public String getPoolRight() {
        return poolRight;
    }

    public void setPoolRight(String poolRight) {
        this.poolRight = poolRight;
    }

    public String getMatchWorkType() {
        return matchWorkType;
    }

    public void setMatchWorkType(String matchWorkType) {
        this.matchWorkType = matchWorkType;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public Long getMatchWorkTitleId() {
        return matchWorkTitleId;
    }

    public void setMatchWorkTitleId(Long matchWorkTitleId) {
        this.matchWorkTitleId = matchWorkTitleId;
    }

    public Long getMatchSubTitleId() {
        return matchSubTitleId;
    }

    public void setMatchSubTitleId(Long matchSubTitleId) {
        this.matchSubTitleId = matchSubTitleId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}