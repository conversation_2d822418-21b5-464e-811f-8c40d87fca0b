package tw.org.must.must.model.ref;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`ref_society_contact_dept`")
public class RefSocietyContactDept extends BaseEntity {

    /**
     * 
     */
    @Column(name = "`dept_id`")
    private Long deptId;

    /**
     * 
     */
    @Column(name = "`dept`")
    private String dept;

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }



}