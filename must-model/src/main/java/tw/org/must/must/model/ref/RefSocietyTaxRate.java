package tw.org.must.must.model.ref;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:
 * @author: handa
 * @time: 2019/12/6 15:58
 */
@Table(name = "ref_society_tax_rate")
public class RefSocietyTaxRate implements Serializable {
    private static final long serialVersionUID = -912295771981351154L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "country_code")
    private  String countryCode;

    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    @Column(name = "country_name")
    private String countryName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }
}
