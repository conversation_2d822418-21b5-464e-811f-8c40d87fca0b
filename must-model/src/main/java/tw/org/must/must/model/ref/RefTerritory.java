package tw.org.must.must.model.ref;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "`ref_territory`")
public class RefTerritory extends BaseEntity {

    /**
     * 
     */
    @Column(name = "tis_n")
    private Long tisN;

    /**
     * 
     */
    @Column(name = "description")
    private String description;

    /**
     * 
     */
    @Column(name = "tis_a")
    private String tisA;

    /**
     * 
     */
    @Column(name = "cwr_date")
    private String cwrDate;

    /**
     * 
     */
    @Column(name = "active")
    private String active;

    public Long getTisN() {
        return tisN;
    }

    public void setTisN(Long tisN) {
        this.tisN = tisN;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTisA() {
        return tisA;
    }

    public void setTisA(String tisA) {
        this.tisA = tisA;
    }

    public String getCwrDate() {
        return cwrDate;
    }

    public void setCwrDate(String cwrDate) {
        this.cwrDate = cwrDate;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }



}