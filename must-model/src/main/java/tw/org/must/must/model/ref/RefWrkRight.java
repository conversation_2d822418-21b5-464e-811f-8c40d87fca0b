package tw.org.must.must.model.ref;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Table(name = "`ref_wrk_right`")
public class RefWrkRight {

    /**
     * 
     */
	@Id
    @Column(name = "right_type")
    private String rightType;

    /**
     * 
     */
    @Column(name = "description")
    private String description;

    public String getRightType() {
        return rightType;
    }

    public void setRightType(String rightType) {
        this.rightType = rightType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }



}