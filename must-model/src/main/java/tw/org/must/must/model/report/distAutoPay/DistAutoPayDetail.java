package tw.org.must.must.model.report.distAutoPay;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class DistAutoPayDetail implements Serializable {
    private static final long serialVersionUID = -6954378623008163409L;

    //年度
    private String year;

    //演奏权利金
    private BigDecimal performRoyalties;

    //基本分配额
    private BigDecimal allocation;

    //权利金调整
    private BigDecimal royaltiesAdj;

    private BigDecimal royaltiesAdj2;

    //会员保留款
    private BigDecimal memberRetain;

    //管理费比例
    private BigDecimal commissionRate;

    //管理费
    private BigDecimal commissionAmount;

    //所得税比例
    private BigDecimal taxableRate;

    //所得税
    private BigDecimal taxableAmount;

    //公益基金比例
    private BigDecimal reciprocalRate;

    //公益基金
    private BigDecimal reciprocalAmount;

    //权利金
    private BigDecimal royaltyAmount;

    private BigDecimal salesTaxRate;

    private BigDecimal salesTaxAmount;

    //演奏权利金描述
    private String firstDescribe;
    //基本分配額描述
    private String distributionDescribe;

    //会员保留款
    private String memberDescribe;

    //700
    private String secondDescribe;

    private String thirdDescribe;

    //附加管理费比例
    private BigDecimal additionalCommissionRate;

    //附加管理费 针对准会员加收的附加管理费
    private BigDecimal additionalCommissionAmount;

    //所得税调整
    private BigDecimal deduction;

    private String royalDescribe ;

}