package tw.org.must.must.model.report.distAutoPay;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class DistAutoPayOverseasReceipt implements Serializable {
    private static final long serialVersionUID = -2537266705782874846L;

    private String date;

    private String societyName;

    private Integer societyCode;

    private String payCurrency;

    private BigDecimal feeInError;

    private BigDecimal adjTotalAmount;

    private BigDecimal adjSendAmount;

    private String paymentMethod;

    private BigDecimal affiliated;

    private BigDecimal bankCharge;

    private BigDecimal netPayment;

    private BigDecimal exchangeRate;

    private BigDecimal totalPayment;

    private DistAutoPay bases;
}
