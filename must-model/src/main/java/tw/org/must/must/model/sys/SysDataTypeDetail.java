package tw.org.must.must.model.sys;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Table;
import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`sys_data_type_detail`")
public class SysDataTypeDetail extends BaseEntity {

    /**
     * 与 sys_data_type表code对应
     */
    @Column(name = "`code`")
    private String code;

    /**
     * 
     */
    @Column(name = "`detail_code`")
    private String detailCode;

    /**
     * 详情detail_code描述
     */
    @Column(name = "`detail_message`")
    private String detailMessage;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDetailCode() {
        return detailCode;
    }

    public void setDetailCode(String detailCode) {
        this.detailCode = detailCode;
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    public void setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
    }



}