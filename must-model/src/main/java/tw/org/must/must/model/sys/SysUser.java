package tw.org.must.must.model.sys;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Table(name = "`sys_user`")
public class SysUser extends BaseEntity {

    private static final long serialVersionUID = -8719524038202581432L;
    /**
     * 中文名称
     */
    @NotBlank
    @Column(name = "name")
    private String name;

    /**
     * 英文
     */
    @NotBlank
    @Column(name = "name_en")
    private String nameEn;

    /**
     * 账户
     */
    @NotBlank
    @Column(name = "account")
    private String account;

    /**
     * 密钥
     */
    @NotBlank
    @Column(name = "password")
    private String password;

    /**
     * 删除状态，0：不删除，1：删除，默认0
     */
    @Column(name = "is_deleted")
    private Boolean deleted;

    @Column(name = "email")
    private String email;

    @Column(name = "mobile")
    private String mobile;

    private List<Long> sysRoleList;

    @Override
    public void init() {
        super.init();
        this.setDeleted(false);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }


    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public List<Long> getSysRoleList() {
        return sysRoleList;
    }

    public void setSysRoleList(List<Long> sysRoleList) {
        this.sysRoleList = sysRoleList;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }
}