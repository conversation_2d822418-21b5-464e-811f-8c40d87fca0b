package tw.org.must.must.model.util;

import tw.org.must.must.model.wrk.WrkWork;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Calendar;

public class CommonModelUtil {

    public static WrkWork createWrkWork(String workUniqueKey, String  genre, Long refWorkId, Integer refWorkSoc){
        WrkWork wrkWork = new WrkWork();
        wrkWork.setGenre(genre);
        wrkWork.setWorkUniqueKey(workUniqueKey);
        if(null != refWorkId){
            wrkWork.setRefWorkId(refWorkId);
        }
        if(null != refWorkSoc){
            wrkWork.setRefWorkSociety(refWorkSoc);
        }
        return wrkWork;
    }

    public static String getYearByDistNo(String distNo){
        String year2 = distNo.substring(1,3);
        Calendar date = Calendar.getInstance();
        String year1 = String.valueOf(date.get(Calendar.YEAR)).substring(0,2);
        String year = year1+year2;
        return year;
    }

    /**
     * 将BigDecimal格式化为货币字符串显示格式
     * 正数显示为 $xxx,xxx.xx
     * 负数显示为 -$xxx,xxx.xx
     * null值显示为 $0.00
     * 
     * @param value 要格式化的BigDecimal值
     * @return 格式化后的字符串
     */
    public static String formatCurrency(BigDecimal value) {
        if (value == null) {
            return "$0.00";
        }
        DecimalFormat df = new DecimalFormat("#,##0.00");
        BigDecimal absValue = value.abs();
        String formatted = df.format(absValue);
        if (value.compareTo(BigDecimal.ZERO) < 0) {
            return "-$" + formatted;
        } else {
            return "$" + formatted;
        }
    }
}