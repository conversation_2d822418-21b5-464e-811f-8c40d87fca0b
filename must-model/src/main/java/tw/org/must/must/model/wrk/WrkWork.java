package tw.org.must.must.model.wrk;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

@Table(name = "`wrk_work`")
public class WrkWork extends BaseEntity {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4686226518889886104L;

	/**
	 * 协会编号
	 */
	@Column(name = "work_society_code")
	private Integer workSocietyCode;

	/**
	 * 作品类别：ORG,ADP,ARG,AV,TV
	 */
	@Column(name = "work_type")
	@NotBlank(message = "workType不能为空！")
	private String workType;

	/**
	 * av ，tv填写，导演
	 */
	@Column(name = "director")
	private String director;

	/**
	 * 引用作品id,作品是adp/arg会有这种类型
	 */
	@Column(name = "ref_work_id")
	private Long refWorkId;

	/**
	 * 协会编码
	 */
	@Column(name = "ref_work_society")
	private Integer refWorkSociety;

	/**
	 * 0，未同步，1已同步
	 */
	@Column(name = "right_syn_indicator")
	private Integer rightSynIndicator;

	/**
	 * 首次创建时间
	 */
	@Column(name = "first_create_date")
	private Date firstCreateDate;

	/**
	 * 
	 */
	@Column(name = "cue_no")
	private String cueNo;

	/**
	 * 出品公司
	 */
	@Column(name = "product_company")
	private String productCompany;

	/**
	 * 出品年份
	 */
	@Column(name = "production_year")
	private Integer productionYear;

	/**
	 * society_code+work_code
	 */
	@Column(name = "society_work_code")
	@Size(max = 3, message = "societyWorkCode最大长度为3")
	private String societyWorkCode;

	/**
	 * 
	 */
	@Column(name = "isan")
	private Integer isan;

	/**
	 * 发布日期
	 */
	@Column(name = "publish_air_date")
	private Date publishAirDate;

	/**
	 * 
	 */
	@Column(name = "production_no")
	private String productionNo;

	/**
	 * 表演语言
	 */
	@Column(name = "perform_language")
	@Size(max = 3, message = "performLanguage最大长度为3")
	private String performLanguage;

	/**
	 * 修改用户
	 */
	@Column(name = "amend_user")
	@NotBlank(message = "amendUser不能为空！")
	private String amendUser;

	/**
	 * 修改用户id
	 */
	@Column(name = "amend_user_id")
	@NotNull(message = "amendUserId不能为空！")
	private Long amendUserId;

	/**
	 * ''L'' = local, ''F'' = foreign work
	 */
	@Column(name = "is_local")
	private String local;

	/**
	 * 可分配标记,0不可分配，1可分配
	 */
	@Column(name = "dist_flag")
	private Integer distFlag;

	/**
	 * 备注
	 */
	@Column(name = "remark")
	private String remark;

	/**
	 * 曲风,数据来自ref_genre_dtl
	 */
	@Column(name = "genre")
	@Size(max = 3, message = "genre不能超过3位！")
	private String genre;

	/**
	 * 分钟
	 */
	@Column(name = "duration_m")
	private Integer durationM;

	/**
	 * 秒，不能超过59
	 */
	@Column(name = "duration_s")
	private Integer durationS;

	/**
	 * ISWC
	 */
	@Column(name = "ISWC")
	private String ISWC;
	/**
	 * 歌词
	 */
	@Column(name = "lyrics")
	private String lyrics;

	/**
	 * 0删除 1有效
	 */
	@Column(name = "status")
	private Integer status;

	/**
	 * AV作品是否提交标识，该属性只有av作品有效
	 */
	@Column(name = "completion")
	private String completion;
	
	
	@Column(name = "work_id")
	private Long workId;
	
	@Column(name = "work_unique_key")
	private String workUniqueKey;
	
	@Column(name = "sd")
	private String sd;

	/**
	 * oracle更新时间
	 */
	@Column(name = "oracle_amend_time")
	private Date oracleAmendTime;
	
	@Transient
	@NotBlank(message = "title不能为空！")
	private String title;

	@Transient
	private String titleLanguage;

	@Transient
	private String episodeNo;
	
	@Transient
	private String autoCalc;
	
	@Transient
	private boolean transferStatus;
	
	@Transient
	private String toTitle;

	@Transient
	private Long destWorkNum;

	@Transient
	private Integer destWorkNumSoc;

	@Transient
	private Long destSubTitleId;

	public String getAutoCalc() {
		return autoCalc;
	}

	public void setAutoCalc(String autoCalc) {
		this.autoCalc = autoCalc;
	}

	public String getEpisodeNo() {
		return episodeNo;
	}

	public void setEpisodeNo(String episodeNo) {
		this.episodeNo = episodeNo;
	}

	public String getTitleLanguage() {
		return titleLanguage;
	}

	public void setTitleLanguage(String titleLanguage) {
		this.titleLanguage = titleLanguage;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Integer getWorkSocietyCode() {
		return workSocietyCode;
	}

	public void setWorkSocietyCode(Integer workSocietyCode) {
		this.workSocietyCode = workSocietyCode;
	}

	public String getWorkType() {
		return workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}

	public String getDirector() {
		return director;
	}

	public void setDirector(String director) {
		this.director = director;
	}

	public Long getRefWorkId() {
		return refWorkId;
	}

	public void setRefWorkId(Long refWorkId) {
		this.refWorkId = refWorkId;
	}

	public Integer getRefWorkSociety() {
		return refWorkSociety;
	}

	public void setRefWorkSociety(Integer refWorkSociety) {
		this.refWorkSociety = refWorkSociety;
	}

	public Integer getRightSynIndicator() {
		return rightSynIndicator;
	}

	public void setRightSynIndicator(Integer rightSynIndicator) {
		this.rightSynIndicator = rightSynIndicator;
	}

	public Date getFirstCreateDate() {
		return firstCreateDate;
	}

	public void setFirstCreateDate(Date firstCreateDate) {
		this.firstCreateDate = firstCreateDate;
	}

	public String getCueNo() {
		return cueNo;
	}

	public void setCueNo(String cueNo) {
		this.cueNo = cueNo;
	}

	public String getProductCompany() {
		return productCompany;
	}

	public void setProductCompany(String productCompany) {
		this.productCompany = productCompany;
	}

	public Integer getProductionYear() {
		return productionYear;
	}

	public void setProductionYear(Integer productionYear) {
		this.productionYear = productionYear;
	}

	public String getSocietyWorkCode() {
		return societyWorkCode;
	}

	public void setSocietyWorkCode(String societyWorkCode) {
		this.societyWorkCode = societyWorkCode;
	}

	public Integer getIsan() {
		return isan;
	}

	public void setIsan(Integer isan) {
		this.isan = isan;
	}

	public Date getPublishAirDate() {
		return publishAirDate;
	}

	public void setPublishAirDate(Date publishAirDate) {
		this.publishAirDate = publishAirDate;
	}

	public String getProductionNo() {
		return productionNo;
	}

	public void setProductionNo(String productionNo) {
		this.productionNo = productionNo;
	}

	public String getPerformLanguage() {
		return performLanguage;
	}

	public void setPerformLanguage(String performLanguage) {
		this.performLanguage = performLanguage;
	}

	public String getAmendUser() {
		return amendUser;
	}

	public void setAmendUser(String amendUser) {
		this.amendUser = amendUser;
	}

	public Long getAmendUserId() {
		return amendUserId;
	}

	public void setAmendUserId(Long amendUserId) {
		this.amendUserId = amendUserId;
	}

	public String getLocal() {
		return local;
	}

	public void setLocal(String local) {
		this.local = local;
	}

	public Integer getDistFlag() {
		return distFlag;
	}

	public void setDistFlag(Integer distFlag) {
		this.distFlag = distFlag;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getGenre() {
		return genre;
	}

	public void setGenre(String genre) {
		this.genre = genre;
	}

	public Integer getDurationM() {
		return durationM;
	}

	public void setDurationM(Integer durationM) {
		this.durationM = durationM;
	}

	public Integer getDurationS() {
		return durationS;
	}

	public void setDurationS(Integer durationS) {
		this.durationS = durationS;
	}

	public String getISWC() {
		return ISWC;
	}

	public void setISWC(String ISWC) {
		this.ISWC = ISWC;
	}

	public String getCompletion() {
		return completion;
	}

	public void setCompletion(String completion) {
		this.completion = completion;
	}

	public String getLyrics() {
		return lyrics;
	}

	public void setLyrics(String lyrics) {
		this.lyrics = lyrics;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getWorkId() {
		return workId;
	}

	public void setWorkId(Long workId) {
		this.workId = workId;
	}

	public boolean getTransferStatus() {
		return transferStatus;
	}

	public void setTransferStatus(boolean transferStatus) {
		this.transferStatus = transferStatus;
	}

	public String getWorkUniqueKey() {
		return workUniqueKey;
	}

	public void setWorkUniqueKey(String workUniqueKey) {
		this.workUniqueKey = workUniqueKey;
	}

	public String getSd() {
		return sd;
	}

	public void setSd(String sd) {
		this.sd = sd;
	}

	public String getToTitle() {
		return toTitle;
	}

	public void setToTitle(String toTitle) {
		this.toTitle = toTitle;
	}

	public Long getDestWorkNum() {
		return destWorkNum;
	}

	public void setDestWorkNum(Long destWorkNum) {
		this.destWorkNum = destWorkNum;
	}

	public Integer getDestWorkNumSoc() {
		return destWorkNumSoc;
	}

	public void setDestWorkNumSoc(Integer destWorkNumSoc) {
		this.destWorkNumSoc = destWorkNumSoc;
	}

	public Long getDestSubTitleId() {
		return destSubTitleId;
	}

	public void setDestSubTitleId(Long destSubTitleId) {
		this.destSubTitleId = destSubTitleId;
	}

	public Date getOracleAmendTime() {
		return oracleAmendTime;
	}

	public void setOracleAmendTime(Date oracleAmendTime) {
		this.oracleAmendTime = oracleAmendTime;
	}

	public boolean isTransferStatus() {
		return transferStatus;
	}
}