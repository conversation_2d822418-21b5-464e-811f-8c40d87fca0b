package tw.org.must.must.model.wrk.ipshare;

import org.springframework.stereotype.Component;
import tw.org.must.must.model.mbr.MbrIp;
import tw.org.must.must.model.mbr.MbrIpAgreement;
import tw.org.must.must.model.mbr.MbrIpAgreementTerritory;
import tw.org.must.must.model.mbr.MbrIpName;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class WrkWorkIpShareVO {

    private Map<String, MbrIpName> mbrIpNameMap;
    private Map<String,String> ipNameNoAndBaseNoMap;
    private Map<String, MbrIp> mbrIpByIpBaseNoMap;
    private Map<String, List<MbrIpAgreement>> mbrIpAgreementHashMap;
    private Map<String, List<MbrIpAgreementTerritory>> mbrIpAgreementTerritoryMap;
    private Map<String,List<String>> ipBaseNoAgrNoList;
    private Map<String,Map<String,String>> ipBaseNoAgrNoMap;
    private Long refWorkId;
    private Integer refWorkSoc;

    public WrkWorkIpShareVO(){
        this.ipNameNoAndBaseNoMap = new HashMap<>();
        this.mbrIpAgreementHashMap = new HashMap<>();
        this.mbrIpNameMap = new HashMap<>();
        this.mbrIpByIpBaseNoMap = new HashMap<>();
        this.mbrIpAgreementTerritoryMap = new HashMap<>();
        this.ipBaseNoAgrNoList = new HashMap<>();
        this.ipBaseNoAgrNoMap = new HashMap<>();
    }

    public Map<String, MbrIpName> getMbrIpNameMap() {
        return mbrIpNameMap;
    }

    public void setMbrIpNameMap(Map<String, MbrIpName> mbrIpNameMap) {
        this.mbrIpNameMap = mbrIpNameMap;
    }

    public Map<String, String> getIpNameNoAndBaseNoMap() {
        return ipNameNoAndBaseNoMap;
    }

    public void setIpNameNoAndBaseNoMap(Map<String, String> ipNameNoAndBaseNoMap) {
        this.ipNameNoAndBaseNoMap = ipNameNoAndBaseNoMap;
    }

    public Map<String, MbrIp> getMbrIpByIpBaseNoMap() {
        return mbrIpByIpBaseNoMap;
    }

    public void setMbrIpByIpBaseNoMap(Map<String, MbrIp> mbrIpByIpBaseNoMap) {
        this.mbrIpByIpBaseNoMap = mbrIpByIpBaseNoMap;
    }

    public Map<String, List<MbrIpAgreement>> getMbrIpAgreementHashMap() {
        return mbrIpAgreementHashMap;
    }

    public void setMbrIpAgreementHashMap(Map<String, List<MbrIpAgreement>> mbrIpAgreementHashMap) {
        this.mbrIpAgreementHashMap = mbrIpAgreementHashMap;
    }

    public Map<String, List<MbrIpAgreementTerritory>> getMbrIpAgreementTerritoryMap() {
        return mbrIpAgreementTerritoryMap;
    }

    public void setMbrIpAgreementTerritoryMap(Map<String, List<MbrIpAgreementTerritory>> mbrIpAgreementTerritoryMap) {
        this.mbrIpAgreementTerritoryMap = mbrIpAgreementTerritoryMap;
    }

    public Map<String, List<String>> getIpBaseNoAgrNoList() {
        return ipBaseNoAgrNoList;
    }

    public void setIpBaseNoAgrNoList(Map<String, List<String>> ipBaseNoAgrNoList) {
        this.ipBaseNoAgrNoList = ipBaseNoAgrNoList;
    }

    public Long getRefWorkId() {
        return refWorkId;
    }

    public void setRefWorkId(Long refWorkId) {
        this.refWorkId = refWorkId;
    }

    public Integer getRefWorkSoc() {
        return refWorkSoc;
    }

    public void setRefWorkSoc(Integer refWorkSoc) {
        this.refWorkSoc = refWorkSoc;
    }

    public Map<String, Map<String, String>> getIpBaseNoAgrNoMap() {
        return ipBaseNoAgrNoMap;
    }

    public void setIpBaseNoAgrNoMap(Map<String, Map<String, String>> ipBaseNoAgrNoMap) {
        this.ipBaseNoAgrNoMap = ipBaseNoAgrNoMap;
    }
}
