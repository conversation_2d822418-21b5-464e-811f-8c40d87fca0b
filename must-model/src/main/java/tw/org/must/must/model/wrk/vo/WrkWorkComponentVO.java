package tw.org.must.must.model.wrk.vo;

public class WrkWorkComponentVO {
	private String title;
	private String titleEn;
	private String genreCode;
	// ---------------------------------------
	private Long workId;
	private Integer workSocietyCode;
	private Integer durationM;
	private Integer durationS;
	private String usageType;
	private String workType ;

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	
	public String getTitleEn() {
		return titleEn;
	}

	public void setTitleEn(String titleEn) {
		this.titleEn = titleEn;
	}

	public Long getWorkId() {
		return workId;
	}

	public void setWorkId(Long workId) {
		this.workId = workId;
	}

 

	public Integer getWorkSocietyCode() {
		return workSocietyCode;
	}

	public void setWorkSocietyCode(Integer workSocietyCode) {
		this.workSocietyCode = workSocietyCode;
	}

	public String getGenreCode() {
		return genreCode;
	}

	public void setGenreCode(String genreCode) {
		this.genreCode = genreCode;
	}

	public Integer getDurationM() {
		return durationM;
	}

	public void setDurationM(Integer durationM) {
		this.durationM = durationM;
	}

	public Integer getDurationS() {
		return durationS;
	}

	public void setDurationS(Integer durationS) {
		this.durationS = durationS;
	}

	public String getUsageType() {
		return usageType;
	}

	public void setUsageType(String usageType) {
		this.usageType = usageType;
	}

	public String getWorkType() {
		return workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}
}