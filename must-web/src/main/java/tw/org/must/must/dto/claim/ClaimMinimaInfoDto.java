package tw.org.must.must.dto.claim;

import tw.org.must.must.common.base.Page;

public class ClaimMinimaInfoDto {

	private Page page;
	
	private String compnyName;
	
	private String productFullName;
	
	private String productShortName;

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public String getCompnyName() {
		return compnyName;
	}

	public void setCompnyName(String compnyName) {
		this.compnyName = compnyName;
	}

	public String getProductFullName() {
		return productFullName;
	}

	public void setProductFullName(String productFullName) {
		this.productFullName = productFullName;
	}

	public String getProductShortName() {
		return productShortName;
	}

	public void setProductShortName(String productShortName) {
		this.productShortName = productShortName;
	}
	
}
