package tw.org.must.must.dto.listmatch;

import io.swagger.annotations.ApiModelProperty;
import tw.org.must.must.common.base.Page;

public class ListMatchDataDspDto{

	private Page page;

	@ApiModelProperty(value = "Fid", required = true)
	private Long fileBaseId;

	@ApiModelProperty(value = "标题（模糊查询）")
	private String title;

	@ApiModelProperty(value = "非空标题（复选框）")
	private Boolean titleNonNull;

	@ApiModelProperty(value = "分位数区间-开始, [0,10)")
	private Integer quantileBegin;

	@ApiModelProperty(value = "分位数区间-结束, (0,10]")
	private Integer quantileEnd;

	@ApiModelProperty(value = "匹配分数等级, -1：(0,10)，0：[10,20]，1：(20, )")
	private Integer matchScoreLevel;

	@ApiModelProperty(value = "匹配状态, 0待审核，1审核通过，2审核拒绝， 3.人工审核，4.侵权回填，5.customerId自动生成")
	private Integer status;

	@ApiModelProperty(value = "编号范围开始，整数>0")
	private Long batchIdA;

	@ApiModelProperty(value = "编号范围结束，需要大于等于batchIdA")
	private Long batchIdB;

	@ApiModelProperty(value = "去重md5值")
	private String dataUniqueKey;

	@ApiModelProperty(value = "点击次数")
	private Long clickNumber;

	public String getDataUniqueKey() {
		return dataUniqueKey;
	}

	public void setDataUniqueKey(String dataUniqueKey) {
		this.dataUniqueKey = dataUniqueKey;
	}

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public Long getFileBaseId() {
		return fileBaseId;
	}

	public void setFileBaseId(Long fileBaseId) {
		this.fileBaseId = fileBaseId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Boolean getTitleNonNull() {
		return titleNonNull;
	}

	public void setTitleNonNull(Boolean titleNonNull) {
		this.titleNonNull = titleNonNull;
	}

	public Integer getQuantileBegin() {
		return quantileBegin;
	}

	public void setQuantileBegin(Integer quantileBegin) {
		this.quantileBegin = quantileBegin;
	}

	public Integer getQuantileEnd() {
		return quantileEnd;
	}

	public void setQuantileEnd(Integer quantileEnd) {
		this.quantileEnd = quantileEnd;
	}

	public Integer getMatchScoreLevel() {
		return matchScoreLevel;
	}

	public void setMatchScoreLevel(Integer matchScoreLevel) {
		this.matchScoreLevel = matchScoreLevel;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Long getBatchIdA() {
		return batchIdA;
	}

	public void setBatchIdA(Long batchIdA) {
		this.batchIdA = batchIdA;
	}

	public Long getBatchIdB() {
		return batchIdB;
	}

	public void setBatchIdB(Long batchIdB) {
		this.batchIdB = batchIdB;
	}

	public Long getClickNumber() {
		return clickNumber;
	}

	public void setClickNumber(Long clickNumber) {
		this.clickNumber = clickNumber;
	}
}
