package tw.org.must.must.dto.listmatch;

import tw.org.must.must.common.base.Page;

import java.util.Date;

public class ListMatchDataDspMatchHistoryLogDto {

    private Page page;

    private Long workIdA;

    private Long workIdB;

    private Integer workSoc;

    private String dataUniqueKey;

    private String resourceId;

    private Date createTimeA;

    private Date createTimeB;

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public Long getWorkIdA() {
        return workIdA;
    }

    public void setWorkIdA(Long workIdA) {
        this.workIdA = workIdA;
    }

    public Long getWorkIdB() {
        return workIdB;
    }

    public void setWorkIdB(Long workIdB) {
        this.workIdB = workIdB;
    }

    public Integer getWorkSoc() {
        return workSoc;
    }

    public void setWorkSoc(Integer workSoc) {
        this.workSoc = workSoc;
    }

    public String getDataUniqueKey() {
        return dataUniqueKey;
    }

    public void setDataUniqueKey(String dataUniqueKey) {
        this.dataUniqueKey = dataUniqueKey;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public Date getCreateTimeA() {
        return createTimeA;
    }

    public void setCreateTimeA(Date createTimeA) {
        this.createTimeA = createTimeA;
    }

    public Date getCreateTimeB() {
        return createTimeB;
    }

    public void setCreateTimeB(Date createTimeB) {
        this.createTimeB = createTimeB;
    }
}
