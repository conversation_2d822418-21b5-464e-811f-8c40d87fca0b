package tw.org.must.must.dto.listmatch;

import tw.org.must.must.common.base.Page;
import tw.org.must.must.model.list.ListMatchDataBasic;

public class ListMatchDto {

	private Page page;

	private ListMatchDataBasic listMatchDataBasic;

	public Page getPage() {
		return page;
	}

	public void setPage(Page page) {
		this.page = page;
	}

	public ListMatchDataBasic getListMatchDataBasic() {
		return listMatchDataBasic;
	}

	public void setListMatchDataBasic(ListMatchDataBasic listMatchDataBasic) {
		this.listMatchDataBasic = listMatchDataBasic;
	}

}
