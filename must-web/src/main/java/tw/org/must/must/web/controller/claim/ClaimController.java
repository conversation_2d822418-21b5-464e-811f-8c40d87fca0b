package tw.org.must.must.web.controller.claim;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.common.util.result.SuccessResult;
import tw.org.must.must.core.service.claim.*;
import tw.org.must.must.dto.claim.ClaimMinimaInfoDto;
import tw.org.must.must.model.claim.*;
import tw.org.must.must.model.claim.vo.ClaimProductDto;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

@Api(tags = "Claim管理")
@RestController
@RequestMapping("/claim")
public class ClaimController {

    @Autowired
    private ClaimSetInfoService claimSetInfoService;

    @Autowired
    private ClaimMinimaInfoService claimMinimaInfoService;

    @Autowired
    private ClaimSignMemberService claimSignMemberService;

    @Autowired
    private ClaimProductService claimProductService;

    @Autowired
    private ClaimProductConfigService claimProductConfigService;
    
    @ApiOperation(value = "获取SetInfo列表")
    @GetMapping("")
    public ResponseEntity getClaimSetInfo(@RequestParam(value = "company", required = false) String company,
                                          @RequestParam(value = "type", required = false) String type,
                                          @RequestParam(value = "startTime", required = false) String startTime,
                                          @RequestParam(value = "endTime", required = false) String endTime,
                                          @RequestParam(value = "status", required = false) Integer status,
                                          @RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
                                          @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<ClaimSetInfo> list = claimSetInfoService.listByCompanyAndTime(company, type, startTime, endTime,status);
        return new ResponseEntity<>(new PageInfo<>(list), HttpStatus.OK);
    }


    @ApiOperation(value = "保存分配SetInfo信息")
    @PostMapping("/set")
    public ResponseEntity saveClaimSetInfo(@RequestBody ClaimSetInfo claimSetInfo) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        simpleDateFormat.setLenient(false);
        try {
            simpleDateFormat.parse(claimSetInfo.getMechanicalStartTime());
            simpleDateFormat.parse(claimSetInfo.getMechanicalEndTime());
            simpleDateFormat.parse(claimSetInfo.getPublicTransmissionStartTime());
            simpleDateFormat.parse(claimSetInfo.getPublicTransmissionEndTime());
        } catch (ParseException e) {
            throw new MustException(ResultCode.DATAFORMAT_FAIL);
        }
        claimSetInfoService.editClaimSetInfo(claimSetInfo);

        return ResponseEntity.ok(new SuccessResult<>(claimSetInfo));

    }
    @ApiOperation(value = "保存分配MinimaInfo信息")
    @PostMapping("/minima")
    public ResponseEntity saveClaimMinimaInfo(@RequestBody ClaimMinimaInfo claimMinimaInfo) {
    	if(null != claimMinimaInfo.getPublicShare() && null != claimMinimaInfo.getMechanicalShare()) {
            claimMinimaInfo.setTotalShare(claimMinimaInfo.getPublicShare().add(claimMinimaInfo.getMechanicalShare()));
        }
        if(claimMinimaInfo.getFormulaType() ==null){
            claimMinimaInfo.setFormulaType(1);
        }
        ClaimMinimaInfo  claimMinimaInfo1 = claimMinimaInfoService.saveClaimMinimaInfo(claimMinimaInfo);
        return new ResponseEntity<>(claimMinimaInfo1,HttpStatus.OK);
    }

    @ApiOperation(value = "获取分配Min imaInfo信息")
    @GetMapping("/minima")
    public ResponseEntity getClaimMinimaInfo( @RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
                                              @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                              @RequestParam(value = "setId", required = true) Long setId,
                                             @RequestParam(value = "productName", required = false) String productName) {
        ClaimMinimaInfo claimMinimaInfo = new ClaimMinimaInfo();
        claimMinimaInfo.setClaimSetId(setId);
        List<ClaimMinimaInfo> list = claimMinimaInfoService.listClaimMinimaInfoWithPage(pageNum,pageSize,setId,productName);
        return new ResponseEntity<>(new PageInfo<>(list), HttpStatus.OK);
    }

    @ApiOperation(value = "编辑补充特殊minima")
    @PostMapping("/editMinima")
    public ResponseEntity editMinima(@RequestBody List<ClaimMinimaInfo> claimMinimaInfoList) {
        claimMinimaInfoService.editMinima(claimMinimaInfoList);
        return new ResponseEntity<>(claimMinimaInfoList, HttpStatus.OK);
    }
    @ApiOperation(value = "删除MinimaInfo信息")
    @DeleteMapping("/minima")
    public ResponseEntity DelClaimMinimaInfo(@RequestParam("id") Long id) {
        return new ResponseEntity<>(claimMinimaInfoService.delete(id),HttpStatus.OK);
    }
    @ApiOperation(value = "获取MinimaInfo的产品名称列表")
    @GetMapping("/minima/product")
    public ResponseEntity getProductShortNameWithSetId(@RequestParam("setId") Long setId) {
        List<ClaimProductDto> formatProductList = claimMinimaInfoService.getProductFormatWithSetId(setId);
        return new ResponseEntity<>(formatProductList,HttpStatus.OK);
    }

    @ApiOperation(value = "保存分配SignMember信息")
    @PostMapping("/sign_member")
    public ResponseEntity saveClaimSignMember(@RequestBody ClaimSignMember claimSignMember) {
        ClaimSignMember claimSignMember1 = claimSignMemberService.saveClaimSignMember(claimSignMember);
        return new ResponseEntity<>(claimSignMember1,HttpStatus.OK);
    }

    @ApiOperation(value = "获取分配SignMember信息")
    @GetMapping("/sign_member")
    public ResponseEntity getClaimSignMember( @RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
                                              @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                              @RequestParam(value = "setId") Long setId,
                                              @RequestParam(value = "publisher", required = false) String publisher,
                                              @RequestParam(value = "ipBaseNo", required = false) String ipBaseNo,
                                              @RequestParam(value = "nameNo", required = false) String nameNo,
                                              @RequestParam(value = "socNo", required = false) String socNo) {

        List<ClaimSignMember> list = claimSignMemberService.listClaimSignMemberWithPage(pageNum,pageSize,setId,publisher,ipBaseNo,nameNo,socNo);
        return new ResponseEntity<>(new PageInfo<>(list), HttpStatus.OK);
    }
    @ApiOperation(value = "删除SignMember信息")
    @DeleteMapping("/sign_member")
    public ResponseEntity delClaimSignMember(@RequestParam("id") Long id) {
        claimSignMemberService.delete(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "获取产品信息")
    @GetMapping("/product")
    public ResponseEntity getClaimProducts() {
        List<ClaimProduct> list = claimProductService.listAll();
        return new ResponseEntity<>(list, HttpStatus.OK);
    }

    @ApiOperation(value = "保存产品信息")
    @PostMapping("/product")
    public ResponseEntity saveClaimProducts(@RequestBody ClaimProduct claimProduct) {
        claimProductService.save(claimProduct);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "删除产品信息")
    @DeleteMapping("/product")
    public ResponseEntity deleteClaimProducts(@RequestParam("id") Long id) {
        claimProductService.delete(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "获取产品配置信息列表")
    @GetMapping("/product/{proId}/config")
    public ResponseEntity getClaimProductConfigs(@PathVariable(value = "proId") Long proId) {
        ClaimProductConfig claimProductConfig = new ClaimProductConfig();
        claimProductConfig.setProId(proId);
        List<ClaimProductConfig> list = claimProductConfigService.list(claimProductConfig);
        return new ResponseEntity<>(list, HttpStatus.OK);
    }

    @ApiOperation(value = "获取产品配置信息")
    @GetMapping("/product/config/{id}")
    public ResponseEntity getClaimProductConfig(@PathVariable("id") Long id) {
        ClaimProductConfig claimProductConfig = claimProductConfigService.getById(id);
        return new ResponseEntity<>(claimProductConfig, HttpStatus.OK);
    }


    @ApiOperation(value = "保存产品配置信息")
    @PostMapping("/product/config")
    public ResponseEntity saveClaimProductConfigs(@RequestBody ClaimProductConfig claimProductConfig) {
        claimProductConfigService.save(claimProductConfig);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "删除产品配置信息")
    @DeleteMapping("/product/config")
    public ResponseEntity deleteClaimProductConfigs(@RequestParam("id") Long id) {
        claimProductConfigService.delete(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    
    @ApiOperation(value = "获取claimMinInfoAndSetInfo信息")
    @ApiImplicitParams({
		@ApiImplicitParam(value = "参数claimMinimaInfoDto", name = "claimMinimaInfoDto", dataType = "ClaimMinimaInfoDto", paramType = "query")
		})
    	@RequestMapping(value = "/getClaimMinmaInfoList", produces = "application/json;charset=utf-8", method = RequestMethod.POST)
    public ResponseEntity<PageInfo<ClaimMinimaInfo>> getClaimMinmaInfoList(@RequestBody ClaimMinimaInfoDto claimMinimaInfoDto) {
    	List<ClaimMinimaInfo> claimMinimaInfoList = claimMinimaInfoService.getClaimMinmaInfoList(claimMinimaInfoDto.getPage(),claimMinimaInfoDto.getCompnyName(),claimMinimaInfoDto.getProductFullName(),claimMinimaInfoDto.getProductShortName());
    	PageInfo<ClaimMinimaInfo> pageInfo = new PageInfo<ClaimMinimaInfo>(claimMinimaInfoList);
		return new ResponseEntity<PageInfo<ClaimMinimaInfo>>(pageInfo,HttpStatus.OK);
    }
    
}
