package tw.org.must.must.web.controller.list;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import tw.org.must.must.common.base.Page;
import tw.org.must.must.common.util.CommonUtils;
import tw.org.must.must.common.util.result.Result;
import tw.org.must.must.core.service.dist.DistParamCategoryService;
import tw.org.must.must.core.service.list.ListBasicFileBaseService;
import tw.org.must.must.core.service.list.ListCategoryService;
import tw.org.must.must.core.service.list.ListFileQueueService;
import tw.org.must.must.core.service.new_list.List2BasicFileBaseService;
import tw.org.must.must.core.service.new_list.List2FileQueueService;
import tw.org.must.must.model.dist.DistParamCategory;
import tw.org.must.must.model.list.ListBasicFileBase;
import tw.org.must.must.model.list.ListCategory;
import tw.org.must.must.model.list.ListFileQueue;
import tw.org.must.must.model.list.vo.ListBasicFileBaseParamVo;
import tw.org.must.must.model.list.vo.ListBasicFileSingleBaseParamVo;
import tw.org.must.must.model.new_list.List2BasicFileBase;
import tw.org.must.must.model.new_list.List2FileQueue;

import java.util.List;
import java.util.Objects;

@Api(tags = "清单文件管理")
@RestController
@RequestMapping("/list2BasicFileBase")
public class List2BasicFileBaseController {

	@Autowired
	private List2BasicFileBaseService listBasicFileBaseService;
	@Autowired
	private ListCategoryService listCategoryService;
	@Lazy
	@Autowired
	private List2FileQueueService listFileQueueService;

	@Autowired
	private DistParamCategoryService distParamCategoryService ;
	
	@ApiOperation(value = "获取新一般清单文件列表")
	@ApiImplicitParams({
		@ApiImplicitParam(value = "参数对象映射-ListBasicFileBaseParamVo", name = "lbfbpv", dataType = "ListBasicFileBaseParamVo", paramType = "query")})
	@RequestMapping(value = "/getList2BasicFileBaseList", produces = "application/json;charset=utf-8", method = RequestMethod.POST)
	public Result<PageInfo<List2BasicFileBase>> getListBasicFileBaseList(@RequestBody ListBasicFileBaseParamVo lbfbpv) {
		Page page = lbfbpv.getPage();
		if (null == page) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數不正確！");
		}
		PageHelper.startPage(page.getPageNum(), page.getPageSize());
		List<List2BasicFileBase> lfqList = listBasicFileBaseService.getListBasicFileBaseList(lbfbpv.getFid(), lbfbpv.getFileQueueId(),
				lbfbpv.getCategoryCode(), lbfbpv.getTitle(), lbfbpv.getUploadType());
		PageInfo<List2BasicFileBase> pageInfo = new PageInfo<>(lfqList);
		return new Result(HttpStatus.OK.value(), null, pageInfo);
	}

	@ApiOperation(value = "标记是否可分配接口")
	@PostMapping(value = "/updateIsDist")
	public Result<String> updateIsDist(@RequestBody ListBasicFileBaseParamVo lbfbpv) {
		if (Objects.isNull(lbfbpv) || Objects.isNull(lbfbpv.getFid()) || lbfbpv.getFid() < 1L || !StringUtils.equalsAny(lbfbpv.getIsDist(), "Y", "N") || StringUtils.isBlank(lbfbpv.getCategoryCode())) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數不正確！");
		}
		List2BasicFileBase listBasicFileBase = new List2BasicFileBase();
		listBasicFileBase.setId(lbfbpv.getFid());
		listBasicFileBase.setDist(lbfbpv.getIsDist());
		String message = null;
		Integer result = 0;
		try {
			result = listBasicFileBaseService.updateSelective(listBasicFileBase);
			if(StringUtils.equals("Y", listBasicFileBase.getDist())) {
				//更新为可分配的情况下，需要把categoryCode同步
				listCategoryService.generateSingleSessionCategoryCode(lbfbpv.getCategoryCode());
			}
		} catch (Exception e) {
			message = e.getMessage();
			e.printStackTrace();
		}
		if(result == 1) {
			return new Result(HttpStatus.OK.value(), "更新成功！");
		}else {
			return new Result(HttpStatus.OK.value(), message);
		}
	}

	@ApiOperation(value = "更新Category code")
	@PostMapping(value = "/updateCategoryCode")
	public Result<String> updateCategoryCode(@RequestBody List2BasicFileBase lbfb) {
		if (Objects.isNull(lbfb) || Objects.isNull(lbfb.getId())) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數不正確！");
		}
		List2BasicFileBase listBasicFileBase = listBasicFileBaseService.getById(lbfb.getId());
		if(Objects.isNull(listBasicFileBase)) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "该base数据不存在！");
		}
		List2FileQueue queue = listFileQueueService.getById(listBasicFileBase.getFileQueueId());
		if(Objects.isNull(queue)) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "该base对应的queue列表数据不存在！");
		}

		String categoryCode = lbfb.getCategoryCode();

	 	List<DistParamCategory> distParamCategoryList = distParamCategoryService.getDistParamCategoryWithStatus(categoryCode);
		for(DistParamCategory distParamCategory :distParamCategoryList){
			if(distParamCategory.getDistStatus() == 7){
				// a.startTime > b.endTime || a.endTime < b.startTime a、b时间不重合
				if(distParamCategory.getListStartTime() == null || distParamCategory.getListEndTime() == null){
					continue;
				}
				if(!(listBasicFileBase.getListFileStartTime().after(distParamCategory.getListEndTime())
						|| listBasicFileBase.getListFileEndTime().before(distParamCategory.getListStartTime())) ){
					return new Result(HttpStatus.BAD_REQUEST.value(), "存在已经CROSS CHECK的分配：" + distParamCategory.getDistNo() + "，不能修改！");
				}
			}
		}

		List<ListCategory> list = listCategoryService.getListCategoryByCode(categoryCode);
		if(CollectionUtils.isEmpty(list)){
			return new Result(HttpStatus.BAD_REQUEST.value(), "Category Code " + categoryCode + "不存在！");
		}

		lbfb.setCategoryCode(categoryCode);
		Integer result = listBasicFileBaseService.updateSelective(lbfb);
		if(result == 1) {
			return new Result(HttpStatus.OK.value(), "更新成功！");
		}else {
			return new Result(HttpStatus.OK.value(), "更新失败！");
		}
	}

	@ApiOperation(value = "更新base")
	@PostMapping(value = "/updateBase")
	public Result<String> updateBase(@RequestBody List2BasicFileBase lbfb) {
		if (Objects.isNull(lbfb) || Objects.isNull(lbfb.getId())) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數不正確！");
		}
		List2BasicFileBase listBasicFileBase = listBasicFileBaseService.getById(lbfb.getId());
		if(Objects.isNull(listBasicFileBase)) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "该base数据不存在！");
		}
		List2FileQueue queue = listFileQueueService.getById(listBasicFileBase.getFileQueueId());
		if(Objects.isNull(queue)) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "该base对应的queue列表数据不存在！");
		}
		String categoryCode = queue.getCategoryCode();
		if(StringUtils.isBlank(categoryCode)) {
			lbfb.setCategoryCode("");
		}else {
			String uploadType = lbfb.getUploadType();
			if(categoryCode.contains("-CJ") || categoryCode.contains("-PG")){
				categoryCode = categoryCode.replaceAll("-CJ|-PG","");
			}
			if(StringUtils.equalsAnyIgnoreCase(uploadType, "CJ", "PG")) {
				categoryCode = String.format("%s-%s", categoryCode, uploadType);
				if (!listCategoryService.checkListCategoryExistOrNot(categoryCode)) {
					return new Result(HttpStatus.BAD_REQUEST.value(), String.format("修改失敗，categoryCode【%s】不存在！請手動創建對應的categoryCode后再重試", categoryCode));
				}
			}
			lbfb.setCategoryCode(categoryCode);
		}
		Integer result = listBasicFileBaseService.updateBase(lbfb);
		if(result == 1) {
			return new Result(HttpStatus.OK.value(), "更新成功！");
		}else {
			return new Result(HttpStatus.OK.value(), "更新失败！");
		}
	}


	@ApiOperation(value = "获取单场次清单base")
	@PostMapping(value = "/getSingleListBasicFileBaseList")
	public Result<PageInfo<List2BasicFileBase>> getSingleListBasicFileBaseList(@RequestBody ListBasicFileSingleBaseParamVo vo) {
		if(null == vo.getType()) {
			vo.setType(0); // 默认单场次
		}
		Page page = vo.getPage();
		if(null == page || page.getPageNum() < 1 || page.getPageSize() < 5) {
			page = new Page(1,10);
		}
		PageHelper.startPage(page.getPageNum(), page.getPageSize());
		PageInfo<List2BasicFileBase> pageInfo = new PageInfo<>(listBasicFileBaseService.getSingleListBasicFileBaseList(vo.getCategoryCode(), vo.getPerDate(), vo.getType()));
		return new Result(HttpStatus.OK.value(), null, pageInfo);
	}

	@ApiOperation(value = "更新concertTitle")
	@PostMapping(value = "/updateConcertTitle")
	public Result<String> updateConcertTitle(@RequestBody List2BasicFileBase lbfb) {
		if (Objects.isNull(lbfb) || Objects.isNull(lbfb.getId())) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數不正確！");
		}
		List2BasicFileBase listBasicFileBase = listBasicFileBaseService.getById(lbfb.getId());
		if(Objects.isNull(listBasicFileBase)) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "该base数据不存在！");
		}
		if(StringUtils.isBlank(lbfb.getTitle())) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "活動名稱不能爲空！");
		}
		if(null == lbfb.getListFileStartTime()) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "活動日期不能爲空！");
		}
		if(StringUtils.isBlank(lbfb.getSourceCompany())) {
			return new Result(HttpStatus.BAD_REQUEST.value(), "主辦方不能爲空！");
		}

		String defaultConcertTitle = "{sourceCompany} {listFileStartTime} 主辦「{title}」 授權曲目{workName}，共{session}場，共{numberOfLines}首，共{clickNumber}次";
		if(StringUtils.isBlank(lbfb.getConcertTitleTemplate())) {
			lbfb.setConcertTitleTemplate(defaultConcertTitle); //默认值
		}else {
			//校验大括号中的字段名是存在
			List<String> strings = CommonUtils.regexMatch(lbfb.getConcertTitleTemplate(), "\\{(.*?)}", 1);
			List<String> deflauts = CommonUtils.regexMatch(defaultConcertTitle, "\\{(.*?)}", 1);
			strings.removeAll(deflauts);
			if(!strings.isEmpty()) {
				return new Result<>(HttpStatus.BAD_REQUEST.value(), String.format("concertTitle不支持該字段表達式，【%s】！", String.join(", ", strings)));
			}
		}
		Integer result = listBasicFileBaseService.updateConcertTitle(lbfb);
		if(result == 1) {
			return new Result(HttpStatus.OK.value(), "更新成功！");
		}else {
			return new Result(HttpStatus.OK.value(), "更新失败！");
		}
	}

	
}
