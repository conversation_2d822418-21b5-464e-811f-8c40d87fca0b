package tw.org.must.must.web.controller.list;

import cn.miludeer.jsoncode.JsonCode;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import tw.org.must.must.common.util.result.Result;
import tw.org.must.must.core.service.list.ListFileQueueService;
import tw.org.must.must.core.service.new_list.List2FileQueueService;
import tw.org.must.must.model.list.ListFileQueue;
import tw.org.must.must.model.new_list.List2FileQueue;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.Objects;

//@Api(tags = "清单队列管理")
@RestController
@RequestMapping("/list2FileQueue")
public class List2FileQueueController {

    @Autowired
    private List2FileQueueService listFileQueueService;

//
//	@ApiOperation(value = "获取清单队列列表")
//	@ApiImplicitParams({
//		@ApiImplicitParam(value = "参数对象映射-ListFileQueueParamVo", name = "lfqpv", dataType = "ListFileQueueParamVo", paramType = "query")})
//	@RequestMapping(value = "/getListFileQueueList", produces = "application/json;charset=utf-8", method = RequestMethod.POST)
//	public ResponseEntity<PageInfo<ListFileQueue>> getListFileQueueList(@RequestBody ListFileQueueParamVo lfqpv){
//		Page page = lfqpv.getPage();
//		if(null == page)
//			return new ResponseEntity<PageInfo<ListFileQueue>>(HttpStatus.BAD_REQUEST);
//		PageHelper.startPage(page.getPageNum(),page.getPageSize());
//		List<ListFileQueue> lfqList = new ArrayList<ListFileQueue>();
//		lfqList = listFileQueueService.getListFileQueueList(lfqpv.getCategoryCode(),lfqpv.getStatus(),lfqpv.getUploadType(),lfqpv.getUploadUserName(),lfqpv.getFileName(),lfqpv.getFileType());
//		PageInfo<ListFileQueue> pageInfo = new PageInfo<ListFileQueue>(lfqList);
//		return new ResponseEntity<PageInfo<ListFileQueue>>(pageInfo,HttpStatus.OK);
//	}


    @ApiOperation("value = 下载error解析文件")
    @PostMapping(value = "downloadErrorFile")
    public void downloadErrorFile(@RequestParam(value = "id") Long id, HttpServletResponse resp) throws IOException {
        List2FileQueue lfq = listFileQueueService.getById(id);
        String extJson = lfq.getExtJson();
        if (StringUtils.isBlank(extJson)) return;
        String filePath = JsonCode.getValue(lfq.getExtJson(), "$.err");
        if (StringUtils.isBlank(filePath)) return;

        //提供下载文件前进行压缩，即服务端生成压缩文件
        File file = new File(filePath);
        /*FileOutputStream fos = new FileOutputStream(file);
        this.toZip(filePath, fos, true);*/
        //获取要下载的文件名
        String fileName = filePath.substring(filePath.lastIndexOf(File.separator) + 1);
        InputStream in = null;
        OutputStream out = null;
        try {
            fileName = new String(fileName.getBytes("utf-8"), "ISO-8859-1");
            //fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            System.out.println(fileName);
            resp.reset();
            resp.setCharacterEncoding("UTF-8");
            resp.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            //设置content-disposition响应头控制浏览器以下载的形式打开文件
            resp.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            System.out.println("file size: " + file.length());
            in = new FileInputStream(filePath);
            int len = 0;
            byte[] buffer = new byte[1024];
            out = resp.getOutputStream();
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != in) {
                in.close();
            }
            out.flush();
            out.close();
        }
    }


    @ApiOperation("value = 重新解析")
    @GetMapping(value = "reparse/{id}")
    public Result<String> reparse(@PathVariable("id") Long id) {
        int code = HttpStatus.OK.value();
        String message = "";
        if (Objects.isNull(id) || id <= 0L) {
            code = HttpStatus.BAD_REQUEST.value();
            message = "請求參數錯誤：id不能為空！";
        } else {
            List2FileQueue lfq = listFileQueueService.getById(id);
            if (Objects.isNull(lfq)) {
                code = HttpStatus.BAD_REQUEST.value();
                message = "請求參數錯誤：該id查詢不到對應的清單數據！";
            } else {
                lfq.setStatus(0);
                listFileQueueService.updateSelective(lfq);
            }
        }
        return new Result<>(code, message);
    }

    @ApiOperation("value = 批量重新解析")
    @GetMapping(value = "reparseList")
    public Result<String> reparse(@RequestParam(value = "ids") List<Long> ids) {
        int code = HttpStatus.OK.value();
        String message = "";
        if (CollectionUtils.isEmpty(ids)) {
            code = HttpStatus.BAD_REQUEST.value();
            message = "請求參數錯誤：id不能為空！";
        } else {
            List<List2FileQueue> queues = listFileQueueService.listByIds(ids);
            queues.stream().filter(x -> x.getStatus() == 3).forEach(t -> {
                t.setStatus(0);
                t.init();
            });
            listFileQueueService.updateBatchByPrimaryKeySelective(queues);
        }
        return new Result<>(code, message);
    }


}
