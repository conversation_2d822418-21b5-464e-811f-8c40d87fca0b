package tw.org.must.must.web.controller.listmatch;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.Sheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tw.org.must.must.common.base.Page;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.util.CommonUtils;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.common.util.Md5;
import tw.org.must.must.common.util.excel.ExcelUtil;
import tw.org.must.must.common.util.excel.GenerateExcelUtil;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.Result;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.common.util.result.SuccessResult;
import tw.org.must.must.core.service.list.ListMatchDataOverseasUniqService;
import tw.org.must.must.core.service.listoverseas.ListMatchDataOverseasMappingService;
import tw.org.must.must.core.service.listoverseas.ListMatchDataOverseasMatchWorkService;
import tw.org.must.must.core.service.listoverseas.ListMatchDataOverseasService;
import tw.org.must.must.core.service.listoverseas.ListOverseasFileBaseService;
import tw.org.must.must.dto.listmatch.*;
import tw.org.must.must.model.list.ListMatchDataBasic;
import tw.org.must.must.model.list.ListMatchDataOverseasUniq;
import tw.org.must.must.model.listoverseas.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static tw.org.must.must.common.constants.Constants.BATCH_SIZE_1000;
import static tw.org.must.must.common.constants.Constants.BATCH_SIZE_10000;

@Api(tags = "O分配-比对数据审核")
@RestController
@RequestMapping("/listmatchoverseas")
public class ListMatchDataOverseasController {

    private static final Logger log = LoggerFactory.getLogger(ListMatchDataOverseasController.class);

    @Autowired
    private ListMatchDataOverseasService listMatchDataOverseasService;
    @Autowired
    private ListMatchDataOverseasUniqService listMatchDataOverseasUniqService;
    @Autowired
    private ListMatchDataOverseasMappingService listMatchDataOverseasMappingService;
    @Autowired
    private ListMatchDataOverseasMatchWorkService listMatchDataOverseasMatchWorkService;

    @Autowired
    private ListOverseasFileBaseService listOverseasFileBaseService;

/*    @ApiOperation("删除Mapping数据")
    @DeleteMapping("delete")
    public Result<Integer> delete(@RequestParam(value = "id") Long id) {
        if (Objects.isNull(id) || id < 1L) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "請求參數不正確！");
        }
        Integer delete = listMatchDataOverseasService.deleteManully(id);
        return new Result<>(HttpStatus.OK.value(), "刪除成功！", delete);
    }*/

    @ApiOperation(value = "获取待审核列表（自动匹配的）")
    @PostMapping("getListMatchDataOverseasList")
    public Result<PageInfo<ListMatchDataOverseasUniq>> getListMatchDataOverseasList(
            @RequestBody ListMatchDataOverseasDto listMatchDataOverseasDto) {
        Page page = listMatchDataOverseasDto.getPage();
        ListMatchDataOverseas listMatchDataOverseas = listMatchDataOverseasDto.getListMatchDataOverseas();
        //从去重表拿数据
        PageInfo<ListMatchDataOverseasUniq> list = listMatchDataOverseasUniqService.getListMatchDataOverseasUniqList(page,listMatchDataOverseas);
        List<ListMatchDataOverseasUniq> listMatchDataOverseasUniqList = list.getList();
        if(!CollectionUtils.isEmpty(listMatchDataOverseasUniqList)){
            List<String> dataUniqueKeys = listMatchDataOverseasUniqList.stream().filter(l -> l.getStatus() > 0).map(ListMatchDataOverseasUniq :: getDataUniqueKey).collect(Collectors.toList());
            Map<String, ListMatchDataOverseas> map = listMatchDataOverseasService.getMapByMd5List(dataUniqueKeys);
            for(ListMatchDataOverseasUniq listMatchDataOverseasUniq : listMatchDataOverseasUniqList){
                if(listMatchDataOverseasUniq.getStatus() > 0){
                    if(map.containsKey(listMatchDataOverseasUniq.getDataUniqueKey())){
                        listMatchDataOverseasUniq.setMatchType(map.get(listMatchDataOverseasUniq.getDataUniqueKey()).getMatchType());
                    } else {
                        listMatchDataOverseasUniq.setMatchType("Y");
                    }
                }
            }
        }

        return new Result(HttpStatus.OK.value(), null, list);
    }

    @ApiOperation(value = "获取IP审核中的作品数据")
    @PostMapping("getListMatchDataOverseasListForIpAudit")
    public Result<PageInfo<ListMatchDataOverseasUniq>> getListMatchDataOverseasListForIpAudit( @RequestBody ListMatchOverseasIpDto param) {
        //从去重表拿数据
        PageInfo<ListMatchDataOverseasUniq> list = listMatchDataOverseasUniqService.getListMatchDataOverseasUniqList(param.getPage(),
                param.getMatchWorkId(),param.getMatchWorkSoc(),param.getMatchWorkTitle(),param.getDistributedIp(),param.getDistributedIpName(),
                param.getMatchIpSoc(),param.getRemitSoc(),param.getRemitIpName(),param.getRemitWorkTitle(),param.getDataUniqueKey(),param.getBatch(),
                param.getfId(),param.getRecordType());
        return new Result(HttpStatus.OK.value(), null, list);
    }

    @ApiOperation(value = "O清單作品審核，查看明細")
    @GetMapping("getUnCheckedListMatchDataOverseasList")
    public ResponseEntity getUnCheckedListMatchDataOverseasList(@RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
                                                                @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                                                @RequestParam(value = "fileBaseId", required = false) Long fileBaseId,
                                                                @RequestParam(value = "remitSociety", required = false) Integer remitSociety,
                                                                @RequestParam(value = "sourceWorkCode", required = false) String sourceWorkCode,
                                                                @RequestParam(value = "originalTitle", required = false) String originalTitle,
                                                                @RequestParam(value = "dataUniqueKey", required = false) String dataUniqueKey,
                                                                @RequestParam(value = "status", required = false) Integer status) {
        PageHelper.startPage(pageNum, pageSize);
        List<ListMatchDataOverseas> list = listMatchDataOverseasService.getUnCheckedListMatchDataOverseasList(fileBaseId, remitSociety, sourceWorkCode, originalTitle, status, dataUniqueKey);
        return ResponseEntity.ok(new SuccessResult(new PageInfo<>(list)));
    }

    @ApiOperation(value = "审核特殊的作品")
    @ApiImplicitParams({@ApiImplicitParam(value = "标识返回值：1：全审，0：部分审", name = "return", dataType = "Integer", paramType = "return")})
    @PostMapping("checkSpecialOverseasList")
    public ResponseEntity checkSpecialOverseasList(@RequestBody CheckSpecialListMatchDataOverseasDto checkSpecialListMatchDataOverseasDto) {
        Integer status = checkSpecialListMatchDataOverseasDto.getStatus();
        Long workTitleId = checkSpecialListMatchDataOverseasDto.getWorkTitleId();
        List<ListMatchDataOverseas> listMatchDataOverseasList = checkSpecialListMatchDataOverseasDto.getListMatchDataOverseas();
        return ResponseEntity.ok(new SuccessResult(listMatchDataOverseasService.checkSpecialOverseasList(status, workTitleId, listMatchDataOverseasList)));
    }


    @ApiOperation(value = "获取待审核列表（手动添加的）")
    @PostMapping("getManualListMatchDataOverseasList")
    public Result<PageInfo<ListMatchDataOverseas>> getManualListMatchDataOverseasList(
            @RequestBody ListMatchDataOverseasDto listMatchDataOverseasDto) {
        Page page = listMatchDataOverseasDto.getPage();
        ListMatchDataOverseas listMatchDataOverseas = listMatchDataOverseasDto.getListMatchDataOverseas();
        if (Objects.isNull(listMatchDataOverseas.getFileBaseId())) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數FID不能為空！");
        }

        ListOverseasFileBase listOverseasFileBase = listOverseasFileBaseService.getById(listMatchDataOverseas.getFileBaseId());
        if(listOverseasFileBase == null){
            return new Result(HttpStatus.BAD_REQUEST.value(), "FID不存在！");
        }

        if(!StringUtils.equals("HC",listOverseasFileBase.getFileType())){
            return new Result(HttpStatus.BAD_REQUEST.value(), "FID不是紙本單！");
        }

//        listMatchDataOverseas.setFileMappingId(0L);
        PageInfo<ListMatchDataOverseas> list = listMatchDataOverseasService.getListMatchDataOverseasList(page, listMatchDataOverseas);
        return new Result(HttpStatus.OK.value(), null, list);
    }


    @ApiOperation(value = "根据id查看file work info详情")
    @GetMapping("getListMatchDataOverseasById")
    public ResponseEntity<ListMatchDataOverseasUniq> getListMatchDataOverseasById(Long id) {
        ListMatchDataOverseasUniq listMatchDataOverseasUniq = listMatchDataOverseasUniqService.getById(id);
        return new ResponseEntity<>(listMatchDataOverseasUniq, HttpStatus.OK);
    }

    @ApiOperation(value = "查看match work info列表")
    @GetMapping("getListMatchDataOverseasMatchWorkList")
    public ResponseEntity<List<ListMatchDataOverseasMatchWork>> getListMatchDataOverseasMatchWorkList(
            String dataUniqueKey) {
        List<ListMatchDataOverseasMatchWork> list = listMatchDataOverseasMatchWorkService
                .getListMatchDataOverseasMatchWorkList(dataUniqueKey);
        return new ResponseEntity<>(list, HttpStatus.OK);
    }

    @ApiOperation(value = "查看file work share列表")
    @GetMapping("getListMatchDataOverseasMappingList")
    public ResponseEntity<List<ListMatchDataOverseasMapping>> getListMatchDataOverseasMappingList(
            Long fileMappingId) {
        List<ListMatchDataOverseasMapping> list = listMatchDataOverseasMappingService
                .getListMatchDataOverseasMappingList(fileMappingId);
        return new ResponseEntity<>(list, HttpStatus.OK);
    }

    @ApiOperation(value = "清单审核")
    @PostMapping("checkListMatchDataBasics")
    public ResponseEntity<Integer> checkListMatchDataOverseas(
            @RequestBody CheckListMatchDataOverseasDto checkListMatchDataOverseasDto) {
        String dataUniqueKey = checkListMatchDataOverseasDto.getDataUniqueKey();
        Long workTitleId = checkListMatchDataOverseasDto.getWorkTitleId();
        Integer status = checkListMatchDataOverseasDto.getStatus();

        List<ListMatchDataOverseasMappingDto> listMatchDataOverseasMappingDto = checkListMatchDataOverseasDto
                .getListMatchDataOverseasMappingDto();
        List<ListMatchDataOverseasMapping> listMatchDataOverseasMappingList = null;
        if (listMatchDataOverseasMappingDto != null && !listMatchDataOverseasMappingDto.isEmpty()) {
            listMatchDataOverseasMappingList = listMatchDataOverseasMappingDto.stream().map(it -> {
                ListMatchDataOverseasMapping listMatchDataOverseasMapping = new ListMatchDataOverseasMapping();
                listMatchDataOverseasMapping.setId(it.getId());
                listMatchDataOverseasMapping.setStatus(it.getStatus());
                listMatchDataOverseasMapping.setIpNameNo(it.getIpNameNo());
                return listMatchDataOverseasMapping;
            }).collect(Collectors.toList());
        }

        listMatchDataOverseasService.checkListMatchDataOverseas(dataUniqueKey, workTitleId, status,
                listMatchDataOverseasMappingList);
        return new ResponseEntity<>(1, HttpStatus.OK);
    }

    @ApiOperation(value = "清单作品审核") //FIXME
    @PostMapping("checkListMatchDataOverseasUniqWithMtchWork")
    public ResponseEntity<Integer> checkListMatchDataOverseasUniqWithMtchWork(
            @RequestBody CheckListMatchDataOverseasDto checkListMatchDataOverseasDto) {
        String dataUniqueKey = checkListMatchDataOverseasDto.getDataUniqueKey();
        Long workTitleId = checkListMatchDataOverseasDto.getWorkTitleId();
        if (workTitleId == null){
            throw new MustException(ResultCode.Unknown_Exception.getCode(),"請指定合併作品！");
        }
        Integer status = checkListMatchDataOverseasDto.getStatus();

        listMatchDataOverseasService.checkListMatchDataOverseasUniqWithMtchWork(dataUniqueKey, workTitleId, status);
        return new ResponseEntity<>(1, HttpStatus.OK);
    }

    @ApiOperation(value = "合并清单明细") //FIXME
    @GetMapping("getListMatchDataOverseasMergeList")
    public ResponseEntity getListMatchDataOverseasMergeList(@RequestParam("dataUniqueKey") String dataUniqueKey) {
        List<ListMatchDataOverseas> listMatchDataOverseasList = listMatchDataOverseasService.getListMatchDataOverseasMergeList(dataUniqueKey);
        return ResponseEntity.ok(new SuccessResult<>(listMatchDataOverseasList));
    }

    @ApiOperation(value = "批量审核清单")
    @PostMapping("checkListMatchDataOverseasAll")
    public ResponseEntity<Integer> checkListMatchDataOverseasAll(@RequestBody CheckBatchListMatchDto checkBatchListMatchDto) {
        Integer status = checkBatchListMatchDto.getStatus();
        List<String> uniqueKeyMd5List = checkBatchListMatchDto.getUniqueKeyMd5List();
        return new ResponseEntity<>(listMatchDataOverseasService.checkListMatchDataOverseasAll(status, uniqueKeyMd5List), HttpStatus.OK);
    }


//	@ApiOperation(value = "批量审核清单")
//	@PostMapping("checkBatchListMatchDataBasics")
//	public ResponseEntity<String> checkBatchListMatchDataOverseas(@RequestBody CheckBatchListMatchDto checkBatchListMatchDto) {
//
//		List<String> uniqueKeyMd5List = checkBatchListMatchDto.getUniqueKeyMd5List();
//		Integer status = checkBatchListMatchDto.getStatus();
//
//		if (uniqueKeyMd5List == null || uniqueKeyMd5List.isEmpty()) {
//			return new ResponseEntity<>("uniqueKeyMd5List为空", HttpStatus.OK);
//		}
//
//		if(status !=1 && status != 2)
//			return new ResponseEntity<>("传入的status不正确", HttpStatus.OK);
//
//		List<ListMatchDataOverseas> listMatchDataOverseasList = listMatchDataOverseasService.getListMatchDataOverseasByMd5List(uniqueKeyMd5List);
//		if(null == listMatchDataOverseasList || listMatchDataOverseasList.isEmpty())
//			return new ResponseEntity<>("uniqueKeyMd5List没有查询到相关数据", HttpStatus.OK);
//
//		listMatchDataOverseasList = listMatchDataOverseasList.stream().filter(it -> null != it.getMatchWorkId()).collect(Collectors.toList());
//		if(null == listMatchDataOverseasList || listMatchDataOverseasList.isEmpty())
//			return new ResponseEntity<>("uniqueKeyMd5List没有匹配数据", HttpStatus.OK);
//
//		listMatchDataOverseasService.checkBatchListMatchDataOverseas(listMatchDataOverseasList,status);
//
//		if(status ==1)
//			return new ResponseEntity<>("批量审核通过成功", HttpStatus.OK);
//		return new ResponseEntity<>("批量审核拒绝成功", HttpStatus.OK);
//	}

/*    @ApiOperation(value = "O清单手动添加/修改数据")
    @PostMapping("addManully")
    public Result<Integer> addManully(@RequestBody ListMatchDataOverseasAddManullyDto listMatchDataOverseasAddManullyDto) {
        Long fileBaseId = listMatchDataOverseasAddManullyDto.getFileBaseId();
        if (Objects.isNull(fileBaseId)) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "fileBaseId不能為空！");
        }
        List<ListMatchDataOverseas> list = listMatchDataOverseasAddManullyDto.getList();
        if (list == null || list.isEmpty()) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "沒有需要添加或修改的數據！");
        }
        String message = "";
        int value = HttpStatus.OK.value();
        try {
            Integer remitSoc = listMatchDataOverseasAddManullyDto.getRemitSoc();
            if (Objects.isNull(remitSoc)) {
                remitSoc = 0;
            }
            Integer finalRemitSoc = remitSoc;
            list.forEach(x -> {
                x.setFileBaseId(fileBaseId);
                x.setFileMappingId(0L);
                x.setRemitSociety(finalRemitSoc);
                x.setReceiptSociety(161);
                String overseasMd5 = MatchMd5.getManullyOverseasMd5(x);
                x.setDataUniqueKey(overseasMd5);
                x.init();
            });

            List<ListMatchDataOverseas> addList = list.stream().filter(x -> Objects.isNull(x.getId())).collect(Collectors.toList());
            List<ListMatchDataOverseas> updateList = list.stream().filter(x -> Objects.nonNull(x.getId())).collect(Collectors.toList());
            listMatchDataOverseasService.saveList(addList);
            listMatchDataOverseasService.updateBatchByPrimaryKeySelective(updateList); // TODO 需要跟新mapping中的dataUniqueKey
            message = "操作成功！";
        } catch (Exception e) {
            message = e.getMessage();
            value = HttpStatus.INTERNAL_SERVER_ERROR.value();
            log.error("msg:", e);
        }
        return new Result(value, message, 1);
    }*/

    @ApiOperation(value = "O清单手动添加/修改数据ListMatchDataOverseasMapping")
    @PostMapping("addManullyOverseasMapping")
    public Result<Integer> addManullyOverseasMapping(@RequestBody ListMatchDataOverseasAndMappingDto dto) {
        if (null == dto || null == dto.getOverseas() || CollectionUtils.isEmpty(dto.getMappings())) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "沒有需要添加或修改的數據！");
        }
        ListMatchDataOverseas overseas = dto.getOverseas();
        if (null == overseas.getId()) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "参数id缺失！");
        }
        String message = "";
        int value = HttpStatus.OK.value();

        List<ListMatchDataOverseasMapping> list = dto.getMappings();
        try {
            list.forEach(x -> {
                x.setFileBaseId(0L);
                x.setFileMappingId(0L);
                x.init();
            });
            List<ListMatchDataOverseasMapping> addList = list.stream().filter(x -> Objects.isNull(x.getId())).collect(Collectors.toList());
            List<ListMatchDataOverseasMapping> updateList = list.stream().filter(x -> Objects.nonNull(x.getId())).collect(Collectors.toList());
            listMatchDataOverseasMappingService.saveList(addList);
            listMatchDataOverseasMappingService.updateBatchByPrimaryKeySelective(updateList);
            long count = list.stream().filter(x -> x.getStatus() == 0).count();
            if(count > 0) {
                overseas.setStatus(0); // 待匹配
            }else {
                long unMatchCount = list.stream().filter(x -> x.getStatus() == 2).count();
                if(unMatchCount > 0) {
                    overseas.setStatus(2); // 不匹配
                }else {
                    overseas.setStatus(3); // ip 匹配完毕
                }
            }
            overseas.init();
            listMatchDataOverseasService.updateSelective(overseas);

        } catch (Exception e) {
            message = e.getMessage();
            value = HttpStatus.INTERNAL_SERVER_ERROR.value();
            log.error("msg:", e);
        }
        return new Result(value, message, 1);
    }

    @ApiOperation(value = "O清单手动删除数据ListMatchDataOverseasMapping")
    @DeleteMapping("deleteOverseasMapping/{id}")
    public Result<Integer> deleteOverseasMapping(@PathVariable("id") Long id) {
        if (null == id) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數id不能爲空！");
        }
        String message = "";
        int value = HttpStatus.OK.value();
        try {
            Integer delete = listMatchDataOverseasMappingService.delete(id);
            if (delete == 1) {
                message = "删除成功！";
            } else {
                message = "刪除失敗！";
            }
        } catch (Exception e) {
            message = e.getMessage();
            value = HttpStatus.INTERNAL_SERVER_ERROR.value();
            log.error("msg:", e);
        }
        return new Result(value, message, 1);
    }

    @ApiOperation(value = "根据overseasId查看file work share列表")
    @GetMapping("getListMatchDataOverseasMappingListByOverseasId")
    public Result<List<ListMatchDataOverseasMapping>> getListMatchDataOverseasMappingListByOverseasId(@RequestParam("overseasId") Long overseasId) {
        List<ListMatchDataOverseasMapping> list = listMatchDataOverseasMappingService.getListMatchDataOverseasMappingListByOverseasId(overseasId);
        return new Result(HttpStatus.OK.value(), null, list);
    }

    @ApiOperation(value = "根据mappingId查看file work share列表")
    @GetMapping("getListMatchDataOverseasMappingListByMappingId")
    public Result<List<ListMatchDataOverseasMapping>> getListMatchDataOverseasMappingListByMappingId(@RequestParam("mappingId") Long mappingId) {
        List<ListMatchDataOverseasMapping> list = listMatchDataOverseasMappingService.getListMatchDataOverseasMappingListByOverseasId(mappingId);
        return new Result(HttpStatus.OK.value(), null, list);
    }


    @Value("${list.file.uploadTempPath}")
    private String uploadTempPath;

    @ApiOperation(value = "上传补充数据")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "文件", name = "files", dataType = "MultipartFile[]", paramType = "query")})
    @RequestMapping(value = "/addImport", produces = "application/json;charset=utf-8", method = RequestMethod.POST)
    public Result<String> addImport(@RequestParam("file") MultipartFile file, @RequestParam("fid") Long fid){
        String message = "";
        if (null == file) {
            message = "上傳文件不能為空！";
        } else if (file.getSize() < 0L) {
            message = "上傳文件内容爲空！";
        } else if (!StringUtils.endsWithAny(file.getOriginalFilename().toLowerCase(), ".xlsx", ".xls")) {
            message = "上傳文件格式必須是以下類型【.xlsx, .xls】！";
        }
        if(fid == null){
            message = "FID不能为空";
        }
        if (StringUtils.isNotBlank(message)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), message);
        }

        if (CommonUtils.isWindows()) {
            uploadTempPath = "F:\\Test\\olist\\temp";
        }

        String filePath = uploadTempPath;
        File parentFile = new File(filePath);
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        String fileName = file.getOriginalFilename();
        File targetFile = null;
        try {
            String targetFileName = Md5.getMd5ByInputStream(file.getInputStream());
            if (StringUtils.isBlank(targetFileName)) {
                message = "文件讀取異常！";
            }
            String fileExt = fileName.substring(fileName.lastIndexOf("."));
            targetFileName = targetFileName + fileExt;
            targetFile = new File(parentFile, Objects.requireNonNull(targetFileName));
            file.transferTo(targetFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (null == targetFile || !targetFile.exists()) {
            message = "文件轉換失敗！";
        }
        if (StringUtils.isNotBlank(message)) {
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), message);
        }
        StringBuilder sb = new StringBuilder();
        try {
            List<ListMatchDataOverseas> overseaList = new ArrayList<>();
            List<ListMatchDataOverseasMapping> mappingList = new ArrayList<>();
            List<String> titleList = Arrays.asList("分配代號","Remit soc","Ref No.","Remit Work title", "Remit IP","Remit IP name No.","Remit IP share", "Remit IP Roy", "Matched work No.","Match Soc No.","Match IP name No.") ;
            ExcelUtil.read(targetFile, titleList, (t, u) -> {
                log.debug("t: {} ========  u: {}}", t, JSON.toJSONString(u));
                try {
                    listMatchDataOverseasMappingService.addImport(u,titleList,fid,overseaList,mappingList);
//                    overseaList.add(getListMatchDataOverseas(u,titleList,fid));
//                    mappingList.add(getListMatchDataOverseasMapping(u,titleList,fid));
                } catch (Exception e) {
                    log.error("海外清單 - 审核结果导入处理失败， {}, msg： {}", t, e.getMessage());
                    sb.append(String.format("%s, msg： %s", t, e.getMessage())).append(CommonUtils.LINE_BREAK);
                }
            });
            if(!CollectionUtils.isEmpty(overseaList)){
                List<List<ListMatchDataOverseas>> partitions = Lists.partition(overseaList, BATCH_SIZE_1000) ;
                partitions.forEach( p -> listMatchDataOverseasService.saveList(p));

                for(int i = 0 ; i< overseaList.size(); i ++){
                    mappingList.get(i).setDataUniqueKey(overseaList.get(i).getDataUniqueKey());
                    mappingList.get(i).setOverseasId(overseaList.get(0).getId());
                }
            }

            List<List<ListMatchDataOverseasMapping>> partitions2 = Lists.partition(mappingList, BATCH_SIZE_1000) ;
            partitions2.forEach( p -> listMatchDataOverseasMappingService.saveList(p));

        } catch (Exception e) {
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
        }
        if (StringUtils.isNotBlank(sb.toString())) {
            sb.insert(0, String.format("文件解析異常!%s", CommonUtils.LINE_BREAK));
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), sb.toString());
        }
        return new Result<>(HttpStatus.OK.value(), message, "導入成功！");
    }

    @ApiOperation(value = "审核结果导入")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "文件", name = "files", dataType = "MultipartFile[]", paramType = "query")})
    @RequestMapping(value = "/importAuditResults", produces = "application/json;charset=utf-8", method = RequestMethod.POST)
    public Result<String> importAuditResults(
            @RequestParam("file") MultipartFile file) {
        String message = "";
        if (null == file) {
            message = "上傳文件不能為空！";
        } else if (file.getSize() < 0L) {
            message = "上傳文件内容爲空！";
        } else if (!StringUtils.endsWithAny(file.getOriginalFilename().toLowerCase(), ".xlsx", ".xls")) {
            message = "上傳文件格式必須是以下類型【.xlsx, .xls】！";
        }
        if (StringUtils.isNotBlank(message)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), message);
        }

        if (CommonUtils.isWindows()) {
            uploadTempPath = "F:\\Test";
        }
        String filePath = uploadTempPath;
        File parentFile = new File(filePath);
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        String fileName = file.getOriginalFilename();
        File targetFile = null;
        try {
            String targetFileName = Md5.getMd5ByInputStream(file.getInputStream());
            if (StringUtils.isBlank(targetFileName)) {
                message = "文件讀取異常！";
            }
            String fileExt = fileName.substring(fileName.lastIndexOf("."));
            targetFileName = targetFileName + fileExt;
            targetFile = new File(parentFile, Objects.requireNonNull(targetFileName));
            file.transferTo(targetFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (null == targetFile || !targetFile.exists()) {
            message = "文件轉換失敗！";
        }
        if (StringUtils.isNotBlank(message)) {
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), message);
        }
        // 读取文件，找到对应字段， 主要是 DataUniqueKey, workNum, workSoc
        StringBuilder sb = new StringBuilder();
        Map<String, List<String>> map = new HashMap<>();
        try {
            ExcelUtil.read(targetFile, Arrays.asList("Remit Society", "Data Unique Key", "Match Work No", "Match Work Soc"), (t, u) -> {
                log.debug("t: {} ========  u: {}}", t, JSON.toJSONString(u));
                try {
                    String remitSociety = u.get("Remit Society");
                    String dataUniqueKey = u.get("Data Unique Key");
                    String workNum = u.get("Match Work No");
                    String workSoc = u.get("Match Work Soc");
                    List<String> list = new ArrayList<>();
                    list.add(remitSociety);
                    list.add(workNum);
                    list.add(workSoc);
                    map.put(dataUniqueKey, list);
                } catch (Exception e) {
                    log.error("海外清單 - 审核结果导入处理失败， {}, msg： {}", t, e.getMessage());
                    sb.append(String.format("%s, msg： %s", t, e.getMessage())).append(CommonUtils.LINE_BREAK);
                }
            });

            for(Map.Entry<String, List<String>> entry : map.entrySet()){
                List<String> list = entry.getValue();
                listMatchDataOverseasService.importAuditResults(list.get(0), entry.getKey(), list.get(1), list.get(2));
            }

        } catch (Exception e) {
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
        }
        if (StringUtils.isNotBlank(sb.toString())) {
            sb.insert(0, String.format("文件解析異常!%s", CommonUtils.LINE_BREAK));
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), sb.toString());
        }
        return new Result<>(HttpStatus.OK.value(), message, "導入成功！");
    }

    @ApiOperation(value = "IP審核匹配作品查詢")
    @PostMapping("listListMatchDataOverseasUniq")
    public Result<PageInfo<ListMatchDataOverseasUniq>> listListMatchDataOverseasUniq(@RequestBody ListMatchDataOverseasMatchWorkCheckDto param) {

        Page page = param.getPage();

        //从去重表拿数据
        PageInfo<ListMatchDataOverseasUniq> list = listMatchDataOverseasUniqService.getListMatchDataOverseasUniqIpCheckList(page, param.getMatchWorkId(),param.getMatchWorkSoc(),param.getMatchWorkTitle(),param.getDistributedIp(),param.getDistributedIpName(),
                param.getMatchIpSoc(),param.getRemitSoc(),param.getRemitIpName(),param.getDistNo(),param.getRemitWorkTitle(),param.getDataUniqueKey(),param.getBatch(),param.getfId(),param.getRejectCode(),param.getStatus());
        return new Result(HttpStatus.OK.value(), null, list);
    }



    @ApiOperation(value = "导出")
    @PostMapping(value = "/export", produces = "application/json;charset=utf-8")
    public void export(HttpServletResponse response, @RequestBody ListMatchDataOverseasDto listMatchDataOverseasDto) {
        ListMatchDataOverseas listMatchDataOverseas = listMatchDataOverseasDto.getListMatchDataOverseas();
        if(null == listMatchDataOverseas) {
            //return new Result<>(HttpStatus.BAD_REQUEST.value(), "請求參數不能為空！");
        }
        listMatchDataOverseas.setFileMappingId(-1L);
        /*Integer count = listMatchDataOverseasUniqService.getListMatchDataOverseasUniqListCount(listMatchDataOverseas);
        if (count == 0) {
            //return new Result(HttpStatus.OK.value(), "查询结果数量：0 ");
            return;
        }*/

        String format = DateParse.format(new Date(), DateParse.patternTime);
        String fileName = String.format("海外清單審核數據_%s", format);
        String message = null;
        try {
            String[] titleArray = {"Remit Society", "Title", "Author/Composer", "ISRC", "ISWC", "STATUS", "Match Work No", "Match Work Soc", "Data Unique Key"};
            String[] keyArray = {"remitSociety", "originalTitle", "authorComposer", "isrc", "iswc", "statusStr", "matchWorkId", "matchWorkSocietyCode", "dataUniqueKey"};
            GenerateExcelUtil<ListMatchDataOverseasUniq> generateExcelUtil = new GenerateExcelUtil<>(titleArray, keyArray, false);
            //公平锁，写入excel走异步
//            ReentrantLock lock = new ReentrantLock(true);
            String sheetName = "sheet";
            int sheetAt = 1;
            for (int i = 1; i < Integer.MAX_VALUE; i++) {
                PageInfo<ListMatchDataOverseasUniq> pageInfo = listMatchDataOverseasUniqService.getListMatchDataOverseasUniqList(new Page(i, Constants.BATCH_SIZE_10000), listMatchDataOverseas);
                if (null == pageInfo || CollectionUtils.isEmpty(pageInfo.getList())) {
                    break;
                }
                Sheet sheet = generateExcelUtil.getWorkbook().getSheet(sheetName);
                // 减1 去除行数
                if (null != sheet && sheet.getLastRowNum() > SpreadsheetVersion.EXCEL2007.getLastRowIndex() - pageInfo.getList().size() - 1) {
                    sheetName = String.format("%s_%d", sheetName, sheetAt++);
                }
                //写入excel
                List<ListMatchDataOverseasUniq> list = pageInfo.getList();
                list.forEach(t -> {
                    t.setMatchWorkId(null);
                    t.setMatchWorkSocietyCode(null);
                    if(t.getStatus() == 0 || t.getStatus() == 2){
                        // 待匹配或不匹配的数据，对应的的match数据置为null
                        t.setStatusStr("未匹配");
                    } else if(t.getStatus() == 1){
                        t.setStatusStr("作品已匹配");
                    } else if(t.getStatus() == 3){
                        t.setStatusStr("ip已匹配");
                    }

                });
                generateExcelUtil.add(sheetName, pageInfo.getList());

                /*String finalSheetName = sheetName;
                CompletableFuture.runAsync(() -> {
                    try {
                        lock.lock();
                        generateExcelUtil.add(finalSheetName, pageInfo.getList());
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        lock.unlock();
                    }
                });*/
            }
            /*while (lock.isLocked()) {
                //自旋，等待最后一个公平锁释放
            }*/
            // 最后输出
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.addHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(new String(fileName.concat(".xlsx").getBytes("utf-8")), "UTF-8"));
            // 使用xlsx格式，会出现错误: java.io.IOException: This archive contains unclosed entries
            // TODO: 2021-02-01 huyong: 莫名其妙又好了。。。但是只有本地測試环境可以；；；；； 02号早上重新发版又可以了，这。。。归根究底的原因无法探究，先这样吧。。。。
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            // response.setContentType("application/octet-stream"); // 返回二进制流（通用） // swagger下载下来，office能打开，wps打不开； 测试环境都打不开
            // response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setCharacterEncoding("utf-8");
//            Sheet sheetAt1 = workbook.getSheetAt(0);
//            System.out.println("測試！！！");
//            log.info("workSheet: {}", sheetAt1);
//            log.info("row: {}", null != sheetAt1 ? sheetAt1.getLastRowNum() : "sheet is null");   firewall-cmd --zone=public --add-port=5005/tcp --permanent
            generateExcelUtil.getWorkbook().write(response.getOutputStream());
        } catch (Exception e) {
            log.error("msg: ", e);
            message = "msg: " + e.getMessage();
        } finally {
            /*if (generateExcelUtil != null && null != generateExcelUtil.getWorkbook()) {
                try {
                    generateExcelUtil.getWorkbook().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(null != out) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }*/
        }
//        if (StringUtils.isNotBlank(message)) {
//            if (!response.isCommitted()) {
//                response.reset();
//            }
//            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), message);
//        }
//        return new Result<>(HttpStatus.OK.value(), null);
    }

    @ApiOperation(value = "根据条件导出海外清单数据")
    @PostMapping(value = "/exportByConditions", produces = "application/json;charset=utf-8")
    public void exportByConditions(HttpServletResponse response,
                                @RequestParam(value = "file_base_id", required = false) List<Long> fileBaseIds,
                                @RequestParam(value = "data_unique_key", required = false) List<String> dataUniqueKeys,
                                @RequestParam(value = "original_title", required = false) List<String> originalTitles,
                                @RequestParam(value = "author_composer", required = false) List<String> authorComposers,
                                @RequestParam(value = "export_type", required = false, defaultValue = "original") String exportType) {
        if (fileBaseIds == null || fileBaseIds.isEmpty()) {
            try {
                response.setContentType("text/plain;charset=utf-8");
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST); // 设置400状态码
                response.getWriter().write("\u8bf7求参数file_base_id不能为空\uff01");
                return;
            } catch (IOException e) {
                log.error("Response error: ", e);
                return;
            }
        }

        String format = DateParse.format(new Date(), DateParse.patternDate);
        String baseFileName = String.format("OS_UnmatchList_0S_%s", format);

        // 检查是否有去重条件
        boolean hasDeduplicationConditions = (dataUniqueKeys != null && !dataUniqueKeys.isEmpty()) ||
                                           (originalTitles != null && !originalTitles.isEmpty()) ||
                                           (authorComposers != null && !authorComposers.isEmpty());
        System.out.println(hasDeduplicationConditions?"有去重条件":"无去重条件");

        try {
            // 定义Excel表头和对应的字段
            String[] titleArray = {"Log_Type", "Batch_No", "Batch_No_Seq", "match_id", "UP_TITLE", "IP_NAME", "artist", "REMARK"};
            String[] keyArray = {"logType", "batch", "batchNoSeq", "dataUniqueKey", "originalTitle", "authorComposer", "artist", "remark"};

            // 查询原始数据（未去重）
            List<ListMatchDataOverseas> allOriginalDataList = listMatchDataOverseasService.getDedupListMatchDataOverseas(
                    fileBaseIds, 0, false, false, false); // 不分组，直接获取所有原始数据

            // 按originalTitle字段升序排列
            allOriginalDataList.sort(Comparator.comparing(item -> item.getOriginalTitle() != null ? item.getOriginalTitle() : ""));

            // 如果没有数据，直接返回
            if (allOriginalDataList.isEmpty()) {
                response.setContentType("text/plain;charset=utf-8");
                response.getWriter().write("没有找到符合条件的数据");
                return;
            }

            // 将原始数据转换为ExportData并生成Excel
            List<ExportData> originalExportDataList = convertToExportData(allOriginalDataList);

            // 创建原始数据的Excel
            GenerateExcelUtil<ExportData> originalExcelUtil = new GenerateExcelUtil<>(titleArray, keyArray, false);
            int batchSize = 10000;
            addDataToExcel(originalExcelUtil, originalExportDataList, batchSize);

            // 如果有去重条件，则处理去重逻辑
            List<ListMatchDataOverseas> dedupDataList = new ArrayList<>();
            GenerateExcelUtil<ExportData> dedupExcelUtil = null;

            if (hasDeduplicationConditions) {
                // 确定去重字段
                boolean groupByDataUniqueKey = dataUniqueKeys != null && !dataUniqueKeys.isEmpty();
                boolean groupByOriginalTitle = originalTitles != null && !originalTitles.isEmpty();
                boolean groupByAuthorComposer = authorComposers != null && !authorComposers.isEmpty();

                // 使用SQL去重逻辑查询数据
                dedupDataList = listMatchDataOverseasService.getDedupListMatchDataOverseas(
                        fileBaseIds, 0, groupByDataUniqueKey, groupByOriginalTitle, groupByAuthorComposer);
                System.out.println("------------------------TEST-----------------------");
                System.out.println("去重数据条数：" + dedupDataList.size());
                System.out.println("------------------------TEST-----------------------");

                // 创建去重后的Excel（即使去重后没有数据）
                List<ExportData> dedupExportDataList = convertToExportData(dedupDataList);
                dedupExcelUtil = new GenerateExcelUtil<>(titleArray, keyArray, false);
                addDataToExcel(dedupExcelUtil, dedupExportDataList, batchSize);
            }

            // 当有去重条件时，返回两个Excel文件打包成的ZIP（无论去重后是否有数据）
            if (hasDeduplicationConditions) {
                // 创建临时文件来保存两个Excel
                File tempOriginalFile = File.createTempFile("original_", ".xlsx");
                File tempDedupFile = File.createTempFile("dedup_", ".xlsx");

                try {
                    // 将原始数据Excel写入临时文件
                    try (FileOutputStream originalFos = new FileOutputStream(tempOriginalFile)) {
                        originalExcelUtil.getWorkbook().write(originalFos);
                    }

                    // 将去重后的Excel写入临时文件
                    try (FileOutputStream dedupFos = new FileOutputStream(tempDedupFile)) {
                        dedupExcelUtil.getWorkbook().write(dedupFos);
                    }

                    // 设置ZIP响应头
                    String zipFileName = baseFileName + ".zip";
                    response.setContentType("application/zip");
                    response.setCharacterEncoding("utf-8");
                    response.addHeader("Content-Disposition", "attachment;filename=\"" + zipFileName + "\"");

                    // 创建ZIP文件并写入响应
                    try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                        // 添加原始数据Excel到ZIP
                        addFileToZip(zipOut, tempOriginalFile, baseFileName + "_原始數據.xlsx");

                        // 添加去重后的Excel到ZIP
                        addFileToZip(zipOut, tempDedupFile, baseFileName + "_去重後.xlsx");
                    }
                } finally {
                    // 删除临时文件
                    tempOriginalFile.delete();
                    tempDedupFile.delete();
                }
            } else {
                // 如果没有去重条件，只导出原始数据
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setCharacterEncoding("utf-8");
                String fileName = baseFileName + ".xlsx";
                response.addHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
                originalExcelUtil.getWorkbook().write(response.getOutputStream());
            }

        } catch (Exception e) {
            log.error("导出海外清单数据失败: ", e);
            try {
                if (!response.isCommitted()) {
                    response.reset();
                    response.setContentType("text/plain;charset=utf-8");
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR); // 设置500状态码
                    response.getWriter().write("导出失败: " + e.getMessage());
                }
            } catch (IOException ex) {
                log.error("Response error: ", ex);
            }
        }
    }

    /**
     * 将ListMatchDataOverseas列表转换为ExportData列表
     * @param dataList 原始数据列表
     * @return 转换后的ExportData列表
     */
    private List<ExportData> convertToExportData(List<ListMatchDataOverseas> dataList) {
        List<ExportData> exportDataList = new ArrayList<>(dataList.size());
        int seqNo = 1;

        for (ListMatchDataOverseas item : dataList) {
            ExportData exportData = new ExportData();
            exportData.setBatch(item.getBatch());
            exportData.setBatchNoSeq(seqNo++);
            exportData.setDataUniqueKey(item.getDataUniqueKey());
            exportData.setOriginalTitle(item.getOriginalTitle());
            exportData.setAuthorComposer(item.getAuthorComposer());
            // logType, artist 和 remark 已经在类中设置了默认值

            exportDataList.add(exportData);
        }

        return exportDataList;
    }

    /**
     * 将数据分批添加到Excel中
     * @param excelUtil Excel生成工具
     * @param dataList 数据列表
     * @param batchSize 批量大小
     */
    private void addDataToExcel(GenerateExcelUtil<ExportData> excelUtil, List<ExportData> dataList, int batchSize) {
        String sheetName = "sheet";
        int sheetAt = 1;

        for (int i = 0; i < dataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, dataList.size());
            List<ExportData> batchList = dataList.subList(i, endIndex);

            // 检查是否需要创建新的sheet
            Sheet sheet = excelUtil.getWorkbook().getSheet(sheetName);
            if (null != sheet && sheet.getLastRowNum() > SpreadsheetVersion.EXCEL2007.getLastRowIndex() - batchList.size() - 1) {
                sheetName = String.format("%s_%d", sheetName, sheetAt++);
            }

            // 添加到Excel
            excelUtil.add(sheetName, batchList);
        }
    }

    /**
     * 内部类，用于导出数据
     */
    private class ExportData {
        private String logType = "OS"; // 固定值 OS
        private Integer batch;
        private Integer batchNoSeq;
        private String dataUniqueKey;
        private String originalTitle;
        private String authorComposer;
        private String artist = ""; // 固定值 空字符串
        private String remark = ""; // 固定值 空字符串

        public String getLogType() { return logType; }
        public Integer getBatch() { return batch; }
        public Integer getBatchNoSeq() { return batchNoSeq; }
        public String getDataUniqueKey() { return dataUniqueKey; }
        public String getOriginalTitle() { return originalTitle; }
        public String getAuthorComposer() { return authorComposer; }
        public String getArtist() { return artist; }
        public String getRemark() { return remark; }

        public void setLogType(String logType) { this.logType = logType; }
        public void setBatch(Integer batch) { this.batch = batch; }
        public void setBatchNoSeq(Integer batchNoSeq) { this.batchNoSeq = batchNoSeq; }
        public void setDataUniqueKey(String dataUniqueKey) { this.dataUniqueKey = dataUniqueKey; }
        public void setOriginalTitle(String originalTitle) { this.originalTitle = originalTitle; }
        public void setAuthorComposer(String authorComposer) { this.authorComposer = authorComposer; }
        public void setArtist(String artist) { this.artist = artist; }
        public void setRemark(String remark) { this.remark = remark; }
    }

    /**
     * 将文件添加到ZIP输出流中
     * @param zipOut ZIP输出流
     * @param file 要添加的文件
     * @param entryName ZIP条目名称
     * @throws IOException 如果发生I/O错误
     */
    private void addFileToZip(ZipOutputStream zipOut, File file, String entryName) throws IOException {
        ZipEntry zipEntry = new ZipEntry(entryName);
        zipOut.putNextEntry(zipEntry);

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[8192]; // 使用更大的缓冲区提高性能
            int length;
            while ((length = fis.read(buffer)) > 0) {
                zipOut.write(buffer, 0, length);
            }
        }

        zipOut.closeEntry();
    }
}
