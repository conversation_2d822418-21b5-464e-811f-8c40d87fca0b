package tw.org.must.must.web.controller.new_list;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import tw.org.must.must.common.base.Page;
import tw.org.must.must.common.util.result.Result;
import tw.org.must.must.core.service.list.ListBasicFileTemplateConfigService;
import tw.org.must.must.core.service.list.ListCategoryService;
import tw.org.must.must.core.service.new_list.List2BasicFileTemplateConfigService;
import tw.org.must.must.core.service.new_list.List2BasicFileTemplateService;
import tw.org.must.must.model.list.enums.BasicFileTemplateParsingMode;
import tw.org.must.must.model.list.enums.BasicFileTemplateType;
import tw.org.must.must.model.list.vo.ListBasicFileTemplateVo;
import tw.org.must.must.model.new_list.List2BasicFileTemplate;

import java.util.List;
import java.util.Objects;


@Api(tags = "新-清单模板管理")
@RestController
@RequestMapping("/list2BasicFileTemplate")
public class List2BasicFileTemplateController {

    private static final Logger logger = LoggerFactory.getLogger(List2BasicFileTemplateController.class);

    @Autowired
    private List2BasicFileTemplateService list2BasicFileTemplateService;

    private List2BasicFileTemplateConfigService list2BasicFileTemplateConfigService;

    @Autowired
    private ListCategoryService listCategoryService;

    @ApiOperation(value = "添加清单模板")
    @PostMapping(value = "/addTempate")
    public Result<String> addTemplate(@RequestBody List2BasicFileTemplate list2BasicFileTemplate) {
        logger.info("input list2BasicFileTemplate: {}", JSON.toJSONString(list2BasicFileTemplate));
        String message = "";
        int code = HttpStatus.OK.value();
        if (StringUtils.isBlank(list2BasicFileTemplate.getFolder())) {
            message = "資料夾 不能爲空！";
        } else if (StringUtils.isBlank(list2BasicFileTemplate.getCategoryCode())) {
            message = "categoryCode 不能爲空！";
        } else if (null == list2BasicFileTemplate.getLine()) {
            message = "表頭所在行 不能爲空";
        } else if (list2BasicFileTemplate.getLine() < 0) {
            message = "表頭所在行 值不能小於0";
        } else if (list2BasicFileTemplateService.checkByFolder(list2BasicFileTemplate.getFolder(), list2BasicFileTemplate.getCategoryCode())) {
            message = String.format("該資料夾【%s】已存在，請重新輸入！", list2BasicFileTemplate.getFolder());
        } else if (!listCategoryService.checkListCategoryExistOrNot(list2BasicFileTemplate.getCategoryCode())) {
            message = String.format("該categoryCode【%s】不存在，請重新確認再輸入！", list2BasicFileTemplate.getCategoryCode());
        } else if (Objects.isNull(list2BasicFileTemplate.getParsingMode())) {
            list2BasicFileTemplate.setParsingMode(BasicFileTemplateParsingMode.STRICT_MODE.code());
        } else if (list2BasicFileTemplate.getParsingMode() > 1 || list2BasicFileTemplate.getParsingMode() < 0) {
            list2BasicFileTemplate.setParsingMode(BasicFileTemplateParsingMode.STRICT_MODE.code());
        }
        if (StringUtils.isNotBlank(message)) {
            code = HttpStatus.BAD_REQUEST.value();
        } else {
            try {
                list2BasicFileTemplate.setType(BasicFileTemplateType.DEFAULT.code());
                list2BasicFileTemplate.init();
                list2BasicFileTemplateService.add(list2BasicFileTemplate);
            } catch (Exception e) {
                message = e.getMessage();
                code = HttpStatus.INTERNAL_SERVER_ERROR.value();
                logger.error("out result: {}", e);
            }
        }
        Result result = new Result(code, message);
        logger.info("out result: {}", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "删除清单模板")
    @DeleteMapping(value = "/delete/{id}")
    public Result<String> deleteTemplate(@PathVariable("id") Long id) {
        logger.info("input id: {}", id);
        String message = "";
        int code = HttpStatus.OK.value();
        if (null == id) {
            message = "id不能爲空！";
        } else if (id < 1) {
            message = "id不能小於1";
        }
        List2BasicFileTemplate list2BasicFileTemplate = list2BasicFileTemplateService.getById(id);
        if (Objects.isNull(list2BasicFileTemplate)) {
            message = String.format("該id【%d】對應的模板不存在！", id);
        }
        if (StringUtils.isNotBlank(message)) {
            code = HttpStatus.BAD_REQUEST.value();
        } else {
            try {
                //刪除清單模板的時候 同時刪除清單配置模板的數據
                if (list2BasicFileTemplate.getType().equals(BasicFileTemplateType.CUSTOM.code())) {
                    //自定義模板説明有清單配置模板的數據
                    list2BasicFileTemplateConfigService.deleteByTemplateId(id);
                }
                list2BasicFileTemplateService.delete(id);
            } catch (Exception e) {
                message = e.getMessage();
                code = HttpStatus.INTERNAL_SERVER_ERROR.value();
                logger.error("out result: {}", e);
            }
        }
        Result result = new Result(code, message);
        logger.info("out result: {}", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "查詢清單模板")
    @PostMapping(value = "/searchTemplate")
    public Result<PageInfo<List2BasicFileTemplate>> searchTemplate(@RequestBody ListBasicFileTemplateVo vo) {
        logger.info("input List2BasicFileTemplateVo: {}", JSON.toJSONString(vo));
        String message = "";
        int code = HttpStatus.OK.value();
        PageInfo<List2BasicFileTemplate> pageInfo = null;
        Page page = vo.getPage();
        if (null == page || page.getPageNum() == 0 || page.getPageSize() <= 0) {
            page = new Page(1, 10);
        }
        try {
            List2BasicFileTemplate list2BasicFileTemplate = new List2BasicFileTemplate();
            BeanUtils.copyProperties(vo, list2BasicFileTemplate);
            PageHelper.startPage(page.getPageNum(), page.getPageSize());
            List<List2BasicFileTemplate> list = list2BasicFileTemplateService.getList(list2BasicFileTemplate);
            pageInfo = new PageInfo<>(list);
        } catch (Exception e) {
            message = e.getMessage();
            code = HttpStatus.INTERNAL_SERVER_ERROR.value();
            logger.error("out result: {}", e);
        }
        Result result = new Result(code, message, pageInfo);
        logger.info("out result: {}", JSON.toJSONString(result));
        return result;
    }

    @ApiOperation(value = "修改清单模板")
    @PostMapping(value = "/updateTemplate")
    public Result<String> updateTemplate(@RequestBody List2BasicFileTemplate list2BasicFileTemplate) {
        logger.info("input list2BasicFileTemplate: {}", JSON.toJSONString(list2BasicFileTemplate));
        String message = "";
        int code = HttpStatus.OK.value();
        if (null == list2BasicFileTemplate.getId()) {
            message = "修改清單模板時，id不能爲空！";
        } else if (StringUtils.isBlank(list2BasicFileTemplate.getFolder())) {
            message = "資料夾字段不能爲空！";
        } else if (StringUtils.isBlank(list2BasicFileTemplate.getCategoryCode())) {
            message = "categoryCode字段不能爲空！";
        } else if (null == list2BasicFileTemplate.getLine()) {
            message = "表頭所在行字段不能爲空";
        } else if (list2BasicFileTemplate.getLine() < 0) {
            message = "表頭所在行字段值不能小於0";
        } else if (!listCategoryService.checkListCategoryExistOrNot(list2BasicFileTemplate.getCategoryCode())) {
            message = "該categoryCode不存在，請重新確認再輸入！";
        } else if (Objects.isNull(list2BasicFileTemplate.getParsingMode())) {
            list2BasicFileTemplate.setParsingMode(BasicFileTemplateParsingMode.STRICT_MODE.code());
        } else if (list2BasicFileTemplate.getParsingMode() > 1 || list2BasicFileTemplate.getParsingMode() < 0) {
            list2BasicFileTemplate.setParsingMode(BasicFileTemplateParsingMode.STRICT_MODE.code());
        }
        List2BasicFileTemplate exist = list2BasicFileTemplateService.getById(list2BasicFileTemplate.getId());
        if (Objects.isNull(exist)) {
            message = String.format("該id【%d】對應的模板不存在！", list2BasicFileTemplate.getId());
        } else if (!StringUtils.equals(exist.getFolder(), list2BasicFileTemplate.getFolder()) && list2BasicFileTemplateService.checkByFolder(list2BasicFileTemplate.getFolder())) {
            message = String.format("該資料夾【%s】已存在，請重新輸入！", list2BasicFileTemplate.getFolder());
        }
        if (StringUtils.isNotBlank(message)) {
            code = HttpStatus.BAD_REQUEST.value();
        } else {
            try {
                list2BasicFileTemplate.init();
                list2BasicFileTemplate.setType(null);
                list2BasicFileTemplateService.updateSelective(list2BasicFileTemplate);
            } catch (Exception e) {
                message = e.getMessage();
                code = HttpStatus.INTERNAL_SERVER_ERROR.value();
                logger.error("out result: {}", e);
            }
        }
        Result result = new Result(code, message);
        logger.info("out result: {}", JSON.toJSONString(result));
        return result;
    }


    @ApiOperation(value = "清單校驗目錄分類")
    @PostMapping(value = "/checkAndClassification")
    public Result<String> checkAndClassification(@RequestParam("filePath") String filePath) {
        logger.info("input filePath: {}", filePath);
        Result<String> result = new Result<>();
        try {
            result = list2BasicFileTemplateService.checkAndClassification(filePath);
        } catch (Exception e) {
            result.setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
            result.setMessage("错误： " + e.getMessage());
            logger.error("out result: {}", e);
        }
        logger.info("out: {}", result.toString());
        return result;
    }

}
