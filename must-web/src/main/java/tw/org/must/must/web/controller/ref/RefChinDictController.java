package tw.org.must.must.web.controller.ref;


import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tw.org.must.must.common.util.result.SuccessResult;
import tw.org.must.must.core.service.ref.RefChinDictService;
import tw.org.must.must.model.ref.RefChinDict;

import java.util.List;

@Api(tags = "RefChinDict -后台")
@RestController
@RequestMapping("/ref/chin/dict")
public class RefChinDictController {

    @Autowired
    private RefChinDictService refChinDictService ;

    @ApiOperation(value = "分页获取列表页")
    @GetMapping("/getListWithPage")
    public ResponseEntity listRefChinDictWithPage(@RequestParam(value = "chinWord", required = false) String chinWord,
                                                  @RequestParam(value = "romanWord", required = false) String romanWord,
                                                      @RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
                                                      @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize) {
        List<RefChinDict> refChinDictList = refChinDictService.listRefChinDictWithPage(chinWord, romanWord, pageSize, pageNum);
        return ResponseEntity.ok(new SuccessResult<>(new PageInfo(refChinDictList)));
    }

    @ApiOperation(value = "新增、保存")
    @PostMapping("/saveRefChinDict")
    public ResponseEntity saveRefChinDict(@RequestBody RefChinDict refChinDict) {

        if (StringUtils.isBlank(refChinDict.getChinWord())) {
            return new ResponseEntity("請求參數Chin Word不能為空！",HttpStatus.OK) ;
        }
        if (StringUtils.isBlank(refChinDict.getRomanWord())) {
            return new ResponseEntity("請求參數Roman Word不能為空！",HttpStatus.OK) ;
        }

        try{
            refChinDictService.saveRefChinDict(refChinDict);
        }catch (Exception e){
            return new ResponseEntity(e.getMessage(),HttpStatus.OK) ;
        }

        return ResponseEntity.ok("保存成功！");
    }

    @ApiOperation(value = "删除")
    @DeleteMapping("/deleteRefChinDict/{id}")
    public ResponseEntity deleteRefChinDict(@PathVariable("id") Long id) {

        Integer count = refChinDictService.delete(id);

        return ResponseEntity.ok(new SuccessResult<>(count));
    }
}
