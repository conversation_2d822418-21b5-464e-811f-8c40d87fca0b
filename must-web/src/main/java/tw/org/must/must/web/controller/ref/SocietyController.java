package tw.org.must.must.web.controller.ref;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.retry.MessageKeyGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tw.org.must.must.common.util.result.Result;
import tw.org.must.must.core.service.ref.*;
import tw.org.must.must.dto.ref.SocietyDto;
import tw.org.must.must.dto.ref.SocietyPageDto;
import tw.org.must.must.model.ref.*;

import java.util.List;
import java.util.Objects;

@Api(tags = "REF-协会管理")
@RestController
@RequestMapping("/ref/society")
public class SocietyController {

    @Autowired
    private RefSocietyService refSocietyService;

    @Autowired
    private RefSocietyRightService refSocietyRightService;

    @Autowired
    private RefSocietyCorrespService refSocietyCorrespService;

    @Autowired
    private RefSocietyContactService refSocietyContactService;

    @Autowired
    private RefCountryService refCountryService;

    @Autowired
    private RefCurrencyService refCurrencyService;

    @ApiOperation(value = "获取协会列表")
    @PostMapping("getSocietyList")
    public ResponseEntity<PageInfo<RefSociety>> getSocietyList(@RequestBody SocietyPageDto societyPageDto) {
        PageInfo<RefSociety> societiesInfo = refSocietyService.getSocietyList(societyPageDto.getPage(),
                societyPageDto.getData());
        return new ResponseEntity<PageInfo<RefSociety>>(societiesInfo, HttpStatus.OK);

    }

    @ApiOperation(value = "添加协会")
    @PostMapping("addSociety")
    public Result<String> addSociety(@RequestBody SocietyDto societyDto) {
        String message = "";
        Integer code = HttpStatus.OK.value();
        RefSociety refSociety = refSocietyService.getSocietyBySocietyCode(societyDto.getRefSociety().getSocietyCode());
        if (Objects.isNull(refSociety)) {
            Integer status = refSocietyService.addSociety(societyDto.getRefSociety(), societyDto.getRefSocietyRightList(),
                    societyDto.getRefSocietyCorrespList(), societyDto.getRefSocietyContactList());
        } else {
            message = "Society No已存在！";
            code = HttpStatus.BAD_REQUEST.value();
        }
        return new Result<>(code, message);
    }

    @ApiOperation(value = "根据协会Id查询")
    @GetMapping("getSocietyBySocietyCode")
    public Result<SocietyDto> getSocietyBySocietyCode(Integer societyCode) {
        int value = HttpStatus.OK.value();
        String message = "";
		SocietyDto societyDto = new SocietyDto();
		if (societyCode == null) {
			value = HttpStatus.BAD_REQUEST.value();
			message = "請求參數協會ID不能爲空！";
		} else {
			RefSociety refSociety = refSocietyService.getSocietyBySocietyCode(societyCode);
			List<RefSocietyRight> refSocietyRightList = refSocietyRightService.getSocietyRightByCode(societyCode);
			List<RefSocietyCorresp> refSocietyCorrespList = refSocietyCorrespService.getSocietyCorrespByCode(societyCode);
			List<RefSocietyContact> refSocietyContactList = refSocietyContactService.getSocietyContactByCode(societyCode);
            if (null != refSociety) {
                societyDto.setRefSociety(refSociety);
                //根据国家编码补全，国家名称
                String countryCode = refSociety.getCountryCode();
                if (org.apache.commons.lang3.StringUtils.isNoneBlank(countryCode)) {
                    RefCountry t_refCountry = refCountryService.getCountryByCode(countryCode);
                    if (t_refCountry != null) {
                        refSociety.setCountryName(t_refCountry.getName());
                    }
                }
                // 根据货币编码补全货币名称
                String currencyCode = refSociety.getSocietyCurrencyCode();
                if (StringUtils.isNotBlank(currencyCode)) {
                    List<RefCurrency> refCurrency = refCurrencyService.getRefCurrency(currencyCode, "");
					if (null != refCurrency && refCurrency.size() > 0) {
						RefCurrency result = refCurrency.get(0);
						if (null != result) {
							refSociety.setCurrencyName(result.getCurrencyName());
						}
					}
				}
                //getRefCurrency
                String drawnOnCountryCode = refSociety.getDrawnOnCountryCode();
                if (StringUtils.isNotBlank(drawnOnCountryCode)){
                    List<RefCurrency> refCurrency = refCurrencyService.getRefCurrency(currencyCode, "");
                    if (null != refCurrency && refCurrency.size() > 0) {
                        RefCurrency result = refCurrency.get(0);
                        if (null != result) {
                            refSociety.setDrawnOnCountry(result.getCurrencyName());
                        }
                    }
                }
				if (refSocietyContactList.size() > 0) {
					societyDto.setRefSocietyContactList(refSocietyContactList);
				}
				if (refSocietyCorrespList.size() > 0) {
					societyDto.setRefSocietyCorrespList(refSocietyCorrespList);
				}
				if (refSocietyRightList.size() > 0) {
				    refSocietyRightList.stream().forEach(refSocietyRight -> {
				        //RefCountry
                        Long affiliatedSocCode = refSocietyRight.getAffiliatedSocCode();
                        if (Objects.nonNull(affiliatedSocCode)){
                            RefSociety refSociety1 = refSocietyService.getSocietyBySocietyCode(Integer.valueOf(String.valueOf(affiliatedSocCode)));
                            if (refSociety1 != null){
                                refSocietyRight.setAffiliatedSoc(refSociety1.getSocietyName());
                            }
                        }
                    });
					societyDto.setRefSocietyRightList(refSocietyRightList);
				}
			}else {
				value = HttpStatus.BAD_REQUEST.value();
				message = String.format("查詢不到想對應的協會數據，請確認查詢協會ID[%d]的準確性！", societyCode);
			}
        }
        return new Result<>(value, message, societyDto);
    }

    @ApiOperation(value = "修改协会")
    @PostMapping("updateSociety")
    public ResponseEntity<Integer> updateSociety(@RequestBody SocietyDto societyDto) {
        Integer status = refSocietyService.updateSociety(societyDto.getRefSociety(), societyDto.getRefSocietyRightList(),
                societyDto.getRefSocietyCorrespList(), societyDto.getRefSocietyContactList());
        return new ResponseEntity<Integer>(status, HttpStatus.OK);
    }
    @ApiOperation(value = "协会删除right")
    @DeleteMapping("delRight/{id}")
    public ResponseEntity<Integer> delRight(@PathVariable("id") Long id) {
        return new ResponseEntity<>(refSocietyRightService.delete(id), HttpStatus.OK);
    }

    @ApiOperation(value = "协会删除Contact")
    @DeleteMapping("delContact/{id}")
    public ResponseEntity<Integer> updateSociety(@PathVariable("id") Long id) {
        return new ResponseEntity<>(refSocietyContactService.delete(id), HttpStatus.OK);
    }

}
