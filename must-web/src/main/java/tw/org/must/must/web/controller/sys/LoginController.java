package tw.org.must.must.web.controller.sys;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.DisabledAccountException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.core.service.sys.SysMenuService;
import tw.org.must.must.model.sys.SysMenu;
import tw.org.must.must.model.sys.SysUser;
import tw.org.must.must.model.sys.vo.UserVO;

import java.util.List;

@Api(tags = "登录")
@RestController
@RequestMapping("/")
public class LoginController {

    private final SysMenuService sysMenuService;

    public LoginController(SysMenuService sysMenuService) {
        this.sysMenuService = sysMenuService;
    }

    @ApiOperation(value = "登录")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "账户", name = "username", required = true, dataType = "String"),
            @ApiImplicitParam(value = "密码", name = "password", required = true, dataType = "String")
    })
    @PostMapping("login")
    public ResponseEntity login(@RequestParam(value = "username") String username,
                                @RequestParam(value = "password") String password) {
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            return new ResponseEntity<>(HttpStatus.OK);
        }
        UsernamePasswordToken token = new UsernamePasswordToken(username, password);
        try {
            subject.login(token);
            SecurityUtils.getSubject().getSession().setTimeout(86400*1000L); //24小时
        }catch (IncorrectCredentialsException e){
            throw new MustException(ResultCode.LOGIN_ERROR.getCode(),"密碼不正確!");
        }catch (UnknownAccountException e){
            throw new MustException(ResultCode.LOGIN_ERROR.getCode(),"用戶名不存在 !");
        }catch (DisabledAccountException e){
            throw new MustException(ResultCode.LOGIN_ERROR.getCode(),"用戶已被鎖定 !");
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "注销")
    @PostMapping("logout")
    public ResponseEntity logout() {
        Subject subject = SecurityUtils.getSubject();
        subject.logout();
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "获取当前登录用户信息及菜单信息")
    @GetMapping("index")
    public ResponseEntity index() {
        Subject subject = SecurityUtils.getSubject();
        SysUser user = (SysUser) subject.getPrincipal();
        if (user == null) {
            return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
        }
        List<SysMenu> menus = sysMenuService.getMenuTreeByUserId(user.getId());
        UserVO userVO = UserVO.build(user);
        JSONObject object = new JSONObject();
        object.put("user", userVO);
        object.put("menus", menus);
        return new ResponseEntity<>(object, HttpStatus.OK);
    }
}
