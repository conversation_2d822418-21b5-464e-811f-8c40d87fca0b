<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.5.final using JasperReports Library version 6.21.5-74d586df47b25dbd05bd0957999819196e59934a  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="880_sub" pageWidth="1000" pageHeight="842" columnWidth="1000" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="038fd05a-867a-4622-80a5-806fcdf113a9">
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="paNameNo" class="java.lang.String"/>
	<field name="paName" class="java.lang.String"/>
	<field name="bankName" class="java.lang.String"/>
	<field name="detailList" class="java.lang.Object"/>
	<field name="bankCode" class="java.lang.String"/>
	<field name="branchNo" class="java.lang.String"/>
	<field name="accountNumber" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<detail>
		<band height="20" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="90" height="18" uuid="68688c34-98ea-4b61-8493-0153a03fc93a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{paNameNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="100" y="0" width="140" height="18" uuid="********-d6f0-4661-b5ec-3f6159fa34cf">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{paName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="580" y="0" width="150" height="18" uuid="9bfb486b-c715-4c95-99c2-242ccb397525">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bankName}]]></textFieldExpression>
			</textField>
			<subreport>
				<reportElement x="240" y="0" width="340" height="18" uuid="aae131d4-9aba-463d-b423-dc99202ecc50">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("detailList")]]></dataSourceExpression>
				<subreportExpression><![CDATA[Thread.currentThread().getContextClassLoader().getResource("ireport/jrxml/distAutoPays/DistAutoPay750_child.jasper")]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement x="740" y="0" width="70" height="18" uuid="7565babd-c4ba-47ea-bb0a-11df4529136d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{bankCode}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="810" y="0" width="87" height="18" uuid="dc0a8017-b683-4182-9cc7-31710d53a97c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{branchNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="900" y="0" width="100" height="18" uuid="751be489-3851-4d64-8651-8ab895956633">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$F{accountNumber}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
