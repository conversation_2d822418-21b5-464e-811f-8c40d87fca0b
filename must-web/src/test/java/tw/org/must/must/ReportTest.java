package tw.org.must.must;


import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.core.task.dist.DistGenerateTypePTODO;
import tw.org.must.must.core.task.report.DistReportAllTask;

import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ReportTest {

    @Autowired
    private DistReportAllTask distReportAllTask;

    @Autowired
    private DistGenerateTypePTODO distGenerateTypePTODO ;

    @Test
    public void testGenerateDistSummary(){
        distReportAllTask.generateDistSummary("E:\\Test","O221");
    }

    @Test
    public void testGenrateTypeP(){
        distGenerateTypePTODO.genrateTypeP("F:\\Test","O221");
    }

    @Test
    public void testGenrateTypePSoc(){
        distGenerateTypePTODO.genrateTypePSoc("F:\\Test","P203");
    }

    @Test
    public void testtestGenrateCRD(){
        distReportAllTask.generateCrd("F:\\Test\\20231020","P203");
    }

    @Test
    public void testDateParse(){
        String string = DateParse.format(new Date(), "yyMM");
        System.out.println(string);
    }

    @Test
    public void testGeneratePMemberPDF(){
        distReportAllTask.init();
        distReportAllTask.generatePMemberPDF("F:\\Test\\20231208","O221");
    }

    @Test
    public void generatePSocietyPDF(){
        distReportAllTask.init();
        distReportAllTask.generatePSocietyPDF("F:\\Test\\20231208","P203");
    }

    @Test
    public void generategenerateDistSummary(){
        distReportAllTask.init();
        distReportAllTask.generateDistSummary("F:\\Test\\20231208","P203");
    }

    @Test
    public void testGenerateCrossCheck(){
        distReportAllTask.init();
        distReportAllTask.generateCrossCheck("F:\\Test\\20231208","O221");
    }

    @Test
    public void generateIpNotInWork() {
        distReportAllTask.generateIpNotInWork("F:\\Test\\20231208","O221");

    }

    @Test
    public void generateAmountCheck() {
        distReportAllTask.generateAmountCheck("F:\\Test\\20231208","O221");

    }

}
