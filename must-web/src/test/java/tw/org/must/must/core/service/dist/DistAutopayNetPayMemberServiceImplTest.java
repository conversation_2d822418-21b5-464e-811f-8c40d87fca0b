package tw.org.must.must.core.service.dist;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tw.org.must.must.MustWebApplication;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import net.sf.jasperreports.engine.JRException;

/**
 * 测试 DistAutopayNetPayMemberService 的货币格式化功能
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MustWebApplication.class)
public class DistAutopayNetPayMemberServiceImplTest {

    @Autowired
    private DistAutopayNetPayMemberService distAutopayNetPayMemberService;

    @Test
    public void testReportDistAutoPay780WithCurrencyFormatting() {
        try {
            // 这里可以使用一个测试的 autopayNo
            String testAutopayNo = "TEST123";
            Integer testSocietyCode = 52;
            String testDistNo = "O221";
            Boolean bankInfo = true;
            
            // 注意：这个测试需要一个模拟的 HttpServletResponse
            // 在实际测试中，你可能需要创建一个 MockHttpServletResponse
            System.out.println("测试 780 报表货币格式化功能");
            System.out.println("autopayNo: " + testAutopayNo);
            System.out.println("societyCode: " + testSocietyCode);
            System.out.println("distNo: " + testDistNo);
            System.out.println("bankInfo: " + bankInfo);
            
            // 实际的测试需要有效的数据和 HttpServletResponse
            // distAutopayNetPayMemberService.reportDistAutoPay780(response, testAutopayNo, bankInfo, testSocietyCode, testDistNo);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
