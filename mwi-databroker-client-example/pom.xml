<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<parent>
		<groupId>net.fasttrackdcn</groupId>
		<artifactId>release</artifactId>
		<version>4.7.0.0</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<groupId>fr.sacem.international</groupId>
	<artifactId>mwi-databroker-client-example</artifactId>
	<name>MWI DataBroker Client Example</name>
	<packaging>jar</packaging>
	<properties>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
	</properties>

	<dependencies>
		<dependency>
			<groupId>fr.sacem.international.common.client</groupId>
			<artifactId>common-client</artifactId>
		</dependency>
		<dependency>
			<groupId>fr.sacem.international.locator.client</groupId>
			<artifactId>locator-client</artifactId>
		</dependency>
		<dependency>
			<groupId>fr.sacem.international.common.mwi.query.utils</groupId>
			<artifactId>common-mwi-query-utils</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.xml.bind</groupId>
			<artifactId>jaxb-api</artifactId>
			<version>2.3.0</version>
		</dependency>
		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-core</artifactId>
			<version>2.3.0</version>
		</dependency>
		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-impl</artifactId>
			<version>2.3.0</version>
		</dependency>
		<dependency>
			<groupId>javax.activation</groupId>
			<artifactId>activation</artifactId>
			<version>1.1.1</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/javax.xml.ws/jaxws-api -->
		<dependency>
			<groupId>javax.xml.ws</groupId>
			<artifactId>jaxws-api</artifactId>
			<version>2.3.0</version>
		</dependency>

		<dependency>
			<groupId>com.sun.xml.ws</groupId>
			<artifactId>jaxws-ri</artifactId>
			<version>2.3.0</version>
			<type>pom</type>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<executions>
					<execution>
						<id>dist</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<finalName>${project.artifactId}-${project.version}</finalName>
							<descriptors>
								<descriptor>src/main/assembly/descriptor.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
		
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
			</resource>
		</resources>
	</build>

</project>
