<assembly>
	<id>dist</id>

	<formats>
		<format>zip</format>
	</formats>

    <baseDirectory>${project.artifactId}-${next.released.version}</baseDirectory>
	<includeBaseDirectory>true</includeBaseDirectory>

	<fileSets>
		<fileSet>
			<directory>src/main/bin/</directory>
			<filtered>true</filtered>
			<outputDirectory>/bin</outputDirectory>
			<lineEnding>dos</lineEnding>
			<includes>
				<include>*.cmd</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>src/main/bin/</directory>
			<filtered>true</filtered>
			<outputDirectory>/bin</outputDirectory>
			<lineEnding>unix</lineEnding>
			<includes>
				<include>*.sh</include>
			</includes>
			<fileMode>0755</fileMode>
			<directoryMode>0755</directoryMode>
		</fileSet>
		<fileSet>
			<directory>src/main/resources</directory>
			<filtered>true</filtered>
			<includes>
				<include>log4j.xml</include>
			</includes>
			<outputDirectory>/config</outputDirectory>
			<fileMode>0644</fileMode>
			<directoryMode>0755</directoryMode>
		</fileSet>
	</fileSets>

	<dependencySets>
		<dependencySet>
			<outputDirectory>/lib</outputDirectory>
			<unpack>false</unpack>
			<scope>runtime</scope>
			<includes>
				<include>*:jar</include>
			</includes>
		</dependencySet>
	</dependencySets>

</assembly>
