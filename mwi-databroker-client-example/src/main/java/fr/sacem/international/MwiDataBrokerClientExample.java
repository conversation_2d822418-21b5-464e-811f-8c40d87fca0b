package fr.sacem.international;

import fr.sacem.international.locator.client.factories.ServiceFactory;
import net.fasttrackdcn.common.locator.Location;
import net.fasttrackdcn.common.mwi.MwiWork;
import net.fasttrackdcn.common.mwi.WorkNaturalKey;
import net.fasttrackdcn.common.query.*;
import net.fasttrackdcn.common.query.result.PageResult;
import net.fasttrackdcn.common.query.result.Result;
import net.fasttrackdcn.locator.LocatorServiceException;
import net.fasttrackdcn.mwi.databroker.MwiDataBrokerExecutionException;
import net.fasttrackdcn.mwi.databroker.MwiDataBrokerService;
import net.fasttrackdcn.mwi.databroker.WorkNotFoundException;
import net.fasttrackdcn.standards.SocietyCode;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class MwiDataBrokerClientExample {

    private static ApplicationContext appContext = new ClassPathXmlApplicationContext(new String[] { "mwi-clients-context.xml" });

    private static ServiceFactory<MwiDataBrokerService> mwiDataBrokerServiceFactory;

    @SuppressWarnings("unchecked")
    public static void main(String[] args) throws MissingCriterionParamException, MissingMainParamException, CriterionNotFoundException, QueryExecutionException, QueryTimeoutException, MwiDataBrokerExecutionException, LocatorServiceException, WorkNotFoundException {

        mwiDataBrokerServiceFactory = (ServiceFactory<MwiDataBrokerService>) appContext.getBean("mwiDataBrokerServiceFactory");

        Query query = new Query();
        List<Criterion> criteria = new ArrayList<Criterion>();
        Criterion criterion1 = new Criterion();
        Criterion criterion2 = new Criterion();

        criterion1.setId("TITLE_BEGINS");
        Param param1 = new Param();
        param1.setId("TITLE");
        param1.setValue("BLUE");
        List<Param> params1 = new ArrayList<Param>();
        params1.add(param1);
        criterion1.setParams(params1);
        criteria.add(criterion1);

        criterion2.setId("CATEGORY");
        Param param2 = new Param();
        param2.setId("CATEGORY");
        param2.setValue("DOM");
        List<Param> params2 = new ArrayList<Param>();
        params2.add(param2);
        criterion2.setParams(params2);
        criteria.add(criterion2);

        query.setCriteria(criteria);

        Paging paging = new Paging();
        paging.setPageNumber(1);
        paging.setRowsPerPage(5);
        query.setPaging(paging);

        query.setTimeout(15);

        SocietyCode sourceDatabase = new SocietyCode("080");
        Location sacemLocation = new Location("080");

        for (int i = 1; i < 10; i++) {
            paging.setPageNumber(i);
            query.setPaging(paging);

            PageResult<WorkNaturalKey> response = mwiDataBrokerServiceFactory.getService(sacemLocation).findWorks(query, sourceDatabase);
            if (response == null || response.getNumberOfResults() == 0) {
                System.out.println("Work not found");
            } else {
                System.out.println("Found " + response.getNumberOfResults() + " works");
                MwiDataBrokerService service = mwiDataBrokerServiceFactory.getService(sacemLocation);
                Set<WorkNaturalKey> workList = new HashSet<>();
                List<WorkNaturalKey> collect = response.getResults().stream().map(Result::getResultElement).collect(Collectors.toList());
                workList.addAll(collect);
                Set<MwiWork> workDetails = service.getWorkDetails(workList);
                workDetails.forEach(detail -> System.out.println("workDetial :  " + detail.getWorkNaturalKey() + "  " + detail.getWorkTitles().toString() + "  "));
                System.out.println();
                System.out.println();
            }

        }
        System.exit(-1);

    }
}
